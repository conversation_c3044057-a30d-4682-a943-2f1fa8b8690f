﻿namespace PremierAutoTag.Framework.Core
{
    public class PaginationStripModel : ObservableBase
    {
        private int _currentIndex;

        public int CurrentIndex
        {
            get { return _currentIndex; }
            set
            {
                _currentIndex = value;
                NotifyPropertyChanged();
            }
        }


        private int _rowsPerPage;

        public int RowsPerPage
        {
            get { return _rowsPerPage; }
            set
            {
                _rowsPerPage = value;
                NotifyPropertyChanged();
            }
        }

        /// <summary>
        /// Sets Current Index to 1 without raising notify property change event
        /// </summary>
        public void ResetCurrentIndex()
        {
            _currentIndex = 1;
        }

    }
}
