﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.EntityFrameworkCore;
using PremierAutoTag.Server.Data.DB;
using System.Security.Claims;

namespace PremierAutoTag.Web
{
    public class CustomClaimsTransformation : IClaimsTransformation
    {
        private readonly ApplicationDbContext _context;

        public CustomClaimsTransformation(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
        {
            if (!(principal.Identity?.IsAuthenticated).GetValueOrDefault())
            {
                return principal;
            }

            var userId = principal.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;

            var clientprofileQuery = (from p in _context.Profiles
                                      from u in _context.Users.Where(x => x.Id == p.Id)
                                      where p.Id == userId
                                      select new
                                      {
                                          u.UserName,
                                          p.FirstName,p.LastName,
                                          ProfileId = p.Id, 
                                          PhoneNumber = u.PhoneNumber ,
                                          Email = u.Email
                                      }
                               );

            var clientprofile = await clientprofileQuery.FirstOrDefaultAsync();
            if (clientprofile is null)
            {
                return await Task.FromResult(principal);
            }

            var existingIdentity = principal.Identity as ClaimsIdentity;
            if (existingIdentity != null)
            {
                var existingNameClaim = existingIdentity.FindFirst(ClaimTypes.Name);
                if (existingNameClaim != null)
                {
                    existingIdentity.RemoveClaim(existingNameClaim);
                }
                existingIdentity.AddClaim(new Claim("ProfileName", $"{clientprofile.FirstName} {clientprofile.LastName}"));
                existingIdentity.AddClaim(new Claim("Phone", clientprofile.PhoneNumber));
            }

            return await Task.FromResult(principal);
        }
    }

}
