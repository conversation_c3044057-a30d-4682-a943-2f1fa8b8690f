﻿@page "/"
@using PremierAutoTag.Razor.Features.ContactUs.Form
@using PremierAutoTag.Razor.Layout
@layout WebsiteLayout

<PageTitle>Home</PageTitle>

<section class="bg-gray-m-800">
    <div class="pt-24 md:pt-44 pb-8 md:pb-16 container mx-auto">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-m-25 text-center mb-16">Skip The Line &amp; Hassle</h1>

        <div class="grid grid-cols-12 gap-4 md:gap-10 px-4 md:px-auto">
            <div class="col-span-12 md:col-span-6">
                <div class="mb-4 md:mb-10">
                    <p class="text-3xl md:text-5xl mb-4 md:leading-14 text-gray-m-25">
                        Express DMV Auto <br>
                        Registration &amp; Titles
                    </p>
                    <p class="text-gray-m-200 text-base md:text-xl">
                        Premier Auto Tag Services offers express DMV
                        title and
                        registration for
                        individuals and
                        specialized services for dealerships. We now provide in-person TSA PreCheck registration in
                        Norwalk.
                    </p>
                </div>
                <div>
                    <div class="flex items-center mb-5">
                        <div class="flex-grow bg bg-gray-m-500 light:bg-black bg-opacity-30 h-[1px]"></div>
                        <div class="flex-grow-0 mx-5 text-base font-semibold text-center text-white light:text-black">
                            Express DMV Services for
                        </div>
                        <div class="flex-grow bg bg-gray-m-500 light:bg-black bg-opacity-30 h-[1px]"></div>
                    </div>
                    <div class="flex w-full md:flex-row flex-col gap-4">
                        <div class="bg-white flex justify-center w-full flex-col gap-2 p-6 items-center rounded-3xl">
                            <img src="images/individualIcon.svg">
                            <h2 class="text-gray-800 font-semibold text-xl  md:text-2xl">Individuals</h2>
                        </div>
                        <div class="bg-white flex justify-center w-full flex-col gap-2 p-6 items-center rounded-3xl">
                            <img src="images/dealershipIcon.svg">
                            <h2 class="text-gray-800 font-semibold text-xl md:text-2xl">Dealership</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-12 md:col-span-6 md:h-[360px]">
                <div class="swiper mySwiper rounded-4xl">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <img src="images/c1.png" alt="AutoTag Service 1" class="w-full h-full object-cover rounded-4xl">
                        </div>
                        <div class="swiper-slide">
                            <img src="images/c1.png" alt="AutoTag Service 2" class="w-full h-full object-cover rounded-4xl">
                        </div>
                        <div class="swiper-slide">
                            <img src="images/c1.png" alt="AutoTag Service 3" class="w-full h-full object-cover rounded-4xl">
                        </div>
                        <div class="swiper-slide">
                            <img src="images/c1.png" alt="AutoTag Service 4" class="w-full h-full object-cover rounded-4xl">
                        </div>
                    </div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="md:py-24 pb-0 py-12 bg-white">
    <div class="container mx-auto">
        <div class="grid grid-cols-12 space-y-8 md:space-y-20 px-4 md:px-auto">
            <div class="col-span-12 md:col-span-6 md:order-1">
                <img src="images/registration.svg" class=" max-h-96" alt="">
            </div>
            <div class="col-span-12 md:col-span-6 flex items-start justify-center  flex-col gap-3 md:gap-6 md:order-2">
                <div>
                    <h2 class="text-gray-m-800 font-semibold text-xl md:text-[42px] mb-0 md:mb-3">
                        Registrations &amp;
                        Titles
                    </h2>
                    <span class="text-sm md:text-xl text-neutral-800">
                        Register your newly purchased car or your
                        existing one if
                        it
                        was registered in another
                        state
                    </span>
                </div>
                <button class="w-fit px-3 md:px-5  py-2 md:py-2.5 flex items-center gap-2 justify-center rounded-lg bg-primary cursor-pointer group hover:bg-primaryDark text-white text-sm md:text-base font-medium">
                    Get this service <span class="group-hover:transition duration-300 group-hover:translate-x-1">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.913 8.45783C15.9995 8.23906 16.0222 7.99833 15.978 7.76609C15.9339 7.53386 15.8249 7.32055 15.665 7.15317L11.0933 2.36541C10.9878 2.25109 10.8617 2.15991 10.7223 2.09718C10.5828 2.03445 10.4329 2.00143 10.2811 2.00005C10.1294 1.99866 9.97885 2.02895 9.83839 2.08913C9.69793 2.14931 9.57032 2.23819 9.463 2.35057C9.35569 2.46296 9.27083 2.5966 9.21336 2.7437C9.15589 2.8908 9.12697 3.04841 9.12829 3.20734C9.12961 3.36627 9.16114 3.52333 9.22104 3.66936C9.28094 3.81539 9.36801 3.94747 9.47717 4.05788L12.0991 6.80366H1.14293C0.839806 6.80366 0.549097 6.92977 0.334756 7.15423C0.120416 7.3787 0 7.68315 0 8.0006C0 8.31805 0.120416 8.62249 0.334756 8.84696C0.549097 9.07143 0.839806 9.19754 1.14293 9.19754H12.0991L9.47831 11.9421C9.36915 12.0525 9.28208 12.1846 9.22218 12.3306C9.16228 12.4767 9.13075 12.6337 9.12943 12.7927C9.12811 12.9516 9.15703 13.1092 9.2145 13.2563C9.27197 13.4034 9.35683 13.537 9.46415 13.6494C9.57146 13.7618 9.69907 13.8507 9.83953 13.9109C9.97999 13.9711 10.1305 14.0013 10.2823 14C10.434 13.9986 10.584 13.9656 10.7234 13.9028C10.8629 13.8401 10.989 13.7489 11.0944 13.6346L15.6661 8.84683C15.772 8.7354 15.8559 8.60322 15.913 8.45783V8.45783Z" fill="white"></path>
                        </svg>
                    </span>
                </button>
            </div>


            <div class="col-span-12 md:col-span-6 flex items-start justify-center flex-col gap-3 md:gap-6 md:pe-20 md:order-3 order-4">
                <div>
                    <h2 class="text-gray-m-800 font-semibold text-xl md:text-[42px] mb-0 md:mb-3">Tag Transfers</h2>
                    <span class="text-sm md:text-xl text-neutral-800">
                        Transfer your license plate from an existing
                        vehicle to
                        your new one&nbsp;
                    </span>
                </div>
                <button class="w-fit px-3 md:px-5  py-2 md:py-2.5 flex items-center gap-2 justify-center rounded-lg bg-primary cursor-pointer group hover:bg-primaryDark text-white text-sm md:text-base font-medium">
                    Get this service <span class="group-hover:transition duration-300 group-hover:translate-x-1">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.913 8.45783C15.9995 8.23906 16.0222 7.99833 15.978 7.76609C15.9339 7.53386 15.8249 7.32055 15.665 7.15317L11.0933 2.36541C10.9878 2.25109 10.8617 2.15991 10.7223 2.09718C10.5828 2.03445 10.4329 2.00143 10.2811 2.00005C10.1294 1.99866 9.97885 2.02895 9.83839 2.08913C9.69793 2.14931 9.57032 2.23819 9.463 2.35057C9.35569 2.46296 9.27083 2.5966 9.21336 2.7437C9.15589 2.8908 9.12697 3.04841 9.12829 3.20734C9.12961 3.36627 9.16114 3.52333 9.22104 3.66936C9.28094 3.81539 9.36801 3.94747 9.47717 4.05788L12.0991 6.80366H1.14293C0.839806 6.80366 0.549097 6.92977 0.334756 7.15423C0.120416 7.3787 0 7.68315 0 8.0006C0 8.31805 0.120416 8.62249 0.334756 8.84696C0.549097 9.07143 0.839806 9.19754 1.14293 9.19754H12.0991L9.47831 11.9421C9.36915 12.0525 9.28208 12.1846 9.22218 12.3306C9.16228 12.4767 9.13075 12.6337 9.12943 12.7927C9.12811 12.9516 9.15703 13.1092 9.2145 13.2563C9.27197 13.4034 9.35683 13.537 9.46415 13.6494C9.57146 13.7618 9.69907 13.8507 9.83953 13.9109C9.97999 13.9711 10.1305 14.0013 10.2823 14C10.434 13.9986 10.584 13.9656 10.7234 13.9028C10.8629 13.8401 10.989 13.7489 11.0944 13.6346L15.6661 8.84683C15.772 8.7354 15.8559 8.60322 15.913 8.45783V8.45783Z" fill="white"></path>
                        </svg>
                    </span>
                </button>
            </div>
            <div class="col-span-12 md:col-span-6 flex justify-end md:order-4 order-3">
                <img src="images/illu2.svg" class=" max-h-96" alt="">
            </div>


            <!-- 3rd -->

            <div class="col-span-12 md:col-span-6 flex justify-center md:justify-start md:order-5">
                <img src="images/illu3.svg" class="max-h-40 md:max-h-96 border border-gray-200 rounded-lg" alt="">
            </div>
            <div class="col-span-12 md:col-span-6 flex items-start justify-center flex-col gap-3 md:gap-6 md:order-6">
                <div>
                    <h2 class="text-gray-m-800 font-semibold text-xl md:text-[42px] mb-0 md:mb-3">
                        Duplicates &amp;
                        Replacements
                    </h2>
                    <span class="text-sm md:text-xl text-neutral-800">
                        Get duplicate registration documentation or
                        replace a
                        lost one
                    </span>
                </div>
                <button class="w-fit px-3 md:px-5  py-2 md:py-2.5 flex items-center gap-2 justify-center rounded-lg bg-primary cursor-pointer group hover:bg-primaryDark text-white text-sm md:text-base font-medium">
                    Get this service <span class="group-hover:transition duration-300 group-hover:translate-x-1">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.913 8.45783C15.9995 8.23906 16.0222 7.99833 15.978 7.76609C15.9339 7.53386 15.8249 7.32055 15.665 7.15317L11.0933 2.36541C10.9878 2.25109 10.8617 2.15991 10.7223 2.09718C10.5828 2.03445 10.4329 2.00143 10.2811 2.00005C10.1294 1.99866 9.97885 2.02895 9.83839 2.08913C9.69793 2.14931 9.57032 2.23819 9.463 2.35057C9.35569 2.46296 9.27083 2.5966 9.21336 2.7437C9.15589 2.8908 9.12697 3.04841 9.12829 3.20734C9.12961 3.36627 9.16114 3.52333 9.22104 3.66936C9.28094 3.81539 9.36801 3.94747 9.47717 4.05788L12.0991 6.80366H1.14293C0.839806 6.80366 0.549097 6.92977 0.334756 7.15423C0.120416 7.3787 0 7.68315 0 8.0006C0 8.31805 0.120416 8.62249 0.334756 8.84696C0.549097 9.07143 0.839806 9.19754 1.14293 9.19754H12.0991L9.47831 11.9421C9.36915 12.0525 9.28208 12.1846 9.22218 12.3306C9.16228 12.4767 9.13075 12.6337 9.12943 12.7927C9.12811 12.9516 9.15703 13.1092 9.2145 13.2563C9.27197 13.4034 9.35683 13.537 9.46415 13.6494C9.57146 13.7618 9.69907 13.8507 9.83953 13.9109C9.97999 13.9711 10.1305 14.0013 10.2823 14C10.434 13.9986 10.584 13.9656 10.7234 13.9028C10.8629 13.8401 10.989 13.7489 11.0944 13.6346L15.6661 8.84683C15.772 8.7354 15.8559 8.60322 15.913 8.45783V8.45783Z" fill="white"></path>
                        </svg>
                    </span>
                </button>
            </div>

            <!-- 4th -->


            <div class="col-span-12 md:col-span-6 flex items-start justify-center flex-col gap-3 md:gap-6 md:pe-20 md:order-7 order-8">
                <div>
                    <h2 class="text-gray-m-800 font-semibold text-xl md:text-[42px] mb-0 md:mb-3">
                        Classic, Vanity &amp;
                        Custom Plates
                    </h2>
                    <span class="text-sm md:text-xl text-neutral-800">
                        Get a unique license plate number or get a
                        classic plate
                        for your vintage car
                    </span>
                </div>
                <button class="w-fit px-3 md:px-5  py-2 md:py-2.5 flex items-center gap-2 justify-center rounded-lg bg-primary cursor-pointer group hover:bg-primaryDark text-white text-sm md:text-base font-medium">
                    Get this service <span class="group-hover:transition duration-300 group-hover:translate-x-1">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.913 8.45783C15.9995 8.23906 16.0222 7.99833 15.978 7.76609C15.9339 7.53386 15.8249 7.32055 15.665 7.15317L11.0933 2.36541C10.9878 2.25109 10.8617 2.15991 10.7223 2.09718C10.5828 2.03445 10.4329 2.00143 10.2811 2.00005C10.1294 1.99866 9.97885 2.02895 9.83839 2.08913C9.69793 2.14931 9.57032 2.23819 9.463 2.35057C9.35569 2.46296 9.27083 2.5966 9.21336 2.7437C9.15589 2.8908 9.12697 3.04841 9.12829 3.20734C9.12961 3.36627 9.16114 3.52333 9.22104 3.66936C9.28094 3.81539 9.36801 3.94747 9.47717 4.05788L12.0991 6.80366H1.14293C0.839806 6.80366 0.549097 6.92977 0.334756 7.15423C0.120416 7.3787 0 7.68315 0 8.0006C0 8.31805 0.120416 8.62249 0.334756 8.84696C0.549097 9.07143 0.839806 9.19754 1.14293 9.19754H12.0991L9.47831 11.9421C9.36915 12.0525 9.28208 12.1846 9.22218 12.3306C9.16228 12.4767 9.13075 12.6337 9.12943 12.7927C9.12811 12.9516 9.15703 13.1092 9.2145 13.2563C9.27197 13.4034 9.35683 13.537 9.46415 13.6494C9.57146 13.7618 9.69907 13.8507 9.83953 13.9109C9.97999 13.9711 10.1305 14.0013 10.2823 14C10.434 13.9986 10.584 13.9656 10.7234 13.9028C10.8629 13.8401 10.989 13.7489 11.0944 13.6346L15.6661 8.84683C15.772 8.7354 15.8559 8.60322 15.913 8.45783V8.45783Z" fill="white"></path>
                        </svg>
                    </span>
                </button>
            </div>
            <div class="col-span-12 md:col-span-6 flex justify-center md:justify-end md:order-8 order-7">
                <img src="images/illu4.svg" class="max-h-40 md:max-h-96 border border-gray-200 rounded-lg" alt="">
            </div>



            <!-- 5rd -->

            <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex md:order-9 order-9">
                <img src="images/illu5.svg" class="max-h-40 md:max-h-96 border border-gray-200 rounded-lg" alt="">
            </div>
            <div class="col-span-12 md:col-span-6 flex items-start justify-center flex-col gap-3 md:gap-6 md:order-10 order-10">
                <div>
                    <h2 class="text-gray-m-800 font-semibold text-xl md:text-[42px] mb-0 md:mb-3">
                        Title Corrections
                    </h2>
                    <span class="text-sm md:text-xl text-neutral-800">Correct title and registrations</span>
                </div>
                <button class="w-fit px-3 md:px-5  py-2 md:py-2.5 flex items-center gap-2 justify-center rounded-lg bg-primary cursor-pointer group hover:bg-primaryDark text-white text-sm md:text-base font-medium">
                    Get this service <span class="group-hover:transition duration-300 group-hover:translate-x-1">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.913 8.45783C15.9995 8.23906 16.0222 7.99833 15.978 7.76609C15.9339 7.53386 15.8249 7.32055 15.665 7.15317L11.0933 2.36541C10.9878 2.25109 10.8617 2.15991 10.7223 2.09718C10.5828 2.03445 10.4329 2.00143 10.2811 2.00005C10.1294 1.99866 9.97885 2.02895 9.83839 2.08913C9.69793 2.14931 9.57032 2.23819 9.463 2.35057C9.35569 2.46296 9.27083 2.5966 9.21336 2.7437C9.15589 2.8908 9.12697 3.04841 9.12829 3.20734C9.12961 3.36627 9.16114 3.52333 9.22104 3.66936C9.28094 3.81539 9.36801 3.94747 9.47717 4.05788L12.0991 6.80366H1.14293C0.839806 6.80366 0.549097 6.92977 0.334756 7.15423C0.120416 7.3787 0 7.68315 0 8.0006C0 8.31805 0.120416 8.62249 0.334756 8.84696C0.549097 9.07143 0.839806 9.19754 1.14293 9.19754H12.0991L9.47831 11.9421C9.36915 12.0525 9.28208 12.1846 9.22218 12.3306C9.16228 12.4767 9.13075 12.6337 9.12943 12.7927C9.12811 12.9516 9.15703 13.1092 9.2145 13.2563C9.27197 13.4034 9.35683 13.537 9.46415 13.6494C9.57146 13.7618 9.69907 13.8507 9.83953 13.9109C9.97999 13.9711 10.1305 14.0013 10.2823 14C10.434 13.9986 10.584 13.9656 10.7234 13.9028C10.8629 13.8401 10.989 13.7489 11.0944 13.6346L15.6661 8.84683C15.772 8.7354 15.8559 8.60322 15.913 8.45783V8.45783Z" fill="white"></path>
                        </svg>
                    </span>
                </button>
            </div>


            <!-- 6th -->


            <div class="col-span-12 md:col-span-6 flex items-start justify-center flex-col gap-3 md:gap-6 md:pe-20 md:order-11 order-12">
                <div>
                    <h2 class="text-gray-m-800 font-semibold text-xl md:text-[42px] mb-0 md:mb-3">Plate Returns</h2>
                    <span class="text-sm md:text-xl text-neutral-800">
                        Return your license plate to DMV thru
                        us
                    </span>
                </div>
                <button class="w-fit px-3 md:px-5  py-2 md:py-2.5 flex items-center gap-2 justify-center rounded-lg bg-primary cursor-pointer group hover:bg-primaryDark text-white text-sm md:text-base font-medium">
                    Get this service <span class="group-hover:transition duration-300 group-hover:translate-x-1">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.913 8.45783C15.9995 8.23906 16.0222 7.99833 15.978 7.76609C15.9339 7.53386 15.8249 7.32055 15.665 7.15317L11.0933 2.36541C10.9878 2.25109 10.8617 2.15991 10.7223 2.09718C10.5828 2.03445 10.4329 2.00143 10.2811 2.00005C10.1294 1.99866 9.97885 2.02895 9.83839 2.08913C9.69793 2.14931 9.57032 2.23819 9.463 2.35057C9.35569 2.46296 9.27083 2.5966 9.21336 2.7437C9.15589 2.8908 9.12697 3.04841 9.12829 3.20734C9.12961 3.36627 9.16114 3.52333 9.22104 3.66936C9.28094 3.81539 9.36801 3.94747 9.47717 4.05788L12.0991 6.80366H1.14293C0.839806 6.80366 0.549097 6.92977 0.334756 7.15423C0.120416 7.3787 0 7.68315 0 8.0006C0 8.31805 0.120416 8.62249 0.334756 8.84696C0.549097 9.07143 0.839806 9.19754 1.14293 9.19754H12.0991L9.47831 11.9421C9.36915 12.0525 9.28208 12.1846 9.22218 12.3306C9.16228 12.4767 9.13075 12.6337 9.12943 12.7927C9.12811 12.9516 9.15703 13.1092 9.2145 13.2563C9.27197 13.4034 9.35683 13.537 9.46415 13.6494C9.57146 13.7618 9.69907 13.8507 9.83953 13.9109C9.97999 13.9711 10.1305 14.0013 10.2823 14C10.434 13.9986 10.584 13.9656 10.7234 13.9028C10.8629 13.8401 10.989 13.7489 11.0944 13.6346L15.6661 8.84683C15.772 8.7354 15.8559 8.60322 15.913 8.45783V8.45783Z" fill="white"></path>
                        </svg>
                    </span>
                </button>
            </div>
            <div class="col-span-12 md:col-span-6 flex justify-center  md:justify-end md:order-12 order-11">
                <img src="images/illu6.svg" class="max-h-40 md:max-h-96 border border-gray-200 rounded-lg" alt="">
            </div>
        </div>
    </div>
</section>
<section class="bg-gray-m-100 py-16">
    <div class="container mx-auto">
        <ContactFormComponent />
    </div>
</section>