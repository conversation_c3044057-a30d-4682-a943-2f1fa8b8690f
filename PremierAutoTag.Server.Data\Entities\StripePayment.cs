using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Server.Data.Entities
{
    [Index(nameof(Id))]
    public class StripePayment : BaseEntity
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(255)]
        public string StripePaymentIntentId { get; set; } = "";

        [Required]
        [StringLength(255)]
        public string StripeCustomerId { get; set; } = "";

        [StringLength(255)]
        public string? StripeSubscriptionId { get; set; }

        [Required]
        public long SubscriptionPlanId { get; set; }

        [Required]
        [StringLength(450)]
        public string UserId { get; set; } = "";

        [Required]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(3)]
        public string Currency { get; set; } = "USD";

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "";

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(2000)]
        public string? StripeMetadata { get; set; }

        public DateTime? PaymentCompletedAt { get; set; }

        public DateTime? PaymentFailedAt { get; set; }

        [StringLength(1000)]
        public string? FailureReason { get; set; }

        // Foreign key for UserSubscription relationship
        public long? UserSubscriptionId { get; set; }

        // Navigation properties
        public virtual SubscriptionPlan SubscriptionPlan { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual UserSubscription? UserSubscription { get; set; }
        public virtual ICollection<StripeWebhookEvent> WebhookEvents { get; set; } = new List<StripeWebhookEvent>();
    }
}
