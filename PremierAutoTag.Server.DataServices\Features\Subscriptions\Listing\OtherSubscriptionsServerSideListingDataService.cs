﻿using Microsoft.EntityFrameworkCore;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.Features.OtherSubscriptions;

namespace PremierAutoTag.Server.DataServices.Features.OtherSubscriptions;
public class OtherSubscriptionsServerSideListingDataService : ServerSideListingDataService<OtherSubscriptionsListingBusinessObject, OtherSubscriptionsFilterBusinessObject>, IOtherSubscriptionsListingDataService
{
	private readonly ApplicationDbContext _context;

	public OtherSubscriptionsServerSideListingDataService(ApplicationDbContext context)
	{
		_context = context;
	}

	public override IQueryable<OtherSubscriptionsListingBusinessObject> GetQuery(OtherSubscriptionsFilterBusinessObject filterBusinessObject)
	{
		// Get all active subscription plans
		var query = _context.SubscriptionPlans
			.Where(sp => !sp.IsDelete)
			.Select(sp => new OtherSubscriptionsListingBusinessObject
			{
				Id = sp.Id,
				Name = sp.Name,
				Description = sp.Description,
				AnnualPrice = sp.AnnualPrice,
				MaxVehicles = sp.MaxVehicles,
				MaxUsers = sp.MaxUsers,
				StandAloneQuotesDiscountPercent = sp.StandAloneQuotesDiscountPercent,
				RegistrationTitleServicesDiscountPercent = sp.RegistrationTitleServicesDiscountPercent,
				HasDirectSubmission = sp.HasDirectSubmission,
				HasDedicatedClientManager = sp.HasDedicatedClientManager,
				Features = sp.Features,
				IsPopular = sp.IsPopular,
				DisplayOrder = sp.DisplayOrder,
				RequiresPayment = sp.RequiresPayment
			});

		// Filter out the user's current subscription plan if UserId is provided
		if (!string.IsNullOrEmpty(filterBusinessObject.UserId))
		{
			var userCurrentPlanId = _context.UserSubscriptions
				.Where(us => us.UserId == filterBusinessObject.UserId && !us.IsDelete && us.Status == "Active")
				.Select(us => us.SubscriptionPlanId)
				.FirstOrDefault();

			if (userCurrentPlanId > 0)
			{
				query = query.Where(sp => sp.Id != userCurrentPlanId);
			}
		}

		// Apply additional filters
		if (filterBusinessObject.IsActive.HasValue && filterBusinessObject.IsActive.Value)
		{
			// Only show active plans (this is already handled by !sp.IsDelete)
		}

		if (filterBusinessObject.IsPopular.HasValue)
		{
			query = query.Where(sp => sp.IsPopular == filterBusinessObject.IsPopular.Value);
		}

		if (filterBusinessObject.MinPrice.HasValue)
		{
			query = query.Where(sp => sp.AnnualPrice >= filterBusinessObject.MinPrice.Value);
		}

		if (filterBusinessObject.MaxPrice.HasValue)
		{
			query = query.Where(sp => sp.AnnualPrice <= filterBusinessObject.MaxPrice.Value);
		}

		// Order by display order, then by price
		query = query.OrderBy(sp => sp.DisplayOrder).ThenBy(sp => sp.AnnualPrice);

		return query;
	}
}
