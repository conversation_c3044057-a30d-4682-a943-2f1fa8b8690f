﻿using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.Features.OtherSubscriptions;
namespace PremierAutoTag.Server.DataServices.Features.OtherSubscriptions;
public class OtherSubscriptionsServerSideListingDataService : ServerSideListingDataService<OtherSubscriptionsListingBusinessObject, OtherSubscriptionsFilterBusinessObject>, IOtherSubscriptionsListingDataService
{

	private readonly ApplicationDbContext _context;

	public OtherSubscriptionsServerSideListingDataService (ApplicationDbContext context)
	{
		_context = context;
	}
	public override IQueryable<OtherSubscriptionsListingBusinessObject> GetQuery(OtherSubscriptionsFilterBusinessObject filterBusinessObject)
	{
		 return Array.Empty<OtherSubscriptionsListingBusinessObject>().AsQueryable();
	}
}
