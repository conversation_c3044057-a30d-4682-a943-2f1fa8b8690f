﻿namespace PremierAutoTag.Framework.Core
{
    public class PubSubMessage
    {
        public PubSubMessage(string message, string userId)
        {
            Message = message;
            UserId = userId;
        }

        public string Message { get; set; }
        public string UserId { get; } 
    }


    public class PubSubMessage<T> : PubSubMessage where T : class
    {
        public PubSubMessage(string message, string userId, T? payload = null) : base(message, userId) 
        { 
            Payload = payload;
        } 

        public T? Payload { get; set; }
    }


    public class MessageCenter : IMessageCenter
    {
        private readonly IAuthenticatedUser authenticatedUser;

        public MessageCenter(IAuthenticatedUser authenticatedUser)
        {
            this.authenticatedUser = authenticatedUser;
        }

        public void Publish(string message)
        {
            PubSub.Hub.Default.Publish(new PubSubMessage(message, authenticatedUser.UserId));
        }

        public void Publish<T>(string message, T payload) where T : class
        {
            PubSub.Hub.Default.Publish(new PubSubMessage<T>(message, authenticatedUser.UserId, payload));
        }

        public void Subscribe(Action<PubSubMessage> action)
        {
            PubSub.Hub.Default.Subscribe<PubSubMessage>((x) =>
            {
                if (x.UserId == authenticatedUser.UserId)
                {
                    action.Invoke(x);
                }
            });
        }

        public void Subscribe<T>(Action<PubSubMessage<T>> action) where T : class
        {
            PubSub.Hub.Default.Subscribe<PubSubMessage<T>>((x) =>
            {
                if (x.UserId == authenticatedUser.UserId)
                {
                    action.Invoke(x);
                }
            });
        }
    }
}
