﻿using Microsoft.AspNetCore.Components;
using System.Web;

namespace PremierAutoTag.Framework.Core.Extensions
{
    public static class NavigationManagerExtensions
    {
        // get entire querystring name/value collection
        public static List<string> QueryString(this NavigationManager navigationManager)
        {
            List<string> items = new List<string>();
            var parameters = HttpUtility.ParseQueryString(new Uri(navigationManager.Uri).Query);
            var nvc = parameters.AllKeys.SelectMany(k => parameters.GetValues(k)?.Where(v => v != null) ?? Enumerable.Empty<string>(), (k, v) => new { key = k, value = v });
            foreach (var item in nvc)
            {
                if (!items.Any(x => x == string.Concat(item.key, "=", item.value.Replace(' ', '+'))))
                {
                    items.Add(string.Concat(item.key, "=", item.value.Replace(' ', '+')));
                }
            }

            return items;
        }
        public static string GetUrlSegments(this NavigationManager navigationManager)
        {
            string segments = string.Empty;
            var query = new Uri(navigationManager.Uri).Query;
            if (!string.IsNullOrEmpty(query))
            {
                segments = navigationManager.Uri.Replace(navigationManager.BaseUri, "").Replace(query, "");
            }
            else
            {
                segments = navigationManager.Uri.Replace(navigationManager.BaseUri, "");
            }
            return segments;
        }
        public static List<string> GetUrlSegmentsList(this NavigationManager navigationManager)
        {
            try
            {
                string segments = string.Empty;
                var query = new Uri(navigationManager.Uri).Query;
                if (!string.IsNullOrEmpty(query))
                {
                    segments = navigationManager.Uri.Replace(navigationManager.BaseUri, "").Replace(query, "");
                }
                else
                {
                    segments = navigationManager.Uri.Replace(navigationManager.BaseUri, "");
                }
                return segments.Split("/").ToList();
            }
            catch (Exception)
            {

                return new List<string>() { "en-pk" };
            }

        }
        public static List<string> QueryStringToList(this NavigationManager navigationManager)
        {
            return navigationManager.QueryString();
        }

        public static List<KeyValue> QueryStringToKeyValue(this NavigationManager navigationManager)
        {
            var parameters = HttpUtility.ParseQueryString(new Uri(navigationManager.Uri).Query);
            var nvc = parameters.AllKeys.SelectMany(k => parameters.GetValues(k)?.Where(v => v != null) ?? Enumerable.Empty<string>(), (k, v) => new KeyValue { Key = k ?? "", Value = v }).ToList();
            return nvc;
        }


        // get single querystring value with specified key
        public static string QueryString(this NavigationManager navigationManager, string key)
        {
            var data = navigationManager.QueryStringToKeyValue().Find(x => x.Key == key);
            return data == null ? "" : data.Value;
        }

        public class KeyValue
        {
            public string Key { get; set; } = "";
            public string Value { get; set; } = "";
        }
    }
}
