﻿using PremierAutoTag.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Razor.Features.MyProfile;
public class MyProfileFormViewModel : ObservableBase
{
	private string _id = "";
	private string _firstName = "";
	private string _lastName = "";
	private string _phone = "";
	private string _email = "";

	public string Id
	{
		get => _id;
		set => SetField(ref _id, value);
	}

	[Required(ErrorMessage = "First name is required")]
	[StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
	public string FirstName
	{
		get => _firstName;
		set => SetField(ref _firstName, value);
	}

	[Required(ErrorMessage = "Last name is required")]
	[StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
	public string LastName
	{
		get => _lastName;
		set => SetField(ref _lastName, value);
	}

	[StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
	public string Phone
	{
		get => _phone;
		set => SetField(ref _phone, value);
	}

	public string Email
	{
		get => _email;
		set => SetField(ref _email, value);
	}
}
