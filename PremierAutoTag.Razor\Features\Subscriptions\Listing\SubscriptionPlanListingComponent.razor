﻿@page "/subscription-plans"
@rendermode @(new InteractiveServerRenderMode())
@using Microsoft.AspNetCore.Components.Forms
@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Razor.Features.SubscriptionPlan;
@using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
@using PremierAutoTag.Razor.Features.Subscriptions.Form;
@using static PremierAutoTag.Framework.Core.UIServices.MessageTypes;

@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.DependencyInjection
@using Microsoft.JSInterop
@using PremierAutoTag.Razor.Layout
@inject IJSRuntime JSRuntime
@attribute [Authorize(Roles = "Admin")]
@layout MainLayout
@inherits ListingBase<SubscriptionPlanListingViewModel,SubscriptionPlanListingBusinessObject,SubscriptionPlanFilterViewModel,SubscriptionPlanFilterBusinessObject, ISubscriptionPlanListingDataService>

<div class="p-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Subscription Plans</h1>
                    <p class="text-sm text-gray-600 mt-1">Manage subscription plans and pricing</p>
                </div>
                <button class="inline-flex items-center px-4 py-2 bg-primary hover:bg-primaryDark text-white text-sm font-medium rounded-lg transition-colors"
                        type="button" @onclick='()=> ShowDialog<SubscriptionPlanFormComponent>("Create New Subscription Plan", default(long))'>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add New Plan
                </button>
            </div>
        </div>

        <div class="p-6">
            <!-- Filters -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Plan Name</label>
                    <InputText @bind-Value="FilterViewModel.Name"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-primary outline-none"
                               placeholder="Search by name..." />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Min Price</label>
                    <InputNumber @bind-Value="FilterViewModel.MinPrice"
                                 class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-primary outline-none"
                                 placeholder="0.00" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Max Price</label>
                    <InputNumber @bind-Value="FilterViewModel.MaxPrice"
                                 class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-primary outline-none"
                                 placeholder="999.99" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <InputSelect @bind-Value="FilterViewModel.IsActive"
                                 class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-primary outline-none">
                        <option value="">All</option>
                        <option value="true">Active</option>
                        <option value="false">Inactive</option>
                    </InputSelect>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Popular</label>
                    <InputSelect @bind-Value="FilterViewModel.IsPopular"
                                 class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-primary outline-none">
                        <option value="">All</option>
                        <option value="true">Popular</option>
                        <option value="false">Regular</option>
                    </InputSelect>
                </div>
                <div class="flex items-end gap-2">
                    <button type="button"
                            class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-primary hover:bg-primaryDark text-white text-sm font-medium rounded-lg transition-colors"
                            @onclick="LoadItems">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                    <button type="button"
                            class="px-3 py-2 border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm font-medium rounded-lg transition-colors"
                            @onclick="ClearFilters">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            @if(Items == null)
            {
                <div class="flex justify-center items-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span class="ml-3 text-gray-600">Loading subscription plans...</span>
                </div>
            }
            else if(Items.Count() == 0)
            {
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No subscription plans</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating your first subscription plan.</p>
                    <div class="mt-6">
                        <button type="button"
                                class="inline-flex items-center px-4 py-2 bg-primary hover:bg-primaryDark text-white text-sm font-medium rounded-lg transition-colors"
                                @onclick='()=> ShowDialog<SubscriptionPlanFormComponent>("Create New Subscription Plan", default(long))'>
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Add New Plan
                        </button>
                    </div>
                </div>
            }
            else
            {
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-1">
                                        <span>Plan Name</span>
                                        <input type="text" @bind="FilterViewModel.Name" @bind:event="oninput" @onkeyup="@(async (e) => { if (e.Key == "Enter") await LoadItems(); })"
                                               class="ml-2 px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-primary focus:border-primary outline-none"
                                               placeholder="Search..." />
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Annual Price</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Vehicles</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Users</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discounts</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Features</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach(var item in Items)
                            {
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="text-sm font-medium text-gray-900">@item.Name</div>
                                            @if(item.IsPopular)
                                            {
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    Popular
                                                </span>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-semibold text-gray-900">@item.FormattedAnnualPrice</div>
                                        @if(item.IsFree)
                                        {
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Free
                                            </span>
                                        }
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@item.MaxVehiclesDisplay</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@item.MaxUsersDisplay</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-xs text-gray-600">
                                            @if(item.StandAloneQuotesDiscountPercent > 0)
                                            {
                                                <div class="mb-1">
                                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                        Quotes: @item.StandAloneQuotesDiscountPercent%
                                                    </span>
                                                </div>
                                            }
                                            @if(item.RegistrationTitleServicesDiscountPercent > 0)
                                            {
                                                <div>
                                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                        Reg/Title: @item.RegistrationTitleServicesDiscountPercent%
                                                    </span>
                                                </div>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex flex-wrap gap-1">
                                            @if(item.HasDirectSubmission)
                                            {
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                    Direct Submit
                                                </span>
                                            }
                                            @if(item.HasDedicatedClientManager)
                                            {
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                    Client Manager
                                                </span>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @(item.IsActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800")">
                                            @item.StatusDisplay
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button type="button"
                                                    class="inline-flex items-center px-3 py-1.5 border border-primary text-primary hover:bg-primary hover:text-white text-xs font-medium rounded transition-colors"
                                                    @onclick='()=> ShowDialog<SubscriptionPlanFormComponent>("Edit Subscription Plan", item.Id)'>
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Edit
                                            </button>
                                            <button type="button"
                                                    class="inline-flex items-center px-3 py-1.5 border border-red-300 text-red-700 hover:bg-red-50 text-xs font-medium rounded transition-colors"
                                                    @onclick="() => DeleteItem(item.Id)">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                                Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    <EditForm Model="PaginationStrip">
                        @*<PaginationStrip @bind-Value="PaginationStrip" TotalPages="TotalPages" TotalRows="TotalRows"></PaginationStrip>*@
                    </EditForm>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private async Task DeleteItem(long id)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this subscription plan?"))
        {
            try
            {
                using (var scope = ScopeFactory!.CreateScope())
                {
                    var formService = scope.ServiceProvider.GetRequiredService<ISubscriptionPlanFormDataService>();
                    var item = await formService.GetItemByIdAsync(id);
                    if (item != null)
                    {
                        item.IsActive = false; // Soft delete by deactivating
                        await formService.SaveAsync(item);
                        await LoadItems(); // Refresh the list
                        AlertService?.Show("Subscription plan deleted successfully.", Success);
                    }
                }
            }
            catch (Exception ex)
            {
                AlertService?.Show($"Error deleting subscription plan: {ex.Message}", Error);
            }
        }
    }

    private async Task ClearFilters()
    {
       FilterViewModel = new SubscriptionPlanFilterViewModel();
        await LoadItems();
    }
}

