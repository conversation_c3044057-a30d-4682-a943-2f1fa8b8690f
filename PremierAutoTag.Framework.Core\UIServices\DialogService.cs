﻿using System.Collections.ObjectModel;
using System.ComponentModel;

namespace PremierAutoTag.Framework.Core.UIServices
{
    public class KtDialogService
    {
        public ObservableCollection<ModalDialogConfig> Dialogs { get; set; }
         
        public KtDialogService()
        {
            Dialogs = new ObservableCollection<ModalDialogConfig>(); 
        }
         

        public void ShowDialogAsync(ModalDialogConfig dialogConfig)
        {
            Dialogs.Add(dialogConfig);
        }
       
         
    }
    public class ModalDialogConfig
    {

        public ModalDialogConfig()
        {
            Id = $"Dialog{DateTime.Now.ToString("HHMMssff")}_";
            Parameters = new Dictionary<string, object>();
        }

        public void AddParameter(string key, object value)
        {
            Parameters.Add(key, value);
        }

        public string Id { get; set; } = "";

        public string Title { get; set; } = "";

        public string SizeClasses { get; set; } = "";

        public string PositionClasses { get; set; } = "";

        public string DialogContainerClasses { get; set; } = "";

        public bool ShowCrossIcon { get; set; } = true;

        public Type Component { get; set; } = null!;

        public IDictionary<string, object> Parameters { get; set; }
    }

    public class KtNotificationService
    {
        public ObservableCollection<Notification> Notifications { get; set; }

        public KtNotificationService()
        {
            Notifications = new ObservableCollection<Notification>();
        }

        public void ShowError(Exception ex)
        {
            Notifications.Add(new Notification()
            {
                Message = $"{ex.Message} {ex.InnerException?.Message}",
                NotificationType = NotificationType.Error,
            });

        }


        public void ShowError(string message)
        {
            Notifications.Add(new Notification()
            {
                Message = message,
                NotificationType = NotificationType.Error,
            });
        }
    }

    public class AlertService
    {
        public event Action<string, MessageTypes>? OnShowAlert;

        public void Show(string message, MessageTypes type)
        {
            OnShowAlert?.Invoke(message, type);
        }

    }

    public class Notification
    {
        public string? Message { get; set; }
        public NotificationType NotificationType { get; set; }
    }

    public enum NotificationType
    {
        Info,
        Warning,
        Error,

    }

    public enum MessageTypes : short
    {
        [Description("success")]
        Success = 1,
        [Description("warning")]
        Warning = 2,
        [Description("info")]
        Info = 3,
        [Description("danger")]
        Error = 4
    }
}

