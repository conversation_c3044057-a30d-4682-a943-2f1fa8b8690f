namespace PremierAutoTag.ServiceContracts.Models
{
    public class EmailVerificationResult
    {
        public bool IsValid { get; set; }
        public bool IsExpired { get; set; }
        public string? ErrorMessage { get; set; }
        public string? UserId { get; set; }

        public static EmailVerificationResult Success(string userId)
        {
            return new EmailVerificationResult
            {
                IsValid = true,
                IsExpired = false,
                UserId = userId
            };
        }

        public static EmailVerificationResult Invalid(string errorMessage)
        {
            return new EmailVerificationResult
            {
                IsValid = false,
                IsExpired = false,
                ErrorMessage = errorMessage
            };
        }

        public static EmailVerificationResult Expired()
        {
            return new EmailVerificationResult
            {
                IsValid = false,
                IsExpired = true,
                ErrorMessage = "Verification token has expired"
            };
        }
    }
}
