﻿using Microsoft.AspNetCore.Mvc;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.DealershipProfile;
namespace PremierAutoTag.Server.DataServices.Controller.DealershipProfile;
[ApiController, Route("api/[controller]/[action]")]
public class DealershipProfilesFormController : ControllerBase, IDealershipProfileFormDataService
{

	private readonly IDealershipProfileFormDataService dataService;

	public DealershipProfilesFormController(IDealershipProfileFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] DealershipProfileFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[HttpGet]
	public async Task<DealershipProfileFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
