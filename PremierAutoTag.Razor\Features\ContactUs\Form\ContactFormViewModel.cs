﻿using PremierAutoTag.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Razor.Features.Contact;
public class ContactFormViewModel : ObservableBase
{
	private string _email = "";
	private string _phoneNumber = "";
	private string _subject = "";
	private string _message = "";
	private string? _name;

	[Required(ErrorMessage = "Email is required")]
	[EmailAddress(ErrorMessage = "Please enter a valid email address")]
	[StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
	public string Email
	{
		get => _email;
		set => SetField(ref _email, value);
	}

	[Required(ErrorMessage = "Phone number is required")]
	[StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
	public string PhoneNumber
	{
		get => _phoneNumber;
		set => SetField(ref _phoneNumber, value);
	}

	[Required(ErrorMessage = "Subject is required")]
	[StringLength(200, ErrorMessage = "Subject cannot exceed 200 characters")]
	public string Subject
	{
		get => _subject;
		set => SetField(ref _subject, value);
	}

	[Required(ErrorMessage = "Message is required")]
	[StringLength(2000, ErrorMessage = "Message cannot exceed 2000 characters")]
	public string Message
	{
		get => _message;
		set => SetField(ref _message, value);
	}

	[StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
	public string? Name
	{
		get => _name;
		set => SetField(ref _name, value);
	}
}
