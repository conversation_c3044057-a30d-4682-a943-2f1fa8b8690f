using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.UIServices;
using PremierAutoTag.Web.Client;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

builder.Services.AddAuthorizationCore();
builder.Services.AddCascadingAuthenticationState();
builder.Services.AddAuthenticationStateDeserialization();
builder.Services.AddTransient<CookieHandler>();

builder.Services.AddHttpClient<BaseHttpClient, HttpCookieClient>("PremierAutoTag.ServerAPI", client =>
{
    client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress);
}).AddHttpMessageHandler<CookieHandler>();

builder.Services.AddScoped<AlertService>();
builder.Services.AddScoped<KtDialogService>();
builder.Services.AddScoped<KtNotificationService>();
builder.Services.AddScoped<IMessageCenter, MessageCenter>();
builder.Services.AddScoped<IAuthenticatedUser, WasmClientAuthenticatedUser>();
builder.Services.AddScoped<ILocalStorageService, LocalStorageService>();
builder.Services.RegisterServices();

await builder.Build().RunAsync();
