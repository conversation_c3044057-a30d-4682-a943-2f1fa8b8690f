﻿namespace PremierAutoTag.Framework.Core.Extensions
{
    //[AttributeUsage(
    //AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter,
    //AllowMultiple = false)]
    //public class NotEmptystring : ValidationAttribute
    //{
    //    public const string ErrorMessage = "The {0} field must not be empty";
    //    public NotEmptystring() : base(ErrorMessage) { }
    //    public override bool IsValid(object value)
    //    {
    //        if (value is null)
    //            return true; // Allows to return a null value
    //        switch (value)
    //        {
    //            case string string:
    //                return string != string.Empty; //Checks whether the string is empty or not and returns false if string is empty
    //            default:
    //                return true;
    //        }
    //    }
    //}
}
