﻿using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.Features.DealerDashboard;
namespace PremierAutoTag.Server.DataServices.Features.DealerDashboard;
public class DealerDashboardServerSideListingDataService : ServerSideListingDataService<DealerDashboardListingBusinessObject, DealerDashboardFilterBusinessObject>, IDealerDashboardListingDataService
{

	private readonly ApplicationDbContext _context;

	public DealerDashboardServerSideListingDataService (ApplicationDbContext context)
	{
		_context = context;
	}
	public override IQueryable<DealerDashboardListingBusinessObject> GetQuery(DealerDashboardFilterBusinessObject filterBusinessObject)
	{
		 return Array.Empty<DealerDashboardListingBusinessObject>().AsQueryable();
	}
}
