﻿using PremierAutoTag.Framework.Core.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace PremierAutoTag.Framework.Core.Helpers
{
    public static class EnumHelper
    {
        public static string GetDescription<T>(this T enumValue) where T : Enum
        {
            FieldInfo? field = enumValue.GetType().GetField(enumValue.ToString());

            // Get the Description attribute from the enum field, if present
            DescriptionAttribute? attribute = field?.GetCustomAttribute<DescriptionAttribute>();

            // Return the Description if present, else return the enum name
            return attribute != null ? attribute.Description : enumValue.ToString();
        }

        public static string? GetUIHint<T>(this T enumValue) where T : Enum
        {
            FieldInfo? field = enumValue.GetType().GetField(enumValue.ToString());

            // Get the Description attribute from the enum field, if present
            UIHintAttribute? attribute = field?.GetCustomAttribute<UIHintAttribute>();

            return attribute?.UIHint;

        }

        public static string? GetPresentationLayer<T>(this T enumValue) where T : Enum
        {
            FieldInfo? field = enumValue.GetType().GetField(enumValue.ToString());

            // Get the Description attribute from the enum field, if present
            UIHintAttribute? attribute = field?.GetCustomAttribute<UIHintAttribute>();

            return attribute?.PresentationLayer;

        }

        public static List<SelectListItem> GetEnumSelectList<TEnum>() where TEnum : Enum
        {
            return Enum.GetValues(typeof(TEnum))
                .Cast<TEnum>()
                .Select(e => new SelectListItem
                {
                    Text = GetDescription(e),
                    Value = e.ToString()
                })
                .ToList();
        }
         

    }
}
