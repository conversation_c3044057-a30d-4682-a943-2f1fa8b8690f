﻿using PremierAutoTag.Framework.Core;

namespace PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
public class SubscriptionPlanFilterBusinessObject : BaseFilterBusinessObject
{
	public string? Name { get; set; }
	public decimal? MinPrice { get; set; }
	public decimal? MaxPrice { get; set; }
	public bool? IsActive { get; set; }
	public bool? IsPopular { get; set; }
	public bool? HasDirectSubmission { get; set; }
	public bool? HasDedicatedClientManager { get; set; }
}
