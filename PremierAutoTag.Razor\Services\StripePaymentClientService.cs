using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Services;
using System.Text.Json;

namespace PremierAutoTag.Razor.Services
{
    public class StripePaymentClientService : IStripePaymentService
    {
        private readonly BaseHttpClient _httpClient;

        public StripePaymentClientService(BaseHttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<string> CreateCheckoutSessionAsync(CreateCheckoutSessionRequest request)
        {
            var dto = new CreateCheckoutSessionDto
            {
                SubscriptionPlanId = request.SubscriptionPlanId,
                SuccessUrl = request.SuccessUrl,
                CancelUrl = request.CancelUrl,
                Metadata = request.Metadata
            };

            var response = await _httpClient.PostAsJsonAsync<CreateCheckoutSessionResponse>("api/Stripe/create-checkout-session", dto);
            return response.CheckoutUrl ?? "";
        }

        public Task<bool> ProcessPaymentSuccessAsync(string sessionId)
        {
            // This method is typically called server-side only
            throw new NotImplementedException("This method should be called server-side only");
        }

        public Task<bool> ProcessPaymentFailureAsync(string sessionId, string errorMessage)
        {
            // This method is typically called server-side only
            throw new NotImplementedException("This method should be called server-side only");
        }

        public Task<bool> ProcessWebhookEventAsync(string eventJson, string signature)
        {
            // This method is typically called server-side only
            throw new NotImplementedException("This method should be called server-side only");
        }
    }

    public class CreateCheckoutSessionDto
    {
        public long SubscriptionPlanId { get; set; }
        public string SuccessUrl { get; set; } = "";
        public string CancelUrl { get; set; } = "";
        public Dictionary<string, string>? Metadata { get; set; }
    }

    public class CreateCheckoutSessionResponse
    {
        public string? CheckoutUrl { get; set; }
    }
}
