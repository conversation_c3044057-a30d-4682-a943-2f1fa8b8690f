﻿using PremierAutoTag.Framework.Core;
namespace PremierAutoTag.ServiceContracts.Features.OtherSubscriptions;
public class OtherSubscriptionsFilterBusinessObject :  BaseFilterBusinessObject
{
	// User-specific filter to exclude current user's subscription plan
	public string? UserId { get; set; }

	// Optional filters for subscription plans
	public bool? IsActive { get; set; }
	public bool? IsPopular { get; set; }
	public decimal? MinPrice { get; set; }
	public decimal? MaxPrice { get; set; }
}
