﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PremierAutoTag.Server.Data.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSubscriptionPlanStructure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HasAPIAccess",
                table: "SubscriptionPlans");

            migrationBuilder.DropColumn(
                name: "MonthlyPrice",
                table: "SubscriptionPlans");

            migrationBuilder.RenameColumn(
                name: "HasPrioritySupport",
                table: "SubscriptionPlans",
                newName: "HasDirectSubmission");

            migrationBuilder.RenameColumn(
                name: "HasAdvancedReporting",
                table: "SubscriptionPlans",
                newName: "HasDedicatedClientManager");

            migrationBuilder.AddColumn<int>(
                name: "RegistrationTitleServicesDiscountPercent",
                table: "SubscriptionPlans",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "StandAloneQuotesDiscountPercent",
                table: "SubscriptionPlans",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RegistrationTitleServicesDiscountPercent",
                table: "SubscriptionPlans");

            migrationBuilder.DropColumn(
                name: "StandAloneQuotesDiscountPercent",
                table: "SubscriptionPlans");

            migrationBuilder.RenameColumn(
                name: "HasDirectSubmission",
                table: "SubscriptionPlans",
                newName: "HasPrioritySupport");

            migrationBuilder.RenameColumn(
                name: "HasDedicatedClientManager",
                table: "SubscriptionPlans",
                newName: "HasAdvancedReporting");

            migrationBuilder.AddColumn<bool>(
                name: "HasAPIAccess",
                table: "SubscriptionPlans",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "MonthlyPrice",
                table: "SubscriptionPlans",
                type: "decimal(16,2)",
                precision: 16,
                scale: 2,
                nullable: false,
                defaultValue: 0m);
        }
    }
}
