﻿namespace PremierAutoTag.Framework.Core.Utils
{
    public static class SafeConvert
    {
        public static byte ToByte(object val)
        {
            try
            {
                return Convert.ToByte(val);
            }
            catch { }
            return 0;
        }

        public static short ToInt16(object val)
        {
            try
            {
                return Convert.ToInt16(val);
            }
            catch { }
            return 0;
        }

        public static int ToInt32(object? val)
        {
            try
            {
                return Convert.ToInt32(val);
            }
            catch { }
            return 0;
        }


        public static long ToInt64(object val)
        {
            try
            {
                return Convert.ToInt64(val);
            }
            catch { }
            return 0;
        }

        public static double ToDouble(object val)
        {
            try
            {
                if (double.TryParse(val?.ToString(), out double d))
                {
                    return d;
                }
            }
            catch { }
            return 0;
        }
        
    }
}
