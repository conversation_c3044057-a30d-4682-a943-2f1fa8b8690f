{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Stripe": {"PublishableKey": "pk_test_51HlqbIDQNEI0AdBmfCXJ5rGsplwk2YiZFNgIxujWPRL0JuVTWKLyfZhxTtYZlLvRBrLzLpRFFRr9jqMEAUar4hYN00EMV5IYLQ", "SecretKey": "sk_test_51HlqbIDQNEI0AdBmqDag5qJIU8wYHjDuUAwKmtt6PUmI2BAB091LVnCVko40JT0ncmSGKxhnmMaHi3ddE9FBYqUr00dgyHtucr", "WebhookSecret": "whsec_your_webhook_secret_here"}, "ConnectionStrings": {"DefaultConnection": "data source=localhost\\MSSQLSERVER22;initial catalog=AutoTagDB;User ID=sa;Password=******;MultipleActiveResultSets=True;connection timeout=300;App=EntityFramework; Integrated Security=true;Trusted_Connection=True;TrustServerCertificate=True;"}, "EmailSettings": {"ApiKey": "endpoint=https://epccommunicationservice.uae.communication.azure.com/;accesskey=CgvAVP2m6dKmgu9vEJHxd94Me1KrAt31J4YDPHcb14OXGqh34ioIJQQJ99AGACULyCpwpOZ0AAAAAZCSr0RD", "DefaultEmail": "<EMAIL>"}, "SecureDataSetting": {"DataProtectionKey": "auto@123321"}, "Development": {"ShowEmailVerificationLinks": false}, "ContactSetting": {"Email": "<EMAIL>"}}