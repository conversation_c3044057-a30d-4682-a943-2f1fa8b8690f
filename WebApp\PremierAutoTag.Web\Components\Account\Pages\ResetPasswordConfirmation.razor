﻿@page "/Account/ResetPasswordConfirmation"
@layout WebsiteLayout

@using PremierAutoTag.Razor.Layout

<PageTitle>Password Reset Complete</PageTitle>

<section class="">
    <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <div class="bg-white max-w-2xl w-full flex flex-col gap-4 md:gap-6 rounded-3xl p-6 md:p-10 border border-gray-200">

            <!-- Success Icon -->
            <div class="flex justify-center mb-4">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- Header -->
            <div class="text-center">
                <h1 class="text-black font-bold mb-2 text-xl md:text-2xl">Password Reset Complete</h1>
                <h2 class="text-gray-600 text-sm md:text-base">
                    Your password has been successfully reset.
                </h2>
            </div>

            <!-- Success Message -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">
                            Great! Your password has been updated successfully. You can now log in with your new password.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Security Tips -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800 mb-1">Security Tips</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Keep your password secure and don't share it with anyone</li>
                            <li>• Use a unique password for your AutoTag account</li>
                            <li>• Consider using a password manager</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex flex-col gap-3">
                <a href="/Account/Login"
                   class="w-full py-2 md:py-2.5 rounded-lg bg-primary hover:bg-primaryDark text-white text-sm md:text-base font-semibold text-center">
                    Continue to Login
                </a>

                <a href="/"
                   class="w-full py-2 md:py-2.5 rounded-lg border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm md:text-base font-semibold text-center">
                    Go to Homepage
                </a>
            </div>

            <!-- Footer -->
            <div class="flex justify-center gap-1 text-center">
                <span class="text-sm text-gray-600">Need help?</span>
                <a href="/contact"
                   class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4">
                    Contact Support
                </a>
            </div>

        </div>
    </div>
</section>
