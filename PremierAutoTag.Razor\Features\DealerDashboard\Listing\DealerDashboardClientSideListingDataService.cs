﻿using System.Net.Http.Json;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Extensions;
using PremierAutoTag.ServiceContracts;
using PremierAutoTag.ServiceContracts.Features.DealerDashboard;
namespace PremierAutoTag.Razor.Features.DealerDashboard;
public class DealerDashboardClientSideListingDataService : IDealerDashboardListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public DealerDashboardClientSideListingDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<PagedDataList<DealerDashboardListingBusinessObject>> GetPaginatedItems(DealerDashboardFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<DealerDashboardListingBusinessObject>>($"api/DealerDashboardsListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}
