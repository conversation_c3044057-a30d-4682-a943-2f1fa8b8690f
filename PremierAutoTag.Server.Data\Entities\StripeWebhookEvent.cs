using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Server.Data.Entities
{
    [Index(nameof(Id))]
    public class StripeWebhookEvent : BaseEntity
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(255)]
        public string StripeEventId { get; set; } = "";

        [Required]
        [StringLength(100)]
        public string EventType { get; set; } = "";

        [Required]
        public string EventData { get; set; } = "";

        [Required]
        [StringLength(50)]
        public string ProcessingStatus { get; set; } = "Pending";

        public DateTime? ProcessedAt { get; set; }

        [StringLength(1000)]
        public string? ProcessingError { get; set; }

        public long? StripePaymentId { get; set; }

        // Navigation properties
        public virtual StripePayment? StripePayment { get; set; }
    }
}
