﻿@page "/myprofile"
@layout MainLayout

@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Framework.Core.UIServices;
@using PremierAutoTag.Razor.Features.MyProfile;
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.ServiceContracts.Features.MyProfile;
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Logging
@attribute [Authorize]
@inherits FormBase<MyProfileFormBusinessObject,MyProfileFormViewModel, string, IMyProfileFormDataService>

@if (!string.IsNullOrEmpty(ValidationError))
{
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <p class="text-red-800 text-sm md:text-base">@ValidationError</p>
    </div>
}

@if (SelectedItem != null)
{
    <EditForm Enhance method="post" Model="SelectedItem" OnValidSubmit="OnFormSubmit" FormName="MyProfile">
        <DataAnnotationsValidator></DataAnnotationsValidator>
        <div class="w-full">
            <div class="tab-content block w-full" data-content="1">
                <h2 class="font-semibold text-lg md:text-xl text-gray-m-800">
                    My Profile
                </h2>
                <hr class="my-4 text-gray-200" />

                <div class="grid grid-cols-12 gap-6 items-center">
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">First Name <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.FirstName"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter your first name" />
                        <ValidationMessage For="() => SelectedItem.FirstName" class="text-red-600 text-sm mt-1" />
                    </div>
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Last Name <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.LastName"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter your last name" />
                        <ValidationMessage For="() => SelectedItem.LastName" class="text-red-600 text-sm mt-1" />
                    </div>
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">
                            Phone <span class="hidden md:inline-block">Number</span>
                        </span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.Phone"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter your phone number" />
                        <ValidationMessage For="() => SelectedItem.Phone" class="text-red-600 text-sm mt-1" />
                    </div>
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">
                            Email <span class="hidden md:inline-block">Address</span>
                        </span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <input value="@SelectedItem.Email" disabled readonly type="email"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 disabled:bg-gray-50 disabled:text-gray-500 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="Email address" />
                        <p class="text-gray-500 text-xs mt-1">Email address cannot be changed from this form</p>
                    </div>
                </div>

                <hr class="my-4 text-gray-200" />

                <div class="flex flex-row gap-3 justify-end items-center">
                    <button type="button"
                            disabled="@IsBusy"
                            class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            disabled="@IsBusy"
                            class="px-4 py-2 rounded-lg bg-primary hover:bg-primaryDark disabled:bg-gray-400 text-white transition-colors">
                        @if (IsBusy)
                        {
                            <span class="flex items-center gap-2">
                                <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Saving...
                            </span>
                        }
                        else
                        {
                            <text>Save</text>
                        }
                    </button>
                </div>
            </div>
        </div>
    </EditForm>
}

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Set the user ID from authenticated user - framework will handle the rest automatically
            if (AuthenticatedUser != null && !string.IsNullOrEmpty(AuthenticatedUser.UserId))
            {
                Id = AuthenticatedUser.UserId;
            }

            // Let framework handle automatic data loading via GetItemByIdAsync() and ConvertBusinessModelToViewModel()
            await base.OnInitializedAsync();
        }
        catch (Exception ex)
        {
            ValidationError = $"Error initializing profile: {ex.Message}";
            Logger.LogError(ex, "Error initializing MyProfile component for user {UserId}", AuthenticatedUser?.UserId);
        }
    }

    // Framework automatically handles conversion using Clone<T>() extension method
    // No need to override ConvertViewModelToBusinessModel and ConvertBusinessModelToViewModel
    // unless custom logic is required

    public override async Task OnAfterSaveAsync(string key)
    {
        // Show success message using framework's AlertService
        AlertService?.Show("Profile updated successfully!", MessageTypes.Success);

        await base.OnAfterSaveAsync(key);
    }
}
