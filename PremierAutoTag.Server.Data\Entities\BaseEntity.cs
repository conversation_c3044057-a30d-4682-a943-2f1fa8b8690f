﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Server.Data.Entities
{
    [Index("CreatedBy", IsUnique = false)]
    [Index("ModifiedBy", IsUnique = false)]
    public class BaseEntity
    {
        public DateTime CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public DateTime? DeletedAt { get; set; }

        [StringLength(36)]
        public string? CreatedBy { get; set; }

        [StringLength(36)]
        public string? ModifiedBy { get; set; }

        [StringLength(36)]
        public string? DeletedBy { get; set; }

        public bool IsActive { get; set; }

        public bool IsDelete { get; set; }

        public static string NewId() { return Guid.NewGuid().ToString().ToLower(); }
    }
}
