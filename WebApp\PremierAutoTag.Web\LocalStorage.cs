﻿using Microsoft.JSInterop;
using PremierAutoTag.Framework.Core;
using System.Runtime.CompilerServices;

namespace PremierAutoTag.Web
{
    public class LocalStorageService : ILocalStorageService
    {
        private readonly IJSRuntime _jsRuntime;
        public LocalStorageService(IJSRuntime jSRuntime)
        {
            _jsRuntime = jSRuntime;
        }

        public async Task<string?> GetValue([CallerMemberName] string memberName = "")
        {
            return await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", memberName);
        }

        public async Task RemoveValue([CallerMemberName] string memberName = "")
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", memberName);
        }

        public async Task SetValue(string? value, [CallerMemberName] string memberName = "")
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", memberName, value);
        }
    }

}
