﻿@page "/Account/Register/Individual"
@layout WebsiteLayout

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.Server.Data.Entities
@using PremierAutoTag.Server.Data.DB
@using PremierAutoTag.Server.Data
@using PremierAutoTag.ServiceContracts.Services
@using PremierAutoTag.Framework.Core.DataProtections

@inject UserManager<ApplicationUser> UserManager
@inject IUserStore<ApplicationUser> UserStore
@inject SignInManager<ApplicationUser> SignInManager
@inject RoleManager<IdentityRole> RoleManager
@inject ILogger<Register> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject ApplicationDbContext DbContext
@inject PremierAutoTag.Web.Services.IRoleBasedRedirectService RoleBasedRedirectService
@inject IEmailVerificationService EmailVerificationService
@inject IDataProtection DataProtection

<PageTitle>Register</PageTitle>


<session class="">
    <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <EditForm Enhance Model="Input" asp-route-returnUrl="@ReturnUrl" method="post" OnValidSubmit="RegisterUser" FormName="register" class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
            <DataAnnotationsValidator />
            <div>
                <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Create your account</h1>
                <h2 class="text-gray-600 text-sm md:text-base text-center">
                    Please fill the below fields to create
                    your account
                </h2>
            </div>
            <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                <div class="w-full">
                    <label class="text-brightGray font-medium text-sm mb-1 block">
                        First Name <span class="text-red-500">*</span>
                    </label>
                    <InputText @bind-Value="Input.FirstName"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="First Name" />
                    <ValidationMessage For="() => Input.FirstName" class="text-red-500 text-sm mt-1" />
                </div>
                <div class="w-full">
                    <label class="text-brightGray font-medium text-sm mb-1 block">
                        Last Name <span class="text-red-500">*</span>
                    </label>
                    <InputText @bind-Value="Input.LastName"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="Last Name" />
                    <ValidationMessage For="() => Input.LastName" class="text-red-500 text-sm mt-1" />
                </div>
            </div>

            <div>
                <label class="text-brightGray font-medium text-sm mb-1 block">
                    Email <span class="text-red-500">*</span>
                </label>
                <InputText @bind-Value="Input.Email"
                           class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                           placeholder="Your Email" />
                <ValidationMessage For="() => Input.Email" class="text-red-500 text-sm mt-1" />
            </div>

            <div>
                <label class="text-brightGray font-medium text-sm mb-1 block">
                    Phone Number <span class="text-red-500">*</span>
                </label>
                <InputText @bind-Value="Input.Phone"
                           class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                           placeholder="Your Phone Number" />
                <ValidationMessage For="() => Input.Phone" class="text-red-500 text-sm mt-1" />
            </div>

            <div>
                <label class="text-brightGray font-medium text-sm mb-1 block">Password <span class="text-red-500">*</span></label>
                <InputText type="password" @bind-Value="Input.Password"
                           class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                           placeholder="••••••••" />
                <ValidationMessage For="() => Input.Password" class="text-red-500 text-sm mt-1" />
            </div>

            <div>
                <label class="text-brightGray font-medium text-sm mb-1 block">Confirm Password <span class="text-red-500">*</span></label>
                <InputText type="password" @bind-Value="Input.ConfirmPassword"
                           class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                           placeholder="••••••••" />
                <ValidationMessage For="() => Input.ConfirmPassword" class="text-red-500 text-sm mt-1" />
            </div>

            <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                    <image src="images/checkIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                    <image src="images/checkGreenIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                    <span class="text-xs md:text-sm text-vampireGray block">Must be at least 8 characters</span>
                </div>

                <div class="flex items-center gap-2">
                    <image src="images/checkIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                    <image src="images/checkGreenIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                    <span class="text-xs md:text-sm text-vampireGray block">
                        Must contain one special
                        character
                    </span>
                </div>
            </div>

            <button class="w-full text-center py-2 md:py-2.5 rounded-lg bg-primary hover:bg-primaryDark text-white text-sm md:text-base font-semibold"
                    type="submit">
                Get Started
            </button>

            <div class="flex justify-center gap-1">
                <span class="text-sm text-gray-600">Already have an account?</span>
                <a href="/Account/Login"
                   class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4 ">
                    Log In
                </a>
            </div>

        </EditForm>
    </div>
</session>


@* <h1>Register</h1>

<div class="row">
    <div class="col-lg-6">
        <StatusMessage Message="@Message" />
        <EditForm Model="Input" asp-route-returnUrl="@ReturnUrl" method="post" OnValidSubmit="RegisterUser" FormName="register">
            <DataAnnotationsValidator />
            <h2>Create a new account.</h2>
            <hr />
            <ValidationSummary class="text-danger" role="alert" />
            <div class="form-floating mb-3">
                <InputText @bind-Value="Input.Email" id="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
                <label for="Input.Email">Email</label>
                <ValidationMessage For="() => Input.Email" class="text-danger" />
            </div>
            <div class="form-floating mb-3">
                <InputText type="password" @bind-Value="Input.Password" id="Input.Password" class="form-control" autocomplete="new-password" aria-required="true" placeholder="password" />
                <label for="Input.Password">Password</label>
                <ValidationMessage For="() => Input.Password" class="text-danger" />
            </div>
            <div class="form-floating mb-3">
                <InputText type="password" @bind-Value="Input.ConfirmPassword" id="Input.ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" placeholder="password" />
                <label for="Input.ConfirmPassword">Confirm Password</label>
                <ValidationMessage For="() => Input.ConfirmPassword" class="text-danger" />
            </div>
            <button type="submit" class="w-100 btn btn-lg btn-primary">Register</button>
        </EditForm>
    </div>  
</div> *@

@code {
    private IEnumerable<IdentityError>? identityErrors;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    private string? Message => identityErrors is null ? null : $"Error: {string.Join(", ", identityErrors.Select(error => error.Description))}";

    public async Task RegisterUser(EditContext editContext)
    {
        var user = CreateUser();

        await UserStore.SetUserNameAsync(user, Input.Email, CancellationToken.None);
        var emailStore = GetEmailStore();
        await emailStore.SetEmailAsync(user, Input.Email, CancellationToken.None);
        user.PhoneNumber = Input.Phone;

        var result = await UserManager.CreateAsync(user, Input.Password);

        if (!result.Succeeded)
        {
            identityErrors = result.Errors;
            return;
        }

        Logger.LogInformation("User created a new account with password.");

        // Assign User role to individual users
        var userId = await UserManager.GetUserIdAsync(user);
        await UserManager.AddToRoleAsync(user, AppRoles.User);

        // Create UserProfile for individual user
        var userProfile = new UserProfile
        {
            Id = userId,
            FirstName = Input.FirstName,
            LastName = Input.LastName,
            Phone = Input.Phone,
            DealerName = $"{Input.FirstName} {Input.LastName}", // For individuals, use full name
            DealerId = "INDIVIDUAL", // Mark as individual user
            TaxId = "", // Not applicable for individuals
            Address = "",
            AuthorizedCompany = "",
            AdministrativeEmail = Input.Email,
            AdministrativePhone = Input.Phone,
            StateId = 0, // Default state
            Subsidary = false,
            DEALERSHIP_IOT_TYPES = 0, // Not applicable for individuals
            VEHICAL_ON_IOT_TYPES = 0, // Not applicable for individuals
            VEHICAL_SOLD_IOT = 0, // Not applicable for individuals
            SubscriptionPlanId = null, // No subscription initially
            CreatedBy = userId,
            CreatedAt = DateTime.UtcNow,
            IsActive = true,
            IsDelete = false
        };

        DbContext.Profiles.Add(userProfile);
        await DbContext.SaveChangesAsync();

        // Generate email verification token and send verification email
        var code = await EmailVerificationService.GenerateEmailVerificationTokenAsync(userId);
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
            new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code, ["returnUrl"] = ReturnUrl });

        // Send verification email for individual user
        var emailSent = await EmailVerificationService.SendIndividualVerificationEmailAsync(userId, Input.Email, callbackUrl);

        if (!emailSent)
        {
            Logger.LogWarning("Failed to send verification email to {Email} for user {UserId}", Input.Email, userId);
        }

        // Redirect to confirmation page instead of signing in immediately
        var encodedData = DataProtection.Encode(Input.Email);
        RedirectManager.RedirectTo(
            "Account/RegisterConfirmation",
            new() { ["data"] = encodedData, ["returnUrl"] = ReturnUrl });
    }

    private ApplicationUser CreateUser()
    {
        try
        {
            return Activator.CreateInstance<ApplicationUser>();
        }
        catch
        {
            throw new InvalidOperationException($"Can't create an instance of '{nameof(ApplicationUser)}'. " +
                $"Ensure that '{nameof(ApplicationUser)}' is not an abstract class and has a parameterless constructor.");
        }
    }

    private IUserEmailStore<ApplicationUser> GetEmailStore()
    {
        if (!UserManager.SupportsUserEmail)
        {
            throw new NotSupportedException("The default UI requires a user store with email support.");
        }
        return (IUserEmailStore<ApplicationUser>)UserStore;
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        public string Email { get; set; } = "";

        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        public string Password { get; set; } = "";

        [DataType(DataType.Password)]
        [Display(Name = "Confirm password")]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = "";

        [Required]
        public string FirstName { get; set; } = "";

        [Required]
        public string LastName { get; set; } = "";

        [Required]
        public string Phone { get; set; } = "";
    }
}
