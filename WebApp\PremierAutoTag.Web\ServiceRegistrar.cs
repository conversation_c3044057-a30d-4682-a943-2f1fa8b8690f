using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.DataProtections;
using PremierAutoTag.Framework.Core.UIServices;
using PremierAutoTag.Server.DataServices;
using PremierAutoTag.Server.DataServices.Features.AdminDashboard;
using PremierAutoTag.Server.DataServices.SelectList;
using PremierAutoTag.ServiceContracts.Features.AdminDashboard;
using PremierAutoTag.ServiceContracts.SelectList;
using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
using PremierAutoTag.Server.DataServices.Features.SubscriptionPlan;
using PremierAutoTag.ServiceContracts.Features.UserDashboard;
using PremierAutoTag.Server.DataServices.Features.UserDashboard;
using PremierAutoTag.ServiceContracts.Features.DealerDashboard;
using PremierAutoTag.Server.DataServices.Features.DealerDashboard;
using PremierAutoTag.Server.DataServices.Services;
using PremierAutoTag.ServiceContracts.Services;
using PremierAutoTag.ServiceContracts.Settings;
using PremierAutoTag.ServiceContracts.Features.MyProfile;
using PremierAutoTag.Server.DataServices.Features.MyProfile;
using PremierAutoTag.ServiceContracts.Features.ChangePassword;
using PremierAutoTag.Server.DataServices.Features.Profiles.Form;
using PremierAutoTag.ServiceContracts.Features.Contact;
using PremierAutoTag.Server.DataServices.Features.Contact;
using PremierAutoTag.Web.Services;
using Microsoft.AspNetCore.Authentication;
using PremierAutoTag.ServiceContracts.Features.DealershipProfile;
                                                               using PremierAutoTag.Server.DataServices.Features.DealershipProfile; 
                                                               //##NewServiceNamespace##
namespace PremierAutoTag.Web
{
    public static class ServiceRegistrar
    {
        public static void RegisterServices(this IServiceCollection services, ConfigurationManager configuration)
        {

            services.Configure<EmailSettings>(configuration.GetSection("EmailSettings"));
            services.Configure<SecureDataSetting>(configuration.GetSection("SecureDataSetting"));
            services.Configure<ContactSetting>(configuration.GetSection("ContactSetting"));

            services.AddSingleton<IDataProtection, DataProtection>();
            services.AddSingleton<AlertService>();
            services.AddSingleton<IEmailService, AzureEmailService>();

            services.AddScoped<IMessageCenter, MessageCenter>();
            services.AddScoped<KtDialogService>();
            services.AddScoped<KtNotificationService>();
            services.AddScoped<IAuthenticatedUser, ServerAuthenticatedUser>();
            services.AddScoped<ILocalStorageService, LocalStorageService>();
            services.AddScoped<ISelectListDataService, SelectListDataService>();
            services.AddScoped<IEmailVerificationService, EmailVerificationService>();
            services.AddScoped<IRoleBasedRedirectService, RoleBasedRedirectService>();
            services.AddScoped<IClaimsTransformation, CustomClaimsTransformation>();

            services.AddScoped<IAdminDashboardListingDataService, AdminDashboardServerSideListingDataService>();
            services.AddScoped<ISubscriptionPlanFormDataService, SubscriptionPlanServerSideFormDataService>();
            services.AddScoped<ISubscriptionPlanListingDataService, SubscriptionPlanServerSideListingDataService>();
            services.AddScoped<IUserDashboardListingDataService, UserDashboardServerSideListingDataService>();
            services.AddScoped<IDealerDashboardListingDataService, DealerDashboardServerSideListingDataService>();
            services.AddScoped<IStripePaymentService, StripePaymentService>();
            services.AddScoped<IMyProfileFormDataService, MyProfileServerSideFormDataService>();
            services.AddScoped<IChangePasswordFormDataService, ChangePasswordServerSideFormDataService>();
            services.AddScoped<IContactFormDataService, ContactServerSideFormDataService>();
            services.AddScoped<IDealershipProfileFormDataService, DealershipProfileServerSideFormDataService>(); 
 //##NewService##
        }
    }
}
