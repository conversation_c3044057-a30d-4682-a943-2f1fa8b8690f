﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PremierAutoTag.Server.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRequiresPaymentToSubscriptionPlan : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "RequiresPayment",
                table: "SubscriptionPlans",
                type: "bit",
                nullable: false,
                defaultValue: false);

            // Update RequiresPayment based on existing AnnualPrice values
            migrationBuilder.Sql(@"
                UPDATE SubscriptionPlans
                SET RequiresPayment = CASE
                    WHEN AnnualPrice > 0 THEN 1
                    ELSE 0
                END");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RequiresPayment",
                table: "SubscriptionPlans");
        }
    }
}
