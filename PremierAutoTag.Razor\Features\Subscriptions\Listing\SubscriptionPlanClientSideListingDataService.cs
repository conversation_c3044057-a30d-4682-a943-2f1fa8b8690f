﻿using System.Net.Http.Json;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Extensions;
using PremierAutoTag.ServiceContracts;
using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
namespace PremierAutoTag.Razor.Features.SubscriptionPlan;
public class SubscriptionPlanClientSideListingDataService : ISubscriptionPlanListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public SubscriptionPlanClientSideListingDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<PagedDataList<SubscriptionPlanListingBusinessObject>> GetPaginatedItems(SubscriptionPlanFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<SubscriptionPlanListingBusinessObject>>($"api/SubscriptionPlansListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}
