﻿using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.Features.Contact;
using PremierAutoTag.ServiceContracts.Services;
using PremierAutoTag.ServiceContracts.Settings;
using PremierAutoTag.ServiceContracts.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace PremierAutoTag.Server.DataServices.Features.Contact;
public class ContactServerSideFormDataService : IContactFormDataService
{
	private readonly ApplicationDbContext _context;
	private readonly ILogger<ContactServerSideFormDataService> _logger;
	private readonly IEmailService _emailService;
	private readonly ContactSetting _contactSettings;

	public ContactServerSideFormDataService(
		ApplicationDbContext context,
		ILogger<ContactServerSideFormDataService> logger,
		IEmailService emailService,
		IOptions<ContactSetting> contactSettings)
	{
		_context = context;
		_logger = logger;
		_emailService = emailService;
		_contactSettings = contactSettings.Value;
	}

	public async Task<long> SaveAsync(ContactFormBusinessObject formBusinessObject)
	{
		try
		{
			_logger.LogInformation("Processing contact form submission - Email: {Email}, Subject: {Subject}",
				formBusinessObject.Email, formBusinessObject.Subject);

			// Send email notification
			var emailSent = await SendContactFormEmailAsync(formBusinessObject);

			if (!emailSent)
			{
				_logger.LogError("Failed to send contact form email for submission from {Email}", formBusinessObject.Email);
				throw new InvalidOperationException("Failed to send contact form email. Please try again later.");
			}

			_logger.LogInformation("Contact form email sent successfully for submission from {Email}", formBusinessObject.Email);

			// Return a success ID since we're not storing in database
			// If you implement database storage, save the record and return the actual ID
			return DateTime.Now.Ticks;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error processing contact form submission from {Email}", formBusinessObject.Email);
			throw;
		}
	}

	public async Task<ContactFormBusinessObject?> GetItemByIdAsync(long id)
	{
		// Since we're not storing contact forms in database, return null
		// If you implement database storage, implement this method to retrieve saved contact forms
		return await Task.FromResult<ContactFormBusinessObject?>(null);
	}

	private async Task<bool> SendContactFormEmailAsync(ContactFormBusinessObject contactForm)
	{
		try
		{
			var contactEmail = _contactSettings.Email;
			if (string.IsNullOrEmpty(contactEmail))
			{
				_logger.LogError("Contact email not configured in ContactSetting");
				return false;
			}

			var emailContent = GenerateContactFormEmailContent(contactForm);

			var emailEvent = new EmailEvent
			{
				To = new List<EmailAddressModel> { new EmailAddressModel(contactEmail) },
				Subject = $"AutoTag Contact Form: {contactForm.Subject}",
				Message = emailContent
			};

			return await _emailService.SendEmailAsync(emailEvent);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error sending contact form email");
			return false;
		}
	}

	private string GenerateContactFormEmailContent(ContactFormBusinessObject contactForm)
	{
		var senderName = !string.IsNullOrEmpty(contactForm.Name) ? contactForm.Name : "Anonymous";

		return $@"
			<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
				<div style='text-align: center; margin-bottom: 30px;'>
					<h1 style='color: #2563eb; margin-bottom: 10px;'>AutoTag Contact Form Submission</h1>
					<p style='color: #6b7280; font-size: 16px;'>New message received from website contact form</p>
				</div>

				<div style='background-color: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 30px;'>
					<h2 style='color: #374151; margin-bottom: 20px; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;'>Contact Details</h2>

					<div style='margin-bottom: 15px;'>
						<strong style='color: #374151;'>Name:</strong>
						<span style='color: #6b7280; margin-left: 10px;'>{senderName}</span>
					</div>

					<div style='margin-bottom: 15px;'>
						<strong style='color: #374151;'>Email:</strong>
						<span style='color: #6b7280; margin-left: 10px;'>{contactForm.Email}</span>
					</div>

					<div style='margin-bottom: 15px;'>
						<strong style='color: #374151;'>Phone:</strong>
						<span style='color: #6b7280; margin-left: 10px;'>{contactForm.PhoneNumber}</span>
					</div>

					<div style='margin-bottom: 20px;'>
						<strong style='color: #374151;'>Subject:</strong>
						<span style='color: #6b7280; margin-left: 10px;'>{contactForm.Subject}</span>
					</div>

					<div style='margin-bottom: 20px;'>
						<strong style='color: #374151;'>Message:</strong>
						<div style='background-color: white; padding: 15px; border-radius: 6px; margin-top: 10px; border-left: 4px solid #2563eb;'>
							<p style='color: #374151; line-height: 1.6; margin: 0; white-space: pre-wrap;'>{contactForm.Message}</p>
						</div>
					</div>

					<div style='margin-top: 20px; padding-top: 15px; border-top: 1px solid #e5e7eb;'>
						<p style='color: #9ca3af; font-size: 12px; margin: 0;'>
							Submitted on: {DateTime.Now:MMMM dd, yyyy 'at' h:mm tt}
						</p>
					</div>
				</div>

				<div style='text-align: center; color: #9ca3af; font-size: 12px;'>
					<p>This message was sent through the AutoTag website contact form.</p>
					<p>Please respond directly to the customer's email address: {contactForm.Email}</p>
				</div>
			</div>";
	}
}
