
using Microsoft.AspNetCore.Components;
using Ra<PERSON>zen;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using static Platform.SourceGenerator.Pages.Index;

namespace Platform.SourceGenerator.Pages
{

    public enum ProjectType
    {
        ServiceContracts = 2,
        ServerSideServices = 3,
        ClientSideServices = 4,
        Controllers = 5,
        UILibrary = 6,
        ClientDependency = 7,
        ServerSideDependency = 8
    }

    public partial class Index
    {
        const string __name__ = "##Name##";
        const string __projectNamespace__ = "##projectNamespace##";
        const string __componentPrefix__ = "##ComponentPrefix##";
        const string __moduleNamespace__ = "##moduleNamespace##";
        const string __frameworkNamespace__ = "##frameworkNamespace##";
        const string __dbContextNamespace__ = "##dbContextNamespace##";
        const string __primaryKeyType__ = "##primaryKeyType##";
        const string __featureClaim__ = "##FeatureEnum##";

        public FormViewModel M { get; set; } = new FormViewModel()
        {
            PkType = string.Empty,
            GenerateForm = true,
            GenerateListing = true,
            GenerateControllerAndClientService = true,
            Project = ProjectEnumType.Portal
        };

        [Inject]
        NotificationService NotificationService { get; set; }

        public CodeCofig CodeCofig { get; set; }
        public Index()
        {
            //CodeCofig = JsonSerializer.Deserialize<CodeCofig>(File.ReadAllText($"appsettings.{M.Project.ToString().ToLower()}.json"));
            if (File.Exists("temp.local.json"))
            {
                try
                {
                    var jsonText = File.ReadAllText("temp.local.json");
                    M = JsonSerializer.Deserialize<FormViewModel>(jsonText) ?? new FormViewModel()
                    {
                        PkType = string.Empty,
                        GenerateForm = true,
                        GenerateListing = true,
                        GenerateControllerAndClientService = true,
                        Project = ProjectEnumType.Portal
                    };
                }
                catch { }
            }
        }

        public enum FileCategory
        {
            Listing = 1,
            Form = 2
        }

        public async Task GenerateDynamicCode()
        {
            CodeCofig = JsonSerializer.Deserialize<CodeCofig>(File.ReadAllText($"appsettings.{M.Project.ToString().ToLower()}.json"));
            File.WriteAllText("temp.local.json", JsonSerializer.Serialize(M));

            var projectProfile = CodeCofig.Profiles.FirstOrDefault();

            foreach (var project in projectProfile.Projects)
            {
                var projectNamespace = $"{project.NameSpace}.{M.NameSpace}";
                var projectPath = Path.Combine(M.BasePath, project.Path, M.DirectoryName);
                List<string> content = new();

                if (!Directory.Exists(projectPath))
                {
                    Directory.CreateDirectory(projectPath);
                }

                if (M.GenerateListing)
                {
                    foreach (var codeFile in CodeCofig.ProjectFiles[project.ProjectType].Where(x => x.FileName.Contains("Listing")))
                    {
                        codeFile.FileName = codeFile.FileName.Replace(__componentPrefix__, M.ComponentPrefix);
                        for (int i = 0; i < codeFile.Content.Count; i++)
                        {
                            codeFile.Content[i] = codeFile.Content[i].Replace(__projectNamespace__, projectNamespace);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__componentPrefix__, M.ComponentPrefix);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__moduleNamespace__, M.NameSpace);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__primaryKeyType__, M.PkType);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__frameworkNamespace__, projectProfile.FrameworkNamespaces);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__dbContextNamespace__, projectProfile.DbContextNamespaces);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__featureClaim__, M.FeatureClaim);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__name__, projectProfile.Name);
                        }
                        var fileName = Path.Combine(projectPath, codeFile.FileName);
                        var dir = Path.GetDirectoryName(fileName);
                        if (!Directory.Exists(dir))
                        {
                            Directory.CreateDirectory(dir);
                        }

                        await File.WriteAllLinesAsync(fileName, codeFile.Content, System.Text.Encoding.UTF8);
                    }
                }

                if (M.GenerateForm)
                {
                    foreach (var codeFile in CodeCofig.ProjectFiles[project.ProjectType].Where(x => x.FileName.Contains("Form")))
                    {
                        codeFile.FileName = codeFile.FileName.Replace(__componentPrefix__, M.ComponentPrefix);
                        for (int i = 0; i < codeFile.Content.Count; i++)
                        {
                            codeFile.Content[i] = codeFile.Content[i].Replace(__projectNamespace__, projectNamespace);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__componentPrefix__, M.ComponentPrefix);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__moduleNamespace__, M.NameSpace);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__primaryKeyType__, M.PkType);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__frameworkNamespace__, projectProfile.FrameworkNamespaces);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__dbContextNamespace__, projectProfile.DbContextNamespaces);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__featureClaim__, M.FeatureClaim);
                            codeFile.Content[i] = codeFile.Content[i].Replace(__name__, projectProfile.Name);
                        }
                        var fileName = Path.Combine(projectPath, codeFile.FileName);
                        var dir = Path.GetDirectoryName(fileName);
                        if (!Directory.Exists(dir))
                        {
                            Directory.CreateDirectory(dir);
                        }

                        await File.WriteAllLinesAsync(fileName, codeFile.Content, System.Text.Encoding.UTF8);
                    }
                }

                if (project.ProjectType == ProjectType.ClientDependency)
                {
                    string filePath = Path.Combine(M.BasePath, project.Path, $"{CodeCofig.ProjectFiles[project.ProjectType].FirstOrDefault().FileName}");
                    if (File.Exists(filePath))
                    {
                        if (M.GenerateForm)
                        {
                            var serviceInjectionNamespace = $@"using {projectProfile.Name}.ServiceContracts.Features.{M.NameSpace};
                                                               using {projectProfile.Name}.Razor.Features.{M.NameSpace}; 
                                                               //##NewServiceNamespace##";
                            var serviceInjection = $"services.AddScoped<I{M.ComponentPrefix}FormDataService, {M.ComponentPrefix}ClientSideFormDataService>(); \n //##NewService##";

                            string fileContent = File.ReadAllText(filePath);
                            if (!fileContent.Contains($"using {projectProfile.Name}.ServiceContracts.Features.{M.NameSpace};") &&
                                    !fileContent.Contains($"using {projectProfile.Name}.Razor.Features.{M.NameSpace};"))
                            {
                                fileContent = fileContent.Replace("//##NewServiceNamespace##", serviceInjectionNamespace);
                            }
                            fileContent = fileContent.Replace("//##NewService##", serviceInjection);

                            File.WriteAllText(filePath, fileContent);
                        }

                        if (M.GenerateListing)
                        {
                            var serviceInjectionNamespace = $@"using {projectProfile.Name}.ServiceContracts.Features.{M.NameSpace};
                                                               using {projectProfile.Name}.Razor.Features.{M.NameSpace};
                                                               //##NewServiceNamespace##";

                            var serviceInjection = $"services.AddScoped<I{M.ComponentPrefix}ListingDataService, {M.ComponentPrefix}ClientSideListingDataService>(); \n //##NewService##";

                            string fileContent = File.ReadAllText(filePath);
                            if (!fileContent.Contains($"using {projectProfile.Name}.ServiceContracts.Features.{M.NameSpace};") &&
                                  !fileContent.Contains($"using {projectProfile.Name}.Razor.Features.{M.NameSpace};"))
                            {
                                fileContent = fileContent.Replace("//##NewServiceNamespace##", serviceInjectionNamespace);
                            }
                            fileContent = fileContent.Replace("//##NewService##", serviceInjection);
                            File.WriteAllText(filePath, fileContent);
                        }
                    }
                }
                if (project.ProjectType == ProjectType.ServerSideDependency)
                {
                    string filePath = Path.Combine(M.BasePath, project.Path, $"{CodeCofig.ProjectFiles[project.ProjectType].FirstOrDefault().FileName}");
                    if (File.Exists(filePath))
                    {
                        if (M.GenerateForm)
                        {
                            var serviceInjectionNamespace = $@"using {projectProfile.Name}.ServiceContracts.Features.{M.NameSpace};
                                                               using {projectProfile.Name}.Server.DataServices.Features.{M.NameSpace}; 
                                                               //##NewServiceNamespace##";

                            var serviceInjection = $"services.AddScoped<I{M.ComponentPrefix}FormDataService, {M.ComponentPrefix}ServerSideFormDataService>(); \n //##NewService##";

                            string fileContent = File.ReadAllText(filePath);
                            if (!fileContent.Contains($"using {projectProfile.Name}.ServiceContracts.Features.{M.NameSpace};"))
                            {
                                fileContent = fileContent.Replace("//##NewServiceNamespace##", serviceInjectionNamespace);
                            }
                            fileContent = fileContent.Replace("//##NewService##", serviceInjection);
                            File.WriteAllText(filePath, fileContent);
                        }

                        if (M.GenerateListing)
                        {
                            var serviceInjectionNamespace = $@"using {projectProfile.Name}.ServiceContracts.Features.{M.NameSpace};
                                                               using {projectProfile.Name}.Server.DataServices.Features.{M.NameSpace};
                                                               //##NewServiceNamespace##";

                            var serviceInjection = $"services.AddScoped<I{M.ComponentPrefix}ListingDataService, {M.ComponentPrefix}ServerSideListingDataService>(); \n //##NewService##";

                            string fileContent = File.ReadAllText(filePath);
                            if (!fileContent.Contains($"using {projectProfile.Name}.ServiceContracts.Features.{M.NameSpace};"))
                            {
                                fileContent = fileContent.Replace("//##NewServiceNamespace##", serviceInjectionNamespace);
                            }
                            fileContent = fileContent.Replace("//##NewService##", serviceInjection);
                            File.WriteAllText(filePath, fileContent);
                        }
                    }
                }
            }
            NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Success, Summary = "Code Generated ", Duration = 5000, Style = "padding: 20px;background-color: #66ff99;color: white;width: auto;" });
        }

 
    }
    public class FormViewModel
    {
        // public bool ValidationForCMS { get; set; }
        public FormViewModel()
        {
        }

        [Required]
        public string? BasePath { get; set; }

        [Required]
        public string? DirectoryName { get; set; }

        public string NameSpace { get; set; }

        [Required]
        public string? ComponentPrefix { get; set; }

        public bool GenerateListing { get; set; }

        public bool GenerateControllerAndClientService { get; set; } = true;

        public bool GenerateForm { get; set; }

        [Required]
        public string? PkType { get; set; } = "int";

        public ProjectEnumType Project { get; set; }

        public string? FeatureClaim { get; set; } = "SystemDefault";
    }

    public enum ProjectEnumType
    {
        Portal = 1
    }

    public class CodeCofig
    {
        public CodeCofig()
        {
            Profiles = new();
            ProjectFiles = new();
        }
        public List<CodeProfile> Profiles { get; set; }
        public Dictionary<ProjectType, List<CodeFile>> ProjectFiles { get; set; }
    }

    public class CodeProfile
    {
        public CodeProfile()
        {
            Name = string.Empty;
            FrameworkNamespaces = string.Empty;
            DbContextNamespaces = string.Empty;
        }
        public string Name { get; set; }
        public string FrameworkNamespaces { get; set; }

        public string DbContextNamespaces { get; set; }

        public List<Project>? Projects { get; set; }

    }

    public class Project
    {
        public string? Path { get; set; }

        public string? NameSpace { get; set; }

        public ProjectType ProjectType { get; set; }
    }

    public class CodeFile
    {
        public string? FileName { get; set; }

        public FileCategory FileCategory { get; set; }

        public List<string> Content { get; set; } = new List<string>();
    }
}