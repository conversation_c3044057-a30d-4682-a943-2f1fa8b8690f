﻿using System.Security.Claims;

namespace PremierAutoTag.Framework.Core
{
    public interface IAuthenticatedUser
    {

        public string UserId { get; set; }
        public string? ProfileName { get; set; }
        public string ImageUrl { get; set; }
        public double TimeZoneOffset { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Username { get; set; }

    }

    public interface IAuthenticatedUserId
    {
        public string UserId { get; }

        Task<string> GetCurrentUserIdAsync()
        {
            return Task.FromResult(UserId);
        }

    }

    public static class ClaimsPrincipleExtensions
    {
        public static string GetUserId(this ClaimsPrincipal principal)
        {
            var claim = principal?.FindFirst("sub") ?? principal?.FindFirst(ClaimTypes.NameIdentifier);

            return claim?.Value ?? "";
        }

        public static string? GetPhone(this ClaimsPrincipal principal)
        {
            var claim = principal.FindFirst("Phone");
            if (claim is not null && !string.IsNullOrEmpty(claim.Value))
            {
                return claim.Value;
            }
            return string.Empty;
        }
        public static string? GetProfileName(this ClaimsPrincipal principal)
        {
            var claim = principal.FindFirst("ProfileName");
            if (claim is not null && !string.IsNullOrEmpty(claim.Value))
            {
                return claim.Value;
            }
            return string.Empty;
        }
        public static string? GetEmail(this ClaimsPrincipal principal)
        {
            var claim = principal?.FindFirst(ClaimTypes.Email);
            if (claim is not null && !string.IsNullOrEmpty(claim.Value))
            {
                return claim.Value;
            }
            return string.Empty;
        }


        public static string? GetUserName(this ClaimsPrincipal principal)
        {
            var claim = principal.FindFirst(ClaimTypes.Name);
            if (claim is not null && !string.IsNullOrEmpty(claim.Value))
            {
                return claim.Value;
            }
            return string.Empty;
        }
    }
}
