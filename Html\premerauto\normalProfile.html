<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link href="./css/output.css" rel="stylesheet">

    <script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
</head>

<body class="!bg-white">
    <header class="bg-white w-full fixed top-0 border-b border-b-gray-m-200 z-50">
        <nav class="flex items-center justify-between md:px-12 lg:px-16  p-4 mx-auto ">

            <div class="flex ">
                <a class="-m-1.5 p-1.5" href="/html/premerauto/index.html"><span class="sr-only">Your Company</span>
                    <img alt="" loading="lazy" decoding="async" class="h-9 lg:h-14" src="images/logo.png">
                </a>
            </div>

            <ul class="items-center  justify-center flex-grow hidden gap-2 xl:gap-6 lg:flex ">
                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="">Home</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="aboutus.html">About us</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="locations.html">Locations/Hours</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="FAQ.html">FAQ</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="tips.html">DMV Tips</a>
                </li>
                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="contact.html">Contact</a>
                </li>
            </ul>

            <div class="flex items-center gap-3">
                <div class="flex items-center gap-3">
                    <a href="login.html"
                        class="bg-white  py-1.5 md:py-2.5 px-3 md:px-4 hover:bg-gray-50 text-gray-900 text-sm md:text-sm font-medium hover:border hover:border-gray-300 border border-transparent   rounded-lg">Log
                        In</a>
                    <a href="register.html" class="rounded-lg text-white bg-primary hover:bg-primaryDark px-5 py-2.5">
                        Register
                    </a>
                </div>
                <div class="flex lg:hidden"><button id="mobileMenuBtn" type="button"
                        class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"><span
                            class="sr-only">Open main menu</span><svg class="w-6 h-6" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
                        </svg></button></div>
            </div>
        </nav>
        <div class="hidden lg:hidden" id="mobileMenu" role="dialog" aria-modal="true">
            <div class="fixed inset-0 z-10 hidden bg-white opacity-50"></div>
            <div
                class="fixed inset-y-0 right-0 z-20 w-full p-4 overflow-y-auto bg-white md:px-6 md:py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
                <div class="flex items-center justify-between pt-1">
                    <a href="#" class="">
                        <span class="sr-only">Your
                            Company</span>
                        <img alt="" loading="lazy" width="194" height="50" decoding="async" class="w-auto h-9"
                            style="color:transparent" src="images/logo.png">
                    </a>
                    <button type="button" id="mobileMenuBtn2" class=" -m-2.5 rounded-md p-2.5 text-gray-700"><span
                            class="sr-only">Close
                            menu</span><svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                        </svg></button>
                </div>
                <div class="flow-root mt-6">
                    <div class="-my-6 divide-y divide-gray-500/10">
                        <div class="py-6 space-y-1">
                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="/">Home</a>
                            <a href="aboutus.html"
                                class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100">About
                                Us</a>
                            <a href="locations.html"
                                class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100">Locations/Hours</a>
                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="FAQ.html">FAQ</a>
                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="tips.html">DMV Tips</a>


                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="contactUs.html">Contact</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <section class="bg-gray-m-800">
        <div class="pt-24 md:pt-44 container mx-auto pb-12 md:pb-24 px-4 md:px-auto">
            <h2 class="font-semibold text-2xl md:text-4xl text-gray-m-25">Welcome Susan,</h2>
        </div>
    </section>

    <session>
        <div
            class="bg-white container mx-auto -mt-6 md:-mt-16 w-full flex flex-col gap-3  md:gap-4 rounded-3xl p-4 md:p-8 border border-gray-200">
            <section class="flex gap-8">

                <div class="w-full flex md:flex-row flex-col gap-8 mx-auto">
                    <!-- Tab Buttons -->
                    <div class="flex flex-row md:flex-col min-w-52 gap-2" id="tabs">
                        <button
                            class="tab-button whitespace-nowrap p-2 text-sm md:text-base md:px-3 md:py-2.5 cursor-pointer flex items-center gap-1 md:gap-2 text-white rounded-lg bg-primary "
                            data-tab="1">
                            <svg class="hidden md:block" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="6" r="4" stroke="currentColor" stroke-width="1.5" />
                                <ellipse cx="12" cy="17" rx="7" ry="4" stroke="currentColor" stroke-width="1.5" />
                            </svg>
                            <span class="hidden md:block">My</span> Profile


                        </button>
                        <button
                            class="tab-button whitespace-nowrap p-2 text-sm md:text-base md:px-3 md:py-2.5 cursor-pointer flex items-center gap-1 md:gap-2 rounded-lg "
                            data-tab="2">
                            <svg class="hidden md:block" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M7.75158 19.3645V18.7457C8.61038 18.7501 9.76232 18.7501 11.3484 18.75H12.6524C14.2385 18.7501 15.3896 18.7501 16.2484 18.7457V19.3645C16.2483 19.7821 16.2543 20.1322 16.3201 20.414C16.3963 20.8491 16.8412 21.7222 18.0105 21.7346C18.2738 21.7495 18.7075 21.7501 19.4723 21.7501C20.2524 21.7501 20.6881 21.7501 20.9535 21.7341C22.4115 21.7213 22.7242 20.6947 22.6983 20.183C22.7428 19.8511 22.7449 19.3497 22.7461 18.5471L22.749 13.4832C22.7547 12.7997 22.7365 12.1297 22.672 11.7779C22.5061 10.873 21.8214 10.085 20.9358 9.47891L21.7373 9.21172C22.1303 9.08073 22.3427 8.65599 22.2117 8.26303C22.0807 7.87008 21.656 7.65771 21.263 7.78869L20.0467 8.19414L18.907 4.28042C18.9001 4.25667 18.892 4.23328 18.8828 4.21033C18.6906 3.73242 18.3044 3.30803 17.743 3.11709C14.3434 1.96097 9.65693 1.96097 6.25737 3.11709C5.69594 3.30803 5.30969 3.73242 5.11755 4.21033C5.10832 4.23328 5.10023 4.25667 5.09332 4.28042L3.95364 8.19426L2.73694 7.78869C2.34398 7.65771 1.91924 7.87008 1.78825 8.26303C1.65727 8.65599 1.86964 9.08073 2.2626 9.21172L3.0642 9.47892C2.17857 10.085 1.49388 10.873 1.32799 11.7779C1.26349 12.1297 1.24531 12.7997 1.25093 13.4832L1.25388 18.5471C1.25506 19.3497 1.25717 19.8511 1.30159 20.183C1.32042 20.6947 1.69573 21.7212 3.04642 21.7341C3.31187 21.7501 3.74749 21.7501 4.52765 21.7501C5.2924 21.7501 5.72612 21.7495 5.98939 21.7346C7.24841 21.7222 7.64094 20.8491 7.67982 20.414C7.74568 20.1322 7.75161 19.7821 7.75158 19.3645ZM5.41361 8.54566L6.52086 4.74322C6.57084 4.6365 6.64842 4.56847 6.74033 4.53722C9.82672 3.48759 14.1736 3.4876 17.26 4.53722C17.3519 4.56847 17.4295 4.6365 17.4795 4.74321L18.5867 8.54574C17.5659 8.33846 16.5299 8.30391 15.6722 8.27542C15.0947 8.25402 13.5761 8.24734 12 8.25091C10.4239 8.24734 8.90531 8.25402 8.3278 8.27542C7.4702 8.30391 6.4343 8.33845 5.41361 8.54566ZM2.78833 19.984C2.76049 19.776 2.75537 18.8884 2.7541 18.0324C3.20418 18.2345 3.79251 18.4106 4.41562 18.53C4.74934 18.583 5.60982 18.7085 6.25158 18.7262L6.24261 19.8591C6.24796 19.9818 6.18786 20.2291 5.90464 20.237C5.69535 20.2488 5.31792 20.2501 4.52765 20.2501C3.71785 20.2501 3.34427 20.2493 3.13676 20.2368C2.91816 20.2296 2.81339 20.0652 2.78833 19.984ZM19.5844 18.53C19.2507 18.583 18.3902 18.7085 17.7484 18.7262C17.7483 18.9423 17.7508 19.7381 17.7573 19.8591C17.7586 19.9818 17.8279 20.2293 18.0953 20.237C18.3046 20.2488 18.682 20.2501 19.4723 20.2501C20.2821 20.2501 20.6557 20.2493 20.8632 20.2368C21.085 20.2368 21.1879 20.0682 21.2116 19.984C21.2395 19.776 21.2447 18.8883 21.2459 18.0324C20.7958 18.2345 20.2075 18.4106 19.5844 18.53ZM8.12757 9.78374C8.66943 9.76365 10.4118 9.74739 11.9983 9.75099C13.5847 9.74739 15.3305 9.76365 15.8723 9.78374C17.5098 9.84235 18.871 9.89108 20.0707 10.7047C20.4325 10.95 20.694 11.1931 20.8744 11.4181L19.7626 11.7887C19.3696 11.9197 19.1573 12.3444 19.2883 12.7374C19.4192 13.1303 19.844 13.3427 20.2369 13.2117L21.2466 12.8752C21.2498 13.0538 21.2508 13.254 21.2491 13.471C21.2435 14.1505 21.2117 14.9212 21.1566 15.5521C21.1449 15.8642 21.0164 16.5268 20.5961 16.6802C20.2849 16.8199 19.4293 17.0307 18.879 17.1362C18.4801 17.2032 18.0745 17.2308 16.7044 17.2421C16.7019 17.2352 16.6993 17.2283 16.6965 17.2215L16.437 16.5725C16.2662 16.1456 16.1162 15.7704 15.9577 15.4732C15.7862 15.1516 15.5738 14.8587 15.246 14.6369C14.9183 14.415 14.5675 14.3265 14.2052 14.2867C13.8704 14.25 13.4663 14.25 13.0065 14.25H10.9938C10.534 14.25 10.1299 14.25 9.7951 14.2867C9.4328 14.3265 9.08203 14.415 8.7543 14.6369C8.42658 14.8587 8.21417 15.1516 8.04267 15.4732C7.88418 15.7704 7.73413 16.1456 7.56338 16.5725L7.30381 17.2215L7.29621 17.2413C5.95995 17.2294 5.50989 17.2015 5.12093 17.1362C4.5706 17.0307 3.71504 16.8199 3.40385 16.6802C3.02498 16.5415 2.87986 15.9706 2.8433 15.5521C2.7882 14.9212 2.75644 14.1505 2.75085 13.471C2.74906 13.2539 2.75006 13.0538 2.75332 12.8752L3.763 13.2117C4.15596 13.3427 4.5807 13.1303 4.71169 12.7374C4.84267 12.3444 4.6303 11.9197 4.23734 11.7887L3.12552 11.4181C3.30592 11.1931 3.56746 10.95 3.92924 10.7047C5.12889 9.89108 6.4901 9.84235 8.12757 9.78374ZM8.90854 17.2485C9.744 17.2501 10.781 17.2501 12.0797 17.2501H12.0859C13.3112 17.2501 14.2951 17.2501 15.0919 17.2488L15.0581 17.1644C14.8693 16.6924 14.7492 16.3949 14.6341 16.179C14.5277 15.9794 14.4583 15.915 14.4051 15.879C14.3519 15.8429 14.2663 15.8024 14.0415 15.7778C13.7983 15.751 13.4774 15.75 12.9691 15.75H11.0313C10.5229 15.75 10.2021 15.751 9.95885 15.7778C9.73407 15.8024 9.64845 15.8429 9.59525 15.879C9.54205 15.915 9.47265 15.9794 9.36625 16.179C9.25113 16.3949 9.131 16.6924 8.94219 17.1644L8.90854 17.2485Z"
                                    fill="currentColor" />
                            </svg>

                            <span class="hidden md:block">My</span> Orders
                        </button>
                        <button
                            class="tab-button whitespace-nowrap p-2 text-sm md:text-base md:px-3 md:py-2.5 cursor-pointer flex items-center gap-1 md:gap-2 rounded-lg "
                            data-tab="3">
                            <svg class="hidden md:block" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M12 14C11.4477 14 11 14.4477 11 15C11 15.5523 11.4477 16 12 16H12.009C12.5613 16 13.009 15.5523 13.009 15C13.009 14.4477 12.5613 14 12.009 14H12Z"
                                    fill="currentColor" />
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M6.75 6.5C6.75 3.6005 9.10051 1.25 12 1.25C14.8995 1.25 17.25 3.6005 17.25 6.5V9.29907C18.7871 10.7153 19.75 12.7452 19.75 15C19.75 19.2802 16.2802 22.75 12 22.75C7.71979 22.75 4.25 19.2802 4.25 15C4.25 12.7452 5.21293 10.7153 6.75 9.29907V6.5ZM15.75 6.5V8.21604C14.6388 7.60047 13.3603 7.25 12 7.25C10.6397 7.25 9.36123 7.60047 8.25 8.21604V6.5C8.25 4.42893 9.92893 2.75 12 2.75C14.0711 2.75 15.75 4.42893 15.75 6.5ZM12 8.75C8.54822 8.75 5.75 11.5482 5.75 15C5.75 18.4518 8.54822 21.25 12 21.25C15.4518 21.25 18.25 18.4518 18.25 15C18.25 11.5482 15.4518 8.75 12 8.75Z"
                                    fill="currentColor" />
                            </svg>
                            <span class="hidden md:block">Change</span>
                            Password
                        </button>
                        <button
                            class="tab-button whitespace-nowrap p-2 text-sm md:text-base md:px-3 md:py-2.5 cursor-pointer flex items-center gap-1 md:gap-2 rounded-lg "
                            data-tab="4">
                            <svg class="hidden md:block" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M2.00098 11.999L16.001 11.999M16.001 11.999L12.501 8.99902M16.001 11.999L12.501 14.999"
                                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path
                                    d="M9.00195 7C9.01406 4.82497 9.11051 3.64706 9.87889 2.87868C10.7576 2 12.1718 2 15.0002 2L16.0002 2C18.8286 2 20.2429 2 21.1215 2.87868C22.0002 3.75736 22.0002 5.17157 22.0002 8L22.0002 16C22.0002 18.8284 22.0002 20.2426 21.1215 21.1213C20.2429 22 18.8286 22 16.0002 22H15.0002C12.1718 22 10.7576 22 9.87889 21.1213C9.11051 20.3529 9.01406 19.175 9.00195 17"
                                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                            </svg>

                            Logout
                        </button>
                    </div>

                    <!-- Tab Content -->
                    <div class="w-full">
                        <div class="tab-content block w-full" data-content="1">
                            <h2 class="font-semibold text-lg md:text-xl text-gray-m-800">
                                My Profile
                            </h2>
                            <hr class="my-4 text-gray-200" />

                            <div class="grid grid-cols-12 gap-6 items-center">
                                <div class="col-span-4 md:col-span-3">
                                    <span class="text-sm font-medium text-gray-700">First Name</span>
                                </div>
                                <div class="md:col-span-7 col-span-8">
                                    <input value="Susan" readonly type="text"
                                        class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                        placeholder="" />
                                </div>
                                <div class="col-span-4 md:col-span-3">
                                    <span class="text-sm font-medium text-gray-700">Last Name</span>
                                </div>
                                <div class="md:col-span-7 col-span-8">
                                    <input value="Smith" readonly type="text"
                                        class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                        placeholder="" />
                                </div>
                                <div class="col-span-4 md:col-span-3">
                                    <span class="text-sm font-medium text-gray-700">Phone <span
                                            class="hidden md:inline-block">Number</span></span>
                                </div>
                                <div class="md:col-span-7 col-span-8">
                                    <input value="+****************" readonly type="text"
                                        class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                        placeholder="" />
                                </div>
                                <div class="col-span-4 md:col-span-3">
                                    <span class="text-sm font-medium text-gray-700">Email <span
                                            class="hidden md:inline-block">Address</span></span>
                                </div>
                                <div class="md:col-span-7 col-span-8">
                                    <input value="<EMAIL>" disabled readonly type="email"
                                        class="w-full py-2 md:py-2.5 border focus:ring-1 disabled:bg-seaShell disabled:text-gray-400 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                        placeholder="" />
                                </div>
                            </div>

                            <hr class="my-4 text-gray-200" />

                            <div class="flex flex-row gap-3 justify-end items-center">
                                <button
                                    class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg">Cancel</button>
                                <button
                                    class="px-4 py-2 rounded-lg bg-primary hover:bg-primaryDark text-white">Save</button>
                            </div>
                        </div>
                        <div class="tab-content hidden" data-content="2">
                            <h2 class="font-semibold text-lg md:text-xl text-gray-m-800">
                                Orders
                            </h2>
                            <div class="py-4 flex md:flex-row flex-col items-center gap-4 justify-between">
                                <div
                                    class="flex items-center py-2.5 px-4 rounded-lg border border-gray-200 w-full md:w-fit gap-2">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M17.5 8.33342H2.5M13.3333 1.66675V5.00008M6.66667 1.66675V5.00008M6.5 18.3334H13.5C14.9001 18.3334 15.6002 18.3334 16.135 18.0609C16.6054 17.8212 16.9878 17.4388 17.2275 16.9684C17.5 16.4336 17.5 15.7335 17.5 14.3334V7.33342C17.5 5.93328 17.5 5.23322 17.2275 4.69844C16.9878 4.22803 16.6054 3.84558 16.135 3.6059C15.6002 3.33341 14.9001 3.33341 13.5 3.33341H6.5C5.09987 3.33341 4.3998 3.33341 3.86502 3.6059C3.39462 3.84558 3.01217 4.22803 2.77248 4.69844C2.5 5.23322 2.5 5.93328 2.5 7.33341V14.3334C2.5 15.7335 2.5 16.4336 2.77248 16.9684C3.01217 17.4388 3.39462 17.8212 3.86502 18.0609C4.3998 18.3334 5.09987 18.3334 6.5 18.3334Z"
                                            stroke="#A4A7AE" stroke-width="1.66667" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                    <input type="text" class="outline-none font-semibold text-sm text-brightGray"
                                        name="dates" value="Jan 10, 2025 – Jan 16, 2025" />
                                </div>

                                <div
                                    class="flex items-center py-2.5 px-4 rounded-lg border border-gray-200 w-full md:w-fit gap-2">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M17.5 17.5L14.5834 14.5833M16.6667 9.58333C16.6667 13.4954 13.4954 16.6667 9.58333 16.6667C5.67132 16.6667 2.5 13.4954 2.5 9.58333C2.5 5.67132 5.67132 2.5 9.58333 2.5C13.4954 2.5 16.6667 5.67132 16.6667 9.58333Z"
                                            stroke="#A4A7AE" stroke-width="1.66667" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>

                                    <input placeholder="Search" type="search"
                                        class="outline-none font-semibold text-sm text-brightGray" />
                                </div>
                            </div>

                            <div class=' relative overflow-x-auto border border-slate-100 sm:rounded-xl'>
                                <table class='w-full text-sm text-left text-gray-500 rtl:text-right'>
                                    <thead class='text-xs text-gray-600  bg-gray-50'>
                                        <tr>
                                            <th scope='col' width="20%" class='px-6 py-3 !font-medium'>
                                                Staff
                                            </th>
                                            <th scope='col' width="20%" class='px-6 py-3 !font-medium'>
                                                Phone No.
                                            </th>
                                            <th scope='col' width="15%" class='px-6 py-3 !font-medium'>
                                                Status
                                            </th>
                                            <th scope='col' width="20%" class='px-6 py-3 !font-medium'>
                                                Message
                                            </th>
                                            <th scope='col' width="40%" class='px-6 py-3 !font-medium'>
                                                Applied at
                                            </th>

                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr
                                            class='bg-white cursor-pointer items-center border-b last:border-b-0 border-b-gray-100 hover:bg-slate-50'>
                                            <td scope='row'
                                                class="flex text-xs items-center px-6 py-4 text-gray-600 whitespace-nowrap">
                                                33423
                                            </td>
                                            <td class='px-6 py-4'>
                                                <div>
                                                    <span
                                                        class="text-gray-600 whitespace-nowrap text-sm before:w-1 before:h-1 before:bg-gray-600 flex items-center gap-2 before:rounded-full before:block before:content-'">Car
                                                        Registration</span>
                                                    <span
                                                        class="text-gray-600 text-sm before:w-1 whitespace-nowrap before:h-1 before:bg-gray-600 flex items-center gap-2 before:rounded-full before:block before:content-'">Title
                                                        Registration</span>
                                                </div>
                                            </td>
                                            <td class='px-6 py-4'>
                                                <span
                                                    class="text-warning-700 py-1 rounded-full border border-warning-200 bg-warning-50  w-fit px-2 text-sm before:w-2 font-semibold before:h-2 before:bg-warning-500 flex items-center gap-2 before:rounded-full before:block before:content-'">Pending</span>
                                            </td>
                                            <td class='px-6 py-4'>
                                                <span class="text-sm whitespace-nowrap text-gray-600">May 17,
                                                    2025</span>
                                            </td>
                                            <td class='px-6 py-4'>
                                                <div class="flex items-center gap-2">
                                                    <button
                                                        class="py-2 px-4 whitespace-nowrap bg-white hover-bg-gray-100 cursor-pointer rounded-lg border border-gray-200 text-sm font-semibold text-brightGray">View
                                                        Details</button>
                                                    <button
                                                        class="py-2 px-4 whitespace-nowrap bg-white hover-bg-gray-100 cursor-pointer rounded-lg border border-gray-200 text-sm font-semibold text-brightGray">Message</button>
                                                </div>
                                            </td>

                                        </tr>
                                        <tr
                                            class='bg-white cursor-pointer items-center border-b last:border-b-0 border-b-gray-100 hover:bg-slate-50'>
                                            <td scope='row'
                                                class="flex text-xs items-center px-6 py-4 text-gray-600 whitespace-nowrap">
                                                33423
                                            </td>
                                            <td class='px-6 py-4'>
                                                <div>
                                                    <span
                                                        class="text-gray-600 whitespace-nowrap text-sm before:w-1 before:h-1 before:bg-gray-600 flex items-center gap-2 before:rounded-full before:block before:content-'">Car
                                                        Registration</span>
                                                    <span
                                                        class="text-gray-600 whitespace-nowrap text-sm before:w-1 before:h-1 before:bg-gray-600 flex items-center gap-2 before:rounded-full before:block before:content-'">Title
                                                        Registration</span>
                                                </div>
                                            </td>
                                            <td class='px-6 py-4'>
                                                <span
                                                    class="text-green-700 py-1 rounded-full border border-green-200 bg-green-50  w-fit px-2 text-sm before:w-2 font-semibold before:h-2 before:bg-green-500 flex items-center gap-2 before:rounded-full before:block before:content-'">Compete</span>
                                            </td>
                                            <td class='px-6 py-4'>
                                                <span class="text-sm whitespace-nowrap text-gray-600">May 17,
                                                    2025</span>
                                            </td>
                                            <td class='px-6 py-4'>
                                                <div class="flex items-center gap-2">
                                                    <button
                                                        class="py-2 px-4 whitespace-nowrap bg-white hover-bg-gray-100  cursor-pointer rounded-lg border border-gray-200 text-sm font-semibold text-brightGray">View
                                                        Details</button>
                                                    <button
                                                        class="py-2 px-4 whitespace-nowrap bg-white hover-bg-gray-100 rounded-lg cursor-pointer border border-gray-200 text-sm font-semibold text-brightGray">Message</button>
                                                </div>
                                            </td>

                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-content hidden" data-content="3">
                            <h2 class="font-semibold text-xl text-gray-m-800">
                                Change Password
                            </h2>

                            <hr class="my-4 text-gray-200" />
                            <div class="grid grid-cols-12 gap-6 items-center">
                                <div class="col-span-4 md:col-span-3">
                                    <span class="text-sm font-medium text-gray-700">Current <span
                                            class="hidden md:inline-block">password</span></span>
                                </div>
                                <div class="md:col-span-7 col-span-8">
                                    <div class="relative flex items-center justify-end">
                                        <button class="absolute p-2 cursor-pointer hover:bg-gray-100 rounded-lg me-2">
                                            <svg width="17" height="13" viewBox="0 0 17 13" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M8.875 1.9375C7.07031 1.9375 5.62109 2.75781 4.5 3.79688C3.43359 4.78125 2.72266 5.92969 2.33984 6.75C2.72266 7.57031 3.43359 8.74609 4.5 9.73047C5.62109 10.7695 7.07031 11.5625 8.875 11.5625C10.6523 11.5625 12.1016 10.7695 13.2227 9.73047C14.2891 8.74609 15.0273 7.57031 15.3828 6.75C15.0273 5.92969 14.2891 4.78125 13.25 3.79688C12.1016 2.75781 10.6523 1.9375 8.875 1.9375ZM3.59766 2.83984C4.88281 1.63672 6.66016 0.625 8.875 0.625C11.0625 0.625 12.8398 1.63672 14.125 2.83984C15.4102 4.04297 16.2578 5.4375 16.668 6.42188C16.75 6.64062 16.75 6.88672 16.668 7.10547C16.2578 8.0625 15.4102 9.48438 14.125 10.6875C12.8398 11.8906 11.0625 12.875 8.875 12.875C6.66016 12.875 4.88281 11.8906 3.59766 10.6875C2.3125 9.48438 1.46484 8.0625 1.05469 7.10547C0.972656 6.88672 0.972656 6.64062 1.05469 6.42188C1.46484 5.4375 2.3125 4.01562 3.59766 2.83984ZM8.875 8.9375C10.0781 8.9375 11.0625 7.98047 11.0625 6.75C11.0625 5.54688 10.0781 4.5625 8.875 4.5625C8.84766 4.5625 8.82031 4.5625 8.82031 4.5625C8.84766 4.72656 8.875 4.86328 8.875 5C8.875 5.98438 8.08203 6.75 7.125 6.75C6.96094 6.75 6.82422 6.75 6.6875 6.69531C6.6875 6.72266 6.6875 6.75 6.6875 6.75C6.6875 7.98047 7.64453 8.9375 8.875 8.9375ZM8.875 3.25C10.1055 3.25 11.2539 3.93359 11.8828 5C12.5117 6.09375 12.5117 7.43359 11.8828 8.5C11.2539 9.59375 10.1055 10.25 8.875 10.25C7.61719 10.25 6.46875 9.59375 5.83984 8.5C5.21094 7.43359 5.21094 6.09375 5.83984 5C6.46875 3.93359 7.61719 3.25 8.875 3.25Z"
                                                    fill="#737373" />
                                            </svg>
                                        </button>

                                        <input value="Susan" readonly type="password"
                                            class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                            placeholder="" />
                                    </div>
                                </div>

                            </div>

                            <hr class="my-4 text-gray-200" />
                            <div class="grid grid-cols-12 gap-6 items-center">
                                <div class="col-span-4 md:col-span-3">
                                    <span class="text-sm font-medium text-gray-700">New <span
                                            class="hidden md:inline-block">password</span></span>
                                </div>
                                <div class="md:col-span-7 col-span-8">
                                    <div class="relative flex items-center justify-end">
                                        <button class="absolute p-2 cursor-pointer hover:bg-gray-100 rounded-lg me-2">
                                            <svg width="17" height="13" viewBox="0 0 17 13" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M8.875 1.9375C7.07031 1.9375 5.62109 2.75781 4.5 3.79688C3.43359 4.78125 2.72266 5.92969 2.33984 6.75C2.72266 7.57031 3.43359 8.74609 4.5 9.73047C5.62109 10.7695 7.07031 11.5625 8.875 11.5625C10.6523 11.5625 12.1016 10.7695 13.2227 9.73047C14.2891 8.74609 15.0273 7.57031 15.3828 6.75C15.0273 5.92969 14.2891 4.78125 13.25 3.79688C12.1016 2.75781 10.6523 1.9375 8.875 1.9375ZM3.59766 2.83984C4.88281 1.63672 6.66016 0.625 8.875 0.625C11.0625 0.625 12.8398 1.63672 14.125 2.83984C15.4102 4.04297 16.2578 5.4375 16.668 6.42188C16.75 6.64062 16.75 6.88672 16.668 7.10547C16.2578 8.0625 15.4102 9.48438 14.125 10.6875C12.8398 11.8906 11.0625 12.875 8.875 12.875C6.66016 12.875 4.88281 11.8906 3.59766 10.6875C2.3125 9.48438 1.46484 8.0625 1.05469 7.10547C0.972656 6.88672 0.972656 6.64062 1.05469 6.42188C1.46484 5.4375 2.3125 4.01562 3.59766 2.83984ZM8.875 8.9375C10.0781 8.9375 11.0625 7.98047 11.0625 6.75C11.0625 5.54688 10.0781 4.5625 8.875 4.5625C8.84766 4.5625 8.82031 4.5625 8.82031 4.5625C8.84766 4.72656 8.875 4.86328 8.875 5C8.875 5.98438 8.08203 6.75 7.125 6.75C6.96094 6.75 6.82422 6.75 6.6875 6.69531C6.6875 6.72266 6.6875 6.75 6.6875 6.75C6.6875 7.98047 7.64453 8.9375 8.875 8.9375ZM8.875 3.25C10.1055 3.25 11.2539 3.93359 11.8828 5C12.5117 6.09375 12.5117 7.43359 11.8828 8.5C11.2539 9.59375 10.1055 10.25 8.875 10.25C7.61719 10.25 6.46875 9.59375 5.83984 8.5C5.21094 7.43359 5.21094 6.09375 5.83984 5C6.46875 3.93359 7.61719 3.25 8.875 3.25Z"
                                                    fill="#737373" />
                                            </svg>
                                        </button>

                                        <input value="Susan" readonly type="password"
                                            class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                            placeholder="" />
                                    </div>
                                </div>

                            </div>

                            <hr class="my-4 text-gray-200" />
                            <div class="grid grid-cols-12 gap-6 items-center">
                                <div class="col-span-4 md:col-span-3">
                                    <span class="text-sm font-medium text-gray-700">Confirm new <span
                                            class="hidden md:inline-block">password</span></span>
                                </div>
                                <div class="md:col-span-7 col-span-8">
                                    <div class="relative flex items-center justify-end">
                                        <button class="absolute p-2 cursor-pointer hover:bg-gray-100 rounded-lg me-2">
                                            <svg width="17" height="13" viewBox="0 0 17 13" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M8.875 1.9375C7.07031 1.9375 5.62109 2.75781 4.5 3.79688C3.43359 4.78125 2.72266 5.92969 2.33984 6.75C2.72266 7.57031 3.43359 8.74609 4.5 9.73047C5.62109 10.7695 7.07031 11.5625 8.875 11.5625C10.6523 11.5625 12.1016 10.7695 13.2227 9.73047C14.2891 8.74609 15.0273 7.57031 15.3828 6.75C15.0273 5.92969 14.2891 4.78125 13.25 3.79688C12.1016 2.75781 10.6523 1.9375 8.875 1.9375ZM3.59766 2.83984C4.88281 1.63672 6.66016 0.625 8.875 0.625C11.0625 0.625 12.8398 1.63672 14.125 2.83984C15.4102 4.04297 16.2578 5.4375 16.668 6.42188C16.75 6.64062 16.75 6.88672 16.668 7.10547C16.2578 8.0625 15.4102 9.48438 14.125 10.6875C12.8398 11.8906 11.0625 12.875 8.875 12.875C6.66016 12.875 4.88281 11.8906 3.59766 10.6875C2.3125 9.48438 1.46484 8.0625 1.05469 7.10547C0.972656 6.88672 0.972656 6.64062 1.05469 6.42188C1.46484 5.4375 2.3125 4.01562 3.59766 2.83984ZM8.875 8.9375C10.0781 8.9375 11.0625 7.98047 11.0625 6.75C11.0625 5.54688 10.0781 4.5625 8.875 4.5625C8.84766 4.5625 8.82031 4.5625 8.82031 4.5625C8.84766 4.72656 8.875 4.86328 8.875 5C8.875 5.98438 8.08203 6.75 7.125 6.75C6.96094 6.75 6.82422 6.75 6.6875 6.69531C6.6875 6.72266 6.6875 6.75 6.6875 6.75C6.6875 7.98047 7.64453 8.9375 8.875 8.9375ZM8.875 3.25C10.1055 3.25 11.2539 3.93359 11.8828 5C12.5117 6.09375 12.5117 7.43359 11.8828 8.5C11.2539 9.59375 10.1055 10.25 8.875 10.25C7.61719 10.25 6.46875 9.59375 5.83984 8.5C5.21094 7.43359 5.21094 6.09375 5.83984 5C6.46875 3.93359 7.61719 3.25 8.875 3.25Z"
                                                    fill="#737373" />
                                            </svg>
                                        </button>

                                        <input value="Susan" readonly type="password"
                                            class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                            placeholder="" />
                                    </div>
                                </div>

                            </div>

                            <hr class="my-4 text-gray-200" />

                            <div class="flex flex-row gap-3 justify-end items-center">
                                <button
                                    class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg">Cancel</button>
                                <button
                                    class="px-4 py-2 rounded-lg bg-primary hover:bg-primaryDark text-white">Save</button>
                            </div>

                        </div>
                        <!-- <div class="tab-content hidden" data-content="4">
                            <p>This is the content of Tab four.</p>
                        </div> -->
                    </div>
                </div>
            </section>
        </div>
    </session>

    <footer class="pt-8 md:pt-16 bg-gray-50 border-t border-t-gray-100 mt-auto mb-0">
        <div class="container px-2 mx-auto lg:px-0">
            <div class="grid grid-cols-12 px-2 lg:px-0">
                <div
                    class="flex flex-col items-center justify-center col-span-12 mb-6 md:mb-12 md:col-span-6 md:col-start-4">
                    <div class=" mb-6">
                        <a href="" class="flex items-center">
                            <img src="images/footerLogo.svg" class="h-8 md:h-12" alt="Logo">
                        </a>


                    </div>



                    <ul class="flex flex-wrap justify-center mb-2 gap-4 md:gap-10 ">
                        <li class="md:mb-4"><a href=""
                                class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">About
                                us</a>
                        </li>
                        <li class="md:mb-4"><a href="aboutus.html"
                                class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">Locations</a>
                        </li>
                        <li class="md:mb-4"><a href="times.html"
                                class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">Walk-in
                                Hours</a>
                        </li>

                        <li class=""><a href="contact.htmt"
                                class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">
                                Contact us
                            </a></li>
                        <li class=""><a href="tips.html"
                                class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">
                                DMV Tips
                            </a></li>
                        <li class=""><a href="FAQ.html"
                                class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">
                                FAQ
                            </a></li>
                    </ul>

                    <div>
                        <span class="text-gray-500 text-center block">© 2024 Premier Auto Tag Services, Inc. All rights
                            reserved.</span>
                    </div>
                </div>
            </div>

        </div>
    </footer>

</body>
<script>
    const Button = document.getElementById('mobileMenuBtn');
    const menu = document.getElementById('mobileMenu');
    const Button2 = document.getElementById('mobileMenuBtn2');

    Button.addEventListener('click', () => {
        menu.classList.toggle('hidden');
    });

    Button2.addEventListener('click', () => {
        menu.classList.toggle('hidden');
    });


    window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 80) {
            header.classList.add('!bg-white', 'shadow-xl', "fixed");
            header.classList.remove('absolute');
        } else {
            header.classList.remove('!bg-white', 'shadow-xl', 'fixed');
            header.classList.add('absolute');
        }
    });

</script>

<script>
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tab;

            // Remove active styles from all buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('text-white', '!bg-primary');
                btn.classList.add('bg-white', 'text-gray-500', 'hover:bg-gray-100');
            });

            // Hide all tab contents
            tabContents.forEach(content => content.classList.add('hidden'));

            // Show the selected tab content
            document.querySelector(`.tab-content[data-content="${tabId}"]`).classList.remove('hidden');

            // Highlight the active tab button
            button.classList.add('text-white', '!bg-primary', 'active');
        });
    });

    $('input[name="dates"]').daterangepicker();
</script>

</html>