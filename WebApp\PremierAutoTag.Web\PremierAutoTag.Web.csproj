<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-PremierAutoTag.Web-a915eea6-ed5e-489e-994f-3fa33568f0bd</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\PremierAutoTag.Server.DataServices\PremierAutoTag.Server.DataServices.csproj" />
    <ProjectReference Include="..\..\PremierAutoTag.Server.Data\PremierAutoTag.Server.Data.csproj" />
    <ProjectReference Include="..\PremierAutoTag.Web.Client\PremierAutoTag.Web.Client.csproj" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>



</Project>
