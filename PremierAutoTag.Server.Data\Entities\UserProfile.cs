﻿using Microsoft.EntityFrameworkCore;
using PremierAutoTag.Framework.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Server.Data.Entities
{
    [Index(nameof(Id))]
    public class UserProfile : BaseEntity
    {
        [Key]
        [StringLength(450)] // Match AspNetUsers.Id length
        public string Id { get; set; } = "";

        [StringLength(100)]
        public string? FirstName { get; set; }

        [StringLength(100)]
        public string? LastName { get; set; }

        [StringLength(150)]
        public string DealerName { get; set; } = "";

        [StringLength(50)]
        public string DealerId { get; set; } = "";

        [StringLength(50)]
        public string TaxId { get; set; } = "";
     
        public int StateId { get; set; } 

        public bool Subsidary { get; set; }

        [StringLength(150)]
        public string Address { get; set; } = "";

        [StringLength(20)]
        public string Phone { get; set; } = "";

        [StringLength(100)]
        public string AuthorizedCompany { get; set; } = "";

        [StringLength(100)]
        public string AdministrativeEmail { get; set; } = "";

        [StringLength(20)]
        public string AdministrativePhone { get; set; } = "";

       
        public DEALERSHIP_IOT_TYPES DEALERSHIP_IOT_TYPES { get; set; }

       
        public VEHICAL_ON_IOT_TYPES VEHICAL_ON_IOT_TYPES { get; set; }

       
        public VEHICAL_SOLD_IOT VEHICAL_SOLD_IOT { get; set; }

        public long? SubscriptionPlanId { get; set; }

        public virtual ApplicationUser ApplicationUser { get; set; } = null!;
        public virtual SubscriptionPlan? SubscriptionPlan { get; set; }

    }
}
