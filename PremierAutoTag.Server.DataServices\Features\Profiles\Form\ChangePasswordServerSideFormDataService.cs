﻿using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.Entities;
using PremierAutoTag.ServiceContracts.Features.ChangePassword;

namespace PremierAutoTag.Server.DataServices.Features.Profiles.Form;

public class ChangePasswordServerSideFormDataService : IChangePasswordFormDataService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<ChangePasswordServerSideFormDataService> _logger;
    private readonly IAuthenticatedUser _authenticatedUser;

    public ChangePasswordServerSideFormDataService(
        UserManager<ApplicationUser> userManager,
        ILogger<ChangePasswordServerSideFormDataService> logger,
        IAuthenticatedUser authenticatedUser)
    {
        _userManager = userManager;
        _logger = logger;
        _authenticatedUser = authenticatedUser;
    }

    public async Task<string> SaveAsync(ChangePasswordFormBusinessObject formBusinessObject)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(formBusinessObject.Id);
            if (user == null)
            {
                throw new InvalidOperationException("User not found");
            }

            var result = await _userManager.ChangePasswordAsync(user, formBusinessObject.CurrentPassword, formBusinessObject.NewPassword);

            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                throw new InvalidOperationException($"Password change failed: {errors}");
            }

            _logger.LogInformation("Password changed successfully for user {UserId}", formBusinessObject.Id);
            return formBusinessObject.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user {UserId}", formBusinessObject.Id);
            throw;
        }
    }

    public async Task<ChangePasswordFormBusinessObject?> GetItemByIdAsync(string id)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return null;
            }

            // Return empty form for password change - we don't populate current passwords
            return new ChangePasswordFormBusinessObject
            {
                Id = id,
                CurrentPassword = "",
                NewPassword = "",
                ConfirmPassword = ""
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user for password change {UserId}", id);
            throw;
        }
    }
}
