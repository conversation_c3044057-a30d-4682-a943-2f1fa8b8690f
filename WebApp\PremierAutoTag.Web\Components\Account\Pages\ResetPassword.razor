﻿@page "/Account/ResetPassword"
@layout WebsiteLayout

@using System.ComponentModel.DataAnnotations
@using System.Text
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.Server.Data.Entities

@inject IdentityRedirectManager RedirectManager
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Reset password</PageTitle>

<section class="">
    <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <EditForm Enhance Model="Input" FormName="reset-password" OnValidSubmit="OnValidSubmitAsync" method="post" class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
            <DataAnnotationsValidator />
            @if (!string.IsNullOrEmpty(Message))
            {
                <div class="text-red-500 text-sm mb-2">@Message</div>
            }
            <div class="flex items-center justify-center">
                <image class="md:w-14 w-10 h-10 md:h-14" src="images/changePassword.svg"></image>
            </div>
            <div>
                <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Reset your password</h1>
                <h2 class="text-gray-600 text-sm md:text-base text-center">
                    Enter your new password below.
                </h2>
            </div>

            <input type="hidden" name="Input.Code" value="@Input.Code" />

            <div>
                <label class="text-brightGray font-medium text-sm mb-1 block">Email</label>
                <InputText @bind-Value="Input.Email"
                           class="w-full py-2 md:py-2.5 border border-gray-200 bg-gray-50 text-gray-500 rounded-lg text-sm md:text-base px-3 cursor-not-allowed"
                           placeholder="Enter your email"
                           autocomplete="username"
                           disabled />
                <ValidationMessage For="() => Input.Email" class="text-red-500 text-sm mt-1" />
            </div>

            <div>
                <label class="text-brightGray font-medium text-sm mb-1 block">New Password</label>
                <InputText type="password"
                           @bind-Value="Input.Password"
                           class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                           placeholder="Enter your new password"
                           autocomplete="new-password" />
                <ValidationMessage For="() => Input.Password" class="text-red-500 text-sm mt-1" />
            </div>

            <div>
                <label class="text-brightGray font-medium text-sm mb-1 block">Confirm Password</label>
                <InputText type="password"
                           @bind-Value="Input.ConfirmPassword"
                           class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                           placeholder="Confirm your new password"
                           autocomplete="new-password" />
                <ValidationMessage For="() => Input.ConfirmPassword" class="text-red-500 text-sm mt-1" />
            </div>

            <button class="w-full py-2 md:py-2.5 text-center rounded-lg bg-primary hover:bg-primaryDark text-white text-sm md:text-base font-semibold"
                    type="submit">
                Reset Password
            </button>

            <a href="/Account/Login" class="flex justify-center items-center gap-1 group">
                <span class="group-hover:transition duration-300 group-hover:-translate-x-1">
                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.3334 10H4.66669M4.66669 10L10.5 15.8334M4.66669 10L10.5 4.16669"
                              stroke="#A4A7AE" stroke-width="1.66667" stroke-linecap="round"
                              stroke-linejoin="round" />
                    </svg>
                </span>
                <span class="text-vampireGray hover:text-gray-700 text-sm font-semibold">
                    Back to log in
                </span>
            </a>

        </EditForm>
    </div>
</section>

@code {
    private IEnumerable<IdentityError>? identityErrors;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? Code { get; set; }

    [SupplyParameterFromQuery]
    private string? Email { get; set; }

    private string? Message => identityErrors is null ? null : $"Error: {string.Join(", ", identityErrors.Select(error => error.Description))}";

    protected override void OnInitialized()
    {
        if (Code is null)
        {
            RedirectManager.RedirectTo("Account/InvalidPasswordReset");
            return;
        }

        Input.Code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(Code));

        // Pre-populate email if provided in query parameters
        if (!string.IsNullOrEmpty(Email))
        {
            Input.Email = Email;
        }
    }

    private async Task OnValidSubmitAsync()
    {
        var user = await UserManager.FindByEmailAsync(Input.Email);
        if (user is null)
        {
            // Don't reveal that the user does not exist
            RedirectManager.RedirectTo("Account/ResetPasswordConfirmation");
        }

        var result = await UserManager.ResetPasswordAsync(user, Input.Code, Input.Password);
        if (result.Succeeded)
        {
            RedirectManager.RedirectTo("Account/ResetPasswordConfirmation");
        }

        identityErrors = result.Errors;
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";

        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [DataType(DataType.Password)]
        [Display(Name = "Confirm password")]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = "";

        [Required]
        public string Code { get; set; } = "";
    }
}
