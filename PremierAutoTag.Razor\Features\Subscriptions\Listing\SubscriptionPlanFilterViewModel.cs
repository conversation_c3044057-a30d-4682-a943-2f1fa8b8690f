﻿using PremierAutoTag.Framework.Core;

namespace PremierAutoTag.Razor.Features.SubscriptionPlan;
public class SubscriptionPlanFilterViewModel : BaseFilterViewModel
{
	private string? _name;
	private decimal? _minPrice;
	private decimal? _maxPrice;
	private bool? _isActive;
	private bool? _isPopular;
	private bool? _hasDirectSubmission;
	private bool? _hasDedicatedClientManager;

	public string? Name
	{
		get => _name;
		set => SetField(ref _name, value);
	}

	public decimal? MinPrice
	{
		get => _minPrice;
		set => SetField(ref _minPrice, value);
	}

	public decimal? MaxPrice
	{
		get => _maxPrice;
		set => SetField(ref _maxPrice, value);
	}

	public bool? IsActive
	{
		get => _isActive;
		set => SetField(ref _isActive, value);
	}

	public bool? IsPopular
	{
		get => _isPopular;
		set => SetField(ref _isPopular, value);
	}

	public bool? HasDirectSubmission
	{
		get => _hasDirectSubmission;
		set => SetField(ref _hasDirectSubmission, value);
	}

	public bool? HasDedicatedClientManager
	{
		get => _hasDedicatedClientManager;
		set => SetField(ref _hasDedicatedClientManager, value);
	}
}
