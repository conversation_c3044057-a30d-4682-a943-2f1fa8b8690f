@tailwind base;
@tailwind components;
@tailwind utilities;

body {
    background-color: #EEF2F6;
    font-family: "Inter", sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

@theme {
  --color-primary: #3250F9;
  --color-primaryDark: #2a43d0;
  --color-primaryLight: #d6dcfe;
  --color-gray-m-100: #EEF2F6;
  --color-gray-m-200: #E3E8EF;
  --color-gray-m-800: #202939;
  --color-gray-m-900: #121926;
  --color-vampireGray: #535862;
  --color-brightGray: #414651;
  --color-gunPowder: #364152;
  --color-gray-m-50: #F8FAFC;
  --color-gray-m-25: #FCFCFD;
  --color-seaShell: #EDEDED;
  --color-warning-700: #B54708;
   --color-warning-50: #FFFAEB;
    --color-warning-500: #F79009;
     --color-warning-200: #FEDF89;
}


body {
    background-color: #EEF2F6;
    font-family: "Inter", sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}


.custom-checkbox input[type="checkbox"] {
    display: none;
}

.custom-checkbox .checkmark {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 1px solid #D0D5DD;
    border-radius: 4px;
    position: relative;
    cursor: pointer;
    background-color: #ffffff;
    transition: all 0.2s ease;
}

.custom-checkbox .checkmark:hover {
    border-color: var(--color-primary);
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    top: 2.5px;
    left: 6px;
    width: 4px;
    height: 8px;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Focus styles for accessibility */
.custom-checkbox input[type="checkbox"]:focus + .checkmark {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* Swiper Navigation Arrows */
.swiper-button-next,
.swiper-button-prev {
    width: 44px !important;
    height: 44px !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 50% !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    color: var(--color-primary) !important;
    font-weight: bold !important;
    transition: all 0.3s ease !important;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background: white !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    transform: scale(1.05) !important;
}

.swiper-button-next::after,
.swiper-button-prev::after {
    font-size: 18px !important;
    font-weight: bold !important;
}

.swiper-button-disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
}

.swiper-button-disabled:hover {
    transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Swiper Pagination Dots */
.swiper-pagination {
    bottom: 20px !important;
}

.swiper-pagination-bullet {
    width: 12px !important;
    height: 12px !important;
    background: rgba(255, 255, 255, 0.5) !important;
    opacity: 1 !important;
    margin: 0 6px !important;
    transition: all 0.3s ease !important;
}

.swiper-pagination-bullet-active {
    background: white !important;
    transform: scale(1.2) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Swiper Container Adjustments */
.swiper {
    position: relative;
}

.swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
}