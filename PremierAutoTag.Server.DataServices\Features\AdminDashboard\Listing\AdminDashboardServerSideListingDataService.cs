﻿using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.Features.AdminDashboard;
namespace PremierAutoTag.Server.DataServices.Features.AdminDashboard;
public class AdminDashboardServerSideListingDataService : ServerSideListingDataService<AdminDashboardListingBusinessObject, AdminDashboardFilterBusinessObject>, IAdminDashboardListingDataService
{

	private readonly ApplicationDbContext _context;

	public AdminDashboardServerSideListingDataService (ApplicationDbContext context)
	{
		_context = context;
	}
	public override IQueryable<AdminDashboardListingBusinessObject> GetQuery(AdminDashboardFilterBusinessObject filterBusinessObject)
	{
		 return Array.Empty<AdminDashboardListingBusinessObject>().AsQueryable();
	}
}
