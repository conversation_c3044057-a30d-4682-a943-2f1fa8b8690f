﻿using PremierAutoTag.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Razor.Features.Profiles.Form;

public class ChangePasswordFormViewModel : ObservableBase
{
    private string _id = "";
    private string _currentPassword = "";
    private string _newPassword = "";
    private string _confirmPassword = "";

    public string Id
    {
        get => _id;
        set => SetField(ref _id, value);
    }

    [Required(ErrorMessage = "Current password is required")]
    [DataType(DataType.Password)]
    public string CurrentPassword
    {
        get => _currentPassword;
        set => SetField(ref _currentPassword, value);
    }

    [Required(ErrorMessage = "New password is required")]
    [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
    [DataType(DataType.Password)]
    public string NewPassword
    {
        get => _newPassword;
        set => SetField(ref _newPassword, value);
    }

    [Required(ErrorMessage = "Confirm password is required")]
    [DataType(DataType.Password)]
    [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
    public string ConfirmPassword
    {
        get => _confirmPassword;
        set => SetField(ref _confirmPassword, value);
    }
}
