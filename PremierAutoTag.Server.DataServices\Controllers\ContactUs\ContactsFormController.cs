﻿using Microsoft.AspNetCore.Mvc;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.Contact;
namespace PremierAutoTag.Server.DataServices.Controller.Contact;
[ApiController, Route("api/[controller]/[action]")]
public class ContactsFormController : ControllerB<PERSON>, IContactFormDataService
{

	private readonly IContactFormDataService dataService;

	public ContactsFormController(IContactFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpPost]
	public async Task<long> SaveAsync([FromBody] ContactFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[HttpGet]
	public async Task<ContactFormBusinessObject?> GetItemByIdAsync(long id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
