<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
    <StaticWebAssetProjectMode>Default</StaticWebAssetProjectMode>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\PremierAutoTag.Framework.Core\PremierAutoTag.Framework.Core.csproj" />
    <ProjectReference Include="..\..\PremierAutoTag.Razor\PremierAutoTag.Razor.csproj" />
    <ProjectReference Include="..\..\PremierAutoTag.ServiceContracts\PremierAutoTag.ServiceContracts.csproj" />
  </ItemGroup>



</Project>
