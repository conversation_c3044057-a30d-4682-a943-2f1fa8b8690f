﻿using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.Features.UserDashboard;
namespace PremierAutoTag.Server.DataServices.Features.UserDashboard;
public class UserDashboardServerSideListingDataService : ServerSideListingDataService<UserDashboardListingBusinessObject, UserDashboardFilterBusinessObject>, IUserDashboardListingDataService
{

	private readonly ApplicationDbContext _context;

	public UserDashboardServerSideListingDataService (ApplicationDbContext context)
	{
		_context = context;
	}
	public override IQueryable<UserDashboardListingBusinessObject> GetQuery(UserDashboardFilterBusinessObject filterBusinessObject)
	{
		 return Array.Empty<UserDashboardListingBusinessObject>().AsQueryable();
	}
}
