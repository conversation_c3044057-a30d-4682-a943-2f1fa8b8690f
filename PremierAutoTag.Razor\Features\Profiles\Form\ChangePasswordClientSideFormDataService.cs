﻿using System.Text;
using System.Text.Json;
using System.Net.Http.Json;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Extensions;
using PremierAutoTag.ServiceContracts;
using PremierAutoTag.ServiceContracts.Features.ChangePassword;

namespace PremierAutoTag.Razor.Features.Profiles.Form;
public class ChangePasswordClientSideFormDataService : IChangePasswordFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public ChangePasswordClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<string> SaveAsync(ChangePasswordFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/ChangePasswordsForm/Save", formBusinessObject);
	}
	public async Task<ChangePasswordFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<ChangePasswordFormBusinessObject>($"api/ChangePasswordsForm/GetItemById?id=" + id);
	}
}
