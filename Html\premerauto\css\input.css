@import "tailwindcss";

body {
    background-color: #EEF2F6;
     font-family: "Inter", sans-serif;
     display: flex;
     flex-direction: column;
     min-height: 100vh;
}

@theme {
  --color-primary: #3250F9;
  --color-primaryDark: #2a43d0;
  --color-primaryLight: #d6dcfe;
  --color-gray-m-100: #EEF2F6;
  --color-gray-m-200: #E3E8EF;
  --color-gray-m-800: #202939;
  --color-gray-m-900: #121926;
  --color-vampireGray: #535862;
  --color-brightGray: #414651;
  --color-gunPowder: #364152;
  --color-gray-m-50: #F8FAFC;
  --color-gray-m-25: #FCFCFD;
  --color-seaShell: #EDEDED;
  --color-warning-700: #B54708;
   --color-warning-50: #FFFAEB;
    --color-warning-500: #F79009;
     --color-warning-200: #FEDF89;
}


.custom-checkbox input[type="checkbox"] {
    display: none;
}

.custom-checkbox .checkmark {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 1px solid #D0D5DD;
    border-radius: 4px;
    position: relative;
    cursor: pointer;
    background-color: #ffffff;
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark {
    background-color: var(--color-primary); /* Change this to your desired color */
}

    .custom-checkbox input[type="checkbox"]:checked + .checkmark::after {
        content: '';
        position: absolute;
        top: 2.5px; /* Adjust based on your checkmark's size */
        left: 6px; /* Adjust based on your checkmark's size */
        width: 4px; /* Width of the checkmark */
        height: 8px; /* Height of the checkmark */
        border: solid #fff; /* Change color of the checkmark */
        border-width: 0 2px 2px 0; /* Creates a checkmark shape */
        transform: rotate(45deg); /* Rotate to form a checkmark */
    }