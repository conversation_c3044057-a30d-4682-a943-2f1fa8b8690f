﻿using System.Collections;
using System.Reflection;
using System.Web;

namespace PremierAutoTag.Framework.Core.Extensions
{
    public static class ObjectExtensions
    {
        public static string ToQueryString(this object obj)
        {
            if (obj == null) throw new ArgumentNullException(nameof(obj));

            var collection = HttpUtility.ParseQueryString(string.Empty);
            var properties = obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                var value = property.GetValue(obj, null);

                if (value == null) continue;


                if (value is IEnumerable enumerable && value is not string)
                {
                    foreach (var item in enumerable)
                    {
                        if (item != null)
                        {
                            collection.Add(property.Name, item is DateTime dt ? dt.ToString("o") : item.ToString());
                        }
                    }
                }
                else
                {
                    collection[property.Name] = value is DateTime dt ? dt.ToString("o") : value.ToString();
                }
            }

            return $"?{collection}";
        }

        public static string? ToParamString(this object obj)
        {
            var properties = obj.GetType().GetProperties();
            var collection = HttpUtility.ParseQueryString(string.Empty);

            foreach (var property in properties)
            {
                if (property.PropertyType != typeof(string) && property.PropertyType.GetInterfaces().Contains(typeof(IEnumerable)))
                {
                    var list = (IEnumerable?)property.GetValue(obj, null);
                    if (list != null)
                    {
                        foreach (var arrayElement in list)
                        {
                            if (!string.IsNullOrEmpty(arrayElement?.ToString()))
                            {
                                collection.Add(property.Name, arrayElement.ToString());
                            }
                        }
                    }
                }
                else
                {
                    var value = property.GetValue(obj, null)?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        collection[property.Name] = value;
                    }
                }
            }

            return collection?.ToString()?.Replace("+", " ")?.Replace("&", "+")?.Replace("=", "-");
        }
    }
}
