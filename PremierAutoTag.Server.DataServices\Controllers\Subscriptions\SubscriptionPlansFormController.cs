﻿using Microsoft.AspNetCore.Mvc;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
namespace PremierAutoTag.Server.DataServices.Controller.SubscriptionPlan;
[ApiController, Route("api/[controller]/[action]")]
public class SubscriptionPlansFormController : ControllerBase, ISubscriptionPlanFormDataService
{

	private readonly ISubscriptionPlanFormDataService dataService;

	public SubscriptionPlansFormController(ISubscriptionPlanFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpPost]
	public async Task<long> SaveAsync([FromBody] SubscriptionPlanFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[HttpGet]
	public async Task<SubscriptionPlanFormBusinessObject?> GetItemByIdAsync(long id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
