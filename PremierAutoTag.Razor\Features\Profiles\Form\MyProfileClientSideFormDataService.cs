﻿using System.Text;
using System.Text.Json;
using System.Net.Http.Json;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Extensions;
using PremierAutoTag.ServiceContracts;
using PremierAutoTag.ServiceContracts.Features.MyProfile;
namespace PremierAutoTag.Razor.Features.MyProfile;
public class MyProfileClientSideFormDataService : IMyProfileFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public MyProfileClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<string> SaveAsync(MyProfileFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/MyProfilesForm/Save", formBusinessObject);
	}
	public async Task<MyProfileFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<MyProfileFormBusinessObject>($"api/MyProfilesForm/GetItemById?id=" + id);
	}
}
