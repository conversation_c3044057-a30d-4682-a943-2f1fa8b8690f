﻿@page "/dashboard/user"
@layout MainLayout
@attribute [Authorize(Roles = "User")]

@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Razor.Features.UserDashboard;
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.ServiceContracts.Features.UserDashboard;
@inherits ListingBase<UserDashboardListingViewModel,UserDashboardListingBusinessObject,UserDashboardFilterViewModel,UserDashboardFilterBusinessObject, IUserDashboardListingDataService>

@*<button  class="btn btn-primary" type="button" @onclick='()=> ShowCenterDialog<UserDashboardFormComponent>(default(long), "Create New UserDashboard")'>Add New</button>*@

@if (Items == null)
{ }
else
{
    if (Items.Count() == 0)
    { }
    else
    {
        @foreach (var item in Items)
        {
            <!-- Add item layout here -->
        }
        <EditForm Model="PaginationStrip">
            @*<PaginationStrip @bind-Value="PaginationStrip" TotalPages="TotalPages" TotalRows="TotalRows"></PaginationStrip> *@
        </EditForm>
    }
}

