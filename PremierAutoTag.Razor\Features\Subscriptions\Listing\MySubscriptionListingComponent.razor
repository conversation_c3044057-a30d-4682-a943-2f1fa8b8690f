﻿@page "/upgrade-plan"
@layout MainLayout
@attribute [Authorize]

@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Razor.Features.MySubscription;
@using PremierAutoTag.ServiceContracts.Features.MySubscription;
@using PremierAutoTag.Razor.Layout
@using System.Security.Claims
@inherits ListingBase<MySubscriptionListingViewModel,MySubscriptionListingBusinessObject,MySubscriptionFilterViewModel,MySubscriptionFilterBusinessObject, IMySubscriptionListingDataService>

<div class="w-full">
    <!-- Prominent Header Section -->
    <h2 class="font-semibold text-lg md:text-xl text-gray-m-800">
        My Subscription
    </h2>
    <hr class="my-4 text-gray-200" />

    @if (IsTableBusy)
    {
        <div class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span class="ml-3 text-gray-600">Loading subscription details...</span>
        </div>
    }
    else if (!string.IsNullOrEmpty(ErrorMessage))
    {
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Error Loading Subscription</h3>
                    <p class="text-sm text-red-700 mt-1">@ErrorMessage</p>
                </div>
            </div>
        </div>
    }
    else
    {
        var subscription = Items.FirstOrDefault();

        @if (subscription != null)
        {
            <!-- Current Subscription Card -->
            <div class="bg-white rounded-xl border border-gray-200 shadow-lg overflow-hidden">
                <!-- Enhanced Header with Status -->
                <div class="bg-gradient-to-r from-primary via-blue-600 to-primaryDark px-6 py-6 relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 bg-white/10 opacity-20">
                        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
                    </div>

                    <div class="relative flex justify-between items-center">
                        <div>
                            <h3 class="text-white text-2xl md:text-3xl font-bold mb-1">@subscription.PlanName</h3>
                            <p class="text-white/90 text-base md:text-lg">Current Plan</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-white/20 backdrop-blur-sm text-white border border-white/30 mb-2">
                                @subscription.StatusDisplay
                            </span>
                            <p class="text-white text-2xl md:text-3xl font-bold">@subscription.PriceDisplay</p>
                        </div>
                    </div>
                </div>

                <!-- Subscription Details -->
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Plan Information -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Plan Details</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Max Vehicles:</span>
                                    <span class="font-medium">@subscription.MaxVehicles</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Max Users:</span>
                                    <span class="font-medium">@subscription.MaxUsers</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Standalone Quotes Discount:</span>
                                    <span class="font-medium">@subscription.StandAloneQuotesDiscountPercent%</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Registration Services Discount:</span>
                                    <span class="font-medium">@subscription.RegistrationTitleServicesDiscountPercent%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Subscription Information -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Subscription Information</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Start Date:</span>
                                    <span class="font-medium">@subscription.StartDate.ToString("MMM dd, yyyy")</span>
                                </div>
                                @if (subscription.EndDate.HasValue)
                                {
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">End Date:</span>
                                        <span class="font-medium">@subscription.EndDate.Value.ToString("MMM dd, yyyy")</span>
                                    </div>
                                }
                                @if (subscription.NextBillingDate.HasValue)
                                {
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Next Billing:</span>
                                        <span class="font-medium">@subscription.NextBillingDate.Value.ToString("MMM dd, yyyy")</span>
                                    </div>
                                }
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Auto Renew:</span>
                                    <span class="font-medium">@(subscription.AutoRenew ? "Yes" : "No")</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Features List -->
                    @if (!string.IsNullOrEmpty(subscription.Features))
                    {
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Plan Features</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                @foreach (var feature in subscription.Features.Split(',', StringSplitOptions.RemoveEmptyEntries))
                                {
                                    <div class="flex items-center gap-2">
                                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">@feature.Trim()</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <!-- Special Features -->
                    <div class="mt-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Additional Benefits</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            @if (subscription.HasDirectSubmission)
                            {
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">Direct Submission</span>
                                </div>
                            }
                            @if (subscription.HasDedicatedClientManager)
                            {
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">Dedicated Client Manager</span>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Payment Information (for paid plans) -->
                    @if (subscription.RequiresPayment && !subscription.IsFree)
                    {
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="flex items-center gap-2 mb-4">
                                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"></path>
                                    <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                                </svg>
                                <h4 class="text-lg font-semibold text-gray-900">Payment Information</h4>
                            </div>

                            @if (!string.IsNullOrEmpty(subscription.PaymentMethod) || subscription.LastPaymentDate.HasValue || subscription.LastPaymentAmount.HasValue || !string.IsNullOrEmpty(subscription.PaymentStatus))
                            {
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="space-y-3">
                                            @if (!string.IsNullOrEmpty(subscription.PaymentMethod))
                                            {
                                                <div class="flex justify-between">
                                                    <span class="text-gray-700 font-medium">Payment Method:</span>
                                                    <span class="text-gray-900 font-semibold">@subscription.PaymentMethod</span>
                                                </div>
                                            }
                                            @if (subscription.LastPaymentDate.HasValue)
                                            {
                                                <div class="flex justify-between">
                                                    <span class="text-gray-700 font-medium">Last Payment:</span>
                                                    <span class="text-gray-900 font-semibold">@subscription.LastPaymentDate.Value.ToString("MMM dd, yyyy")</span>
                                                </div>
                                            }
                                        </div>
                                        <div class="space-y-3">
                                            @if (subscription.LastPaymentAmount.HasValue)
                                            {
                                                <div class="flex justify-between">
                                                    <span class="text-gray-700 font-medium">Last Amount:</span>
                                                    <span class="text-green-700 font-bold text-lg">$@subscription.LastPaymentAmount.Value.ToString("F2")</span>
                                                </div>
                                            }
                                            @if (!string.IsNullOrEmpty(subscription.PaymentStatus))
                                            {
                                                <div class="flex justify-between">
                                                    <span class="text-gray-700 font-medium">Payment Status:</span>
                                                    <span class="font-semibold @(subscription.PaymentStatus == "succeeded" ? "text-green-700" : "text-yellow-700")">@subscription.PaymentStatus</span>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-yellow-800 font-medium">No payment information available</span>
                                    </div>
                                </div>
                            }
                        </div>
                    }

                    <!-- Action Buttons -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="/subscription-plans" class="inline-flex items-center justify-center px-4 py-2 border border-primary text-primary hover:bg-primary hover:text-white text-sm font-medium rounded-lg transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                </svg>
                                Upgrade Plan
                            </a>
                            @if (subscription.IsActive && subscription.RequiresPayment)
                            {
                                <button type="button" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 text-sm font-medium rounded-lg transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Cancel Subscription
                                </button>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <!-- No Subscription State -->
            <div class="text-center py-12">
                <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Subscription</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto">
                    You don't have an active subscription plan. Choose a plan to get started with AutoTag's premium features.
                </p>
                <a href="/subscription-plans" class="inline-flex items-center px-6 py-3 bg-primary hover:bg-primaryDark text-white font-medium rounded-lg transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Choose a Plan
                </a>
            </div>
        }
    }
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        // Set the user filter to only show current user's subscriptions
        var userId = AuthenticatedUser?.UserId;
        if (!string.IsNullOrEmpty(userId))
        {
            FilterViewModel.UserId = userId;
        }

        await base.OnInitializedAsync();
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "Active" => "bg-green-100 text-green-800",
            "Cancelled" => "bg-red-100 text-red-800",
            "Expired" => "bg-gray-100 text-gray-800",
            _ => "bg-gray-100 text-gray-800"
        };
    }
}

