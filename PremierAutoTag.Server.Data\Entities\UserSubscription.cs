using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Server.Data.Entities
{
    [Index(nameof(Id))]
    public class UserSubscription : BaseEntity
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(450)]
        public string UserId { get; set; } = "";

        [Required]
        public long SubscriptionPlanId { get; set; }

        [StringLength(255)]
        public string? StripeSubscriptionId { get; set; }

        [StringLength(255)]
        public string? StripeCustomerId { get; set; }

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "Active";

        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public DateTime? NextBillingDate { get; set; }

        public DateTime? CancelledAt { get; set; }

        [StringLength(1000)]
        public string? CancellationReason { get; set; }

        public bool AutoRenew { get; set; } = true;

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual SubscriptionPlan SubscriptionPlan { get; set; } = null!;
        public virtual ICollection<StripePayment> Payments { get; set; } = new List<StripePayment>();
    }
}
