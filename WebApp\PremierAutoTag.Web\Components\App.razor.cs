using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;

namespace PremierAutoTag.Web.Components
{
    public partial class App
    {
        [CascadingParameter]
        private HttpContext HttpContext { get; set; } = default!;

        private static IComponentRenderMode RenderMode = new InteractiveServerRenderMode(false);

        private IComponentRenderMode? PageRenderMode
        {
            get
            {
                // Define public pages that should use Static SSR (no interactivity)
                var publicPaths = new[]
                {
                    "/",                    // Homepage
                    "/aboutus",            // About Us page
                    "/locations",          // Locations page
                    "/FAQ",                // FAQ page
                    "/tips",               // DMV Tips page
                    "/contact",            // Contact page
                    "/privacy",            // Privacy Policy
                    "/terms"               // Terms of Service
                };

                // Check if current path is a public page that should use Static SSR
                var currentPath = HttpContext.Request.Path.Value ?? "";
                var isPublicPage = false;

                // Explicit check for homepage (exact match)
                if (currentPath == "/")
                {
                    isPublicPage = true;
                    Console.WriteLine($"App.razor: '{currentPath}' detected as PUBLIC (homepage)");
                }
                // Check for Account routes (explicit prefix)
                else if (currentPath.StartsWith("/Account/", StringComparison.OrdinalIgnoreCase))
                {
                    isPublicPage = true;
                    Console.WriteLine($"App.razor: '{currentPath}' detected as PUBLIC (Account route)");
                }
                // Check other public routes (exact match)
                else if (publicPaths.Contains(currentPath, StringComparer.OrdinalIgnoreCase))
                {
                    isPublicPage = true;
                    Console.WriteLine($"App.razor: '{currentPath}' detected as PUBLIC (in publicPaths array)");
                }
                else
                {
                    Console.WriteLine($"App.razor: '{currentPath}' detected as PROTECTED (will use Interactive mode)");
                }

                // If it's a public page, use Static SSR (return null for no interactivity)
                if (isPublicPage)
                {
                    return null; // Static SSR
                }

                // For authenticated pages, use Interactive Server mode if routing is accepted
                if (HttpContext.AcceptsInteractiveRouting())
                {
                    return RenderMode; // Interactive Server
                }

                // Default to Static SSR for any other case
                return null;
            }
        }
    }
}