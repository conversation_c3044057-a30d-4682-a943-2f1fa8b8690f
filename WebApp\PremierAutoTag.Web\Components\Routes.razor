﻿@using PremierAutoTag.Razor.Layout
<Router AppAssembly="typeof(Program).Assembly" AdditionalAssemblies="new[] { typeof(Client._Imports).Assembly, typeof(MainLayout).Assembly }">
    <Found Context="routeData">
        @{
            var currentPath = routeData.PageType?.GetCustomAttributes(typeof(Microsoft.AspNetCore.Components.RouteAttribute), false)
                .Cast<Microsoft.AspNetCore.Components.RouteAttribute>()
                .FirstOrDefault()?.Template ?? "";

            // Define public routes that don't require authentication
            var publicRoutes = new[] { "/", "/Account/Login", "/Account/Register", "/Account/ForgotPassword",
                                     "/Account/ResetPassword", "/Account/ConfirmEmail", "/Account/RegisterConfirmation",
                                     "/Account/ForgotPasswordConfirmation", "/Account/ResetPasswordConfirmation",
                                     "/Account/ResendEmailConfirmation", "/aboutus", "/locations", "/FAQ",
                                     "/tips", "/contact", "/privacy", "/terms" };

            // Check if route is public - be explicit about homepage and Account routes
            var isPublicRoute = false;

            if (currentPath == "/") // Exact match for homepage
            {
                isPublicRoute = true;
                Console.WriteLine($"Route '{currentPath}' detected as PUBLIC (homepage)");
            }
            else if (currentPath.StartsWith("/Account/", StringComparison.OrdinalIgnoreCase)) // Account routes
            {
                isPublicRoute = true;
                Console.WriteLine($"Route '{currentPath}' detected as PUBLIC (Account route)");
            }
            else if (publicRoutes.Contains(currentPath, StringComparer.OrdinalIgnoreCase)) // Other public routes
            {
                isPublicRoute = true;
                Console.WriteLine($"Route '{currentPath}' detected as PUBLIC (in publicRoutes array)");
            }
            else
            {
                Console.WriteLine($"Route '{currentPath}' detected as PROTECTED (will use MainLayout with Interactive mode)");
            }
        }

        @if (isPublicRoute)
        {
            <!-- Public pages use WebsiteLayout and don't require authentication -->
            <RouteView RouteData="routeData" DefaultLayout="typeof(WebsiteLayout)" />
        }
        else
        {
            <!-- Protected pages require authentication and use MainLayout -->
            <AuthorizeRouteView RouteData="routeData" DefaultLayout="typeof(MainLayout)">
                <Authorizing>
                    <div class="flex items-center justify-center min-h-screen">
                        <div class="text-center">
                            <svg class="animate-spin h-8 w-8 text-primary mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <p class="text-gray-600">Loading...</p>
                        </div>
                    </div>
                </Authorizing>
                <NotAuthorized>
                    <RedirectToLogin />
                </NotAuthorized>
            </AuthorizeRouteView>
        }

        <FocusOnNavigate RouteData="routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Page Not Found</PageTitle>
        <LayoutView Layout="typeof(WebsiteLayout)">
            <div class="flex items-center justify-center min-h-screen">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-gray-900 mb-4">404</h1>
                    <p class="text-gray-600 mb-4">Sorry, the page you're looking for doesn't exist.</p>
                    <a href="/" class="text-primary hover:text-primaryDark font-semibold">Go back home</a>
                </div>
            </div>
        </LayoutView>
    </NotFound>
</Router>
