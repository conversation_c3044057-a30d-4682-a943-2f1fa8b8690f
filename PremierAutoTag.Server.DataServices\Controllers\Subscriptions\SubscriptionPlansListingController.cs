﻿using Microsoft.AspNetCore.Mvc;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
namespace PremierAutoTag.Server.DataServices.Controller.SubscriptionPlan;
[ApiController, Route("api/[controller]/[action]")]
public class SubscriptionPlansListingController : ControllerBase, ISubscriptionPlanListingDataService
{

	private readonly ISubscriptionPlanListingDataService dataService;

	public SubscriptionPlansListingController(ISubscriptionPlanListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpGet]
	public async Task<PagedDataList<SubscriptionPlanListingBusinessObject>> GetPaginatedItems([FromQuery] SubscriptionPlanFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
