﻿@page "/Account/Register"
@layout WebsiteLayout

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.Server.Data.Entities

@inject UserManager<ApplicationUser> UserManager
@inject IUserStore<ApplicationUser> UserStore
@inject SignInManager<ApplicationUser> SignInManager
@inject ILogger<Register> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>Register</PageTitle>

<section class="">
    <div class="md:md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <div class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
            <div>
                <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Register as</h1>
                <h2 class="text-gray-600 text-sm md:text-base text-center">
                    Please choose the option to create
                    account
                </h2>
            </div>
            <div class=" flex flex-col md:flex-row rounded-md justify-center  gap-4 " role="group">
                <a href="/Account/Register/Individual"
                   class="px-5 py-6 w-full text-sm font-medium gap-2 flex flex-col cursor-pointer items-center text-gray-900 bg-white border border-gray-200 rounded-3xl hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring focus:ring-primary focus:bg-blue-50 focus:text-primary ">
                    <image src="images/individualIcon.svg" />
                    <h2 class="text-gray-800 text-lg font-semibold">Individual</h2>
                </a>
                <a href="/Account/Register/Dealership"
                   class="px-5 py-6 w-full text-sm font-medium gap-2 flex flex-col cursor-pointer items-center text-gray-900 bg-white border border-gray-200 rounded-3xl hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring focus:ring-primary focus:bg-blue-50 focus:text-primary ">
                    <image src="images/dealershipIcon.svg" />
                    <h2 class="text-gray-800 text-lg font-semibold">Dealership</h2>
                </a>
            </div>
            <div class="flex justify-center gap-1">
                <span class="text-sm text-gray-600">Already have an account?</span>
                <a href="/Account/Login"
                   class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4 ">
                    Log
                    In
                </a>
            </div>
        </div>
    </div>
</section>
