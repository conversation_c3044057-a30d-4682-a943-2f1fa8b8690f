﻿class AutoTagEvents {
    constructor() {
        /*this.preloader = document.getElementById('preloader');*/
        this.loader = document.getElementById('loader');
        this.init();
        this.overrideFetch();
        this.bindEnhancedLoadEvent();
        this.loadSwiper();
    }

    // Initialize functions for both normal and Blazor navigation
    init() {
        this.initializeMobileMenu();
        this.loadSwiper();
    }

    // Override fetch to handle Blazor enhanced navigation
    overrideFetch() {
        const { fetch: originalFetch } = window;
        window.fetch = async (...args) => {
            const [url, options] = args;
            if (options && options.headers && options.headers['accept']?.includes('blazor-enhanced-nav')) {
                this.showLoader(true);
                console.log('Show Loader');
            }
            return await originalFetch(...args);
        };
    }

    // Bind enhanced load event for Blazor-specific navigation
    bindEnhancedLoadEvent() {
        // Check if Blazor is available (for interactive pages)
        if (typeof Blazor !== 'undefined') {
            Blazor.addEventListener('enhancedload', () => {
                this.init();
                console.log('enhancedload event fired');
                this.showLoader(false);
            });
        }
    }

    showLoader(isLoading) {
        if (isLoading) {
            // this.preloader.style.display = 'none'; // Hide preloader
            this.loader.style.display = 'block';  // Show loader
        } else {
            //this.preloader.style.display = 'none'; // Ensure preloader is hidden
            this.loader.style.display = 'none';   // Hide loader
        }
    }

    loadSwiper() {
        // Check if Swiper is available and if there are swiper elements on the page
        if (typeof Swiper !== 'undefined') {
            const swiperElements = document.querySelectorAll('.mySwiper');

            swiperElements.forEach((element, index) => {
                // Avoid reinitializing already initialized swipers
                if (!element.swiper) {
                    try {
                        const swiper = new Swiper(element, {
                            cssMode: true,
                            navigation: {
                                nextEl: ".swiper-button-next",
                                prevEl: ".swiper-button-prev",
                            },
                            pagination: {
                                el: ".swiper-pagination",
                                clickable: true,
                            },
                            mousewheel: true,
                            keyboard: true,
                            loop: true,
                            autoplay: {
                                delay: 3000,
                                disableOnInteraction: false,
                            },
                        });
                        console.log(`Swiper ${index + 1} initialized successfully`);
                    } catch (error) {
                        console.error(`Error initializing Swiper ${index + 1}:`, error);
                    }
                }
            });
        } else {
            console.warn('Swiper library not loaded');
        }
    }

    initializeMobileMenu() {
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }
    }

}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    new AutoTagEvents();
});
