/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        // Current project (PremierAutoTag.Web) - Server-side components
        "./Components/**/*.{razor,cshtml,cs,html}",
        "./Pages/**/*.{razor,cshtml,cs,html}",
        "./wwwroot/**/*.{html,js}",
        "./**/*.razor",

        // PremierAutoTag.Razor library (go up one level, then into the library)
        "../../PremierAutoTag.Razor/**/*.{razor,cshtml,cs,html}",
        "../../PremierAutoTag.Razor/Layout/**/*.razor",
        "../../PremierAutoTag.Razor/wwwroot/**/*.{html,js}",

        // PremierAutoTag.Web.Client (WebAssembly client)
        "../PremierAutoTag.Web.Client/**/*.{razor,cshtml,cs,html}",

        // Include C# files that might contain Tailwind classes
        "./**/*.cs",
        "../../PremierAutoTag.Razor/**/*.cs",
        "../../PremierAutoTag.Web.Client/**/*.cs"
    ],
 
    theme: {
            extend: {
                colors: {
                    primary: '#3250F9',
                    primaryDark: '#2a43d0',
                    black: '#000',
                    white: '#fff',
                    red: {
                        500: 'oklch(63.7% 0.237 25.331)',
                    },
                    green: {
                        50: 'oklch(98.2% 0.018 155.826)',
                        100: 'oklch(96.2% 0.044 156.743)',
                        200: 'oklch(92.5% 0.084 155.995)',
                        500: 'oklch(72.3% 0.219 149.579)',
                        700: 'oklch(52.7% 0.154 150.069)',
                    },
                    blue: {
                        50: 'oklch(97% 0.014 254.604)',
                        700: 'oklch(48.8% 0.243 264.376)',
                    },
                    slate: {
                        50: 'oklch(98.4% 0.003 247.858)',
                        100: 'oklch(96.8% 0.007 247.896)',
                        700: 'oklch(37.2% 0.044 257.287)',
                    },
                    gray: {
                        50: 'oklch(98.5% 0.002 247.839)',
                        100: 'oklch(96.7% 0.003 264.542)',
                        200: 'oklch(92.8% 0.006 264.531)',
                        300: 'oklch(87.2% 0.01 258.338)',
                        400: 'oklch(70.7% 0.022 261.325)',
                        500: 'oklch(55.1% 0.027 264.364)',
                        600: 'oklch(44.6% 0.03 256.802)',
                        700: 'oklch(37.3% 0.034 259.733)',
                        800: 'oklch(27.8% 0.033 256.848)',
                        900: 'oklch(21% 0.034 264.665)',
                        m: {
                            25: '#FCFCFD',
                            50: '#F8FAFC',
                            200: '#E3E8EF',
                            800: '#202939',
                            900: '#121926',
                        },
                    },
                    vampireGray: '#535862',
                    brightGray: '#414651',
                    gunPowder: '#364152',
                    seaShell: '#EDEDED',
                    warning: {
                        50: '#FFFAEB',
                        200: '#FEDF89',
                        500: '#F79009',
                        700: '#B54708',
                    },
                },
            },
        },
    
    plugins: []
}
