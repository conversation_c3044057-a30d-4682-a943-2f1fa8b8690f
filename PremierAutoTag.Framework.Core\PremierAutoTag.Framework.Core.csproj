﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<ItemGroup>
		<SupportedPlatform Include="browser" />
	</ItemGroup>
	<ItemGroup>
	  <Compile Remove="PPPlatformConstants.cs" />
	</ItemGroup>
	<ItemGroup> 
		<PackageReference Include="Microsoft.AspNetCore.Components" Version="9.0.6" /> 
		<PackageReference Include="Microsoft.AspNetCore.DataProtection.Abstractions" Version="9.0.6" /> 
		<PackageReference Include="Microsoft.AspNetCore.DataProtection.Extensions" Version="9.0.6" /> 
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" /> 
		<PackageReference Include="Microsoft.JSInterop" Version="9.0.6" />
		<PackageReference Include="PubSub" Version="4.0.2" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="9.0.6" />
	</ItemGroup>

</Project>
