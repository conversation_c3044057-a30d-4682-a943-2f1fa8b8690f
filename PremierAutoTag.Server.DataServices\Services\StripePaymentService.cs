using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.Server.Data.Entities;
using PremierAutoTag.ServiceContracts.Services;
using Stripe;
using Stripe.Checkout;
using Microsoft.AspNetCore.Identity;

namespace PremierAutoTag.Server.DataServices.Services
{
    public class StripePaymentService : IStripePaymentService
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<StripePaymentService> _logger;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IEmailVerificationService _emailVerificationService;

        public StripePaymentService(
            ApplicationDbContext context,
            IConfiguration configuration,
            ILogger<StripePaymentService> logger,
            UserManager<ApplicationUser> userManager,
            IEmailVerificationService emailVerificationService)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
            _userManager = userManager;
            _emailVerificationService = emailVerificationService;

            // Initialize Stripe with the secret key
            StripeConfiguration.ApiKey = _configuration["Stripe:SecretKey"];
        }

        public async Task<string> CreateCheckoutSessionAsync(CreateCheckoutSessionRequest request)
        {
            try
            {
                // Get the subscription plan
                var subscriptionPlan = await _context.SubscriptionPlans
                    .FirstOrDefaultAsync(sp => sp.Id == request.SubscriptionPlanId && sp.IsActive && !sp.IsDelete);

                if (subscriptionPlan == null)
                {
                    throw new InvalidOperationException("Subscription plan not found or inactive");
                }

                // Create or get Stripe customer
                var customer = await GetOrCreateStripeCustomerAsync(request.UserId, request.UserEmail);

                // Create checkout session options
                var options = new SessionCreateOptions
                {
                    PaymentMethodTypes = new List<string> { "card" },
                    LineItems = new List<SessionLineItemOptions>
                    {
                        new SessionLineItemOptions
                        {
                            PriceData = new SessionLineItemPriceDataOptions
                            {
                                UnitAmount = (long)(subscriptionPlan.AnnualPrice * 100), // Convert to cents
                                Currency = "usd",
                                ProductData = new SessionLineItemPriceDataProductDataOptions
                                {
                                    Name = subscriptionPlan.Name,
                                    Description = subscriptionPlan.Description ?? $"{subscriptionPlan.Name} - Annual Subscription"
                                }
                            },
                            Quantity = 1
                        }
                    },
                    Mode = "payment",
                    Customer = customer.Id,
                    SuccessUrl = request.SuccessUrl + "?session_id={CHECKOUT_SESSION_ID}",
                    CancelUrl = request.CancelUrl,
                    Metadata = new Dictionary<string, string>
                    {
                        { "subscription_plan_id", request.SubscriptionPlanId.ToString() },
                        { "user_id", request.UserId },
                        { "plan_name", subscriptionPlan.Name }
                    }
                };

                // Add custom metadata if provided
                if (request.Metadata != null)
                {
                    foreach (var kvp in request.Metadata)
                    {
                        options.Metadata[kvp.Key] = kvp.Value;
                    }
                }

                var service = new SessionService();
                var session = await service.CreateAsync(options);

                // Create a pending payment record
                var stripePayment = new StripePayment
                {
                    StripePaymentIntentId = session.Id, // Using session ID for now
                    StripeCustomerId = customer.Id,
                    SubscriptionPlanId = request.SubscriptionPlanId,
                    UserId = request.UserId,
                    Amount = subscriptionPlan.AnnualPrice,
                    Currency = "USD",
                    Status = "pending",
                    Description = $"Payment for {subscriptionPlan.Name} subscription"
                };

                _context.StripePayments.Add(stripePayment);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created Stripe checkout session {SessionId} for user {UserId}", session.Id, request.UserId);

                return session.Url;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Stripe checkout session for user {UserId}", request.UserId);
                throw;
            }
        }

        public async Task<bool> ProcessPaymentSuccessAsync(string sessionId)
        {
            try
            {
                var service = new SessionService();
                var session = await service.GetAsync(sessionId);

                if (session.PaymentStatus == "paid")
                {
                    // Update payment record
                    var payment = await _context.StripePayments
                        .FirstOrDefaultAsync(p => p.StripePaymentIntentId == sessionId);

                    if (payment != null)
                    {
                        payment.Status = "completed";
                        payment.PaymentCompletedAt = DateTime.UtcNow;

                        // Create or update user subscription
                        await CreateOrUpdateUserSubscriptionAsync(payment);

                        await _context.SaveChangesAsync();

                        _logger.LogInformation("Successfully processed payment for session {SessionId}", sessionId);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment success for session {SessionId}", sessionId);
                return false;
            }
        }

        public async Task<bool> ProcessPaymentFailureAsync(string sessionId, string errorMessage)
        {
            try
            {
                var payment = await _context.StripePayments
                    .FirstOrDefaultAsync(p => p.StripePaymentIntentId == sessionId);

                if (payment != null)
                {
                    payment.Status = "failed";
                    payment.PaymentFailedAt = DateTime.UtcNow;
                    payment.FailureReason = errorMessage;

                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Processed payment failure for session {SessionId}: {ErrorMessage}", sessionId, errorMessage);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment failure for session {SessionId}", sessionId);
                return false;
            }
        }

        public async Task<bool> ProcessWebhookEventAsync(string eventJson, string signature)
        {
            try
            {
                var webhookSecret = _configuration["Stripe:WebhookSecret"];
                var stripeEvent = EventUtility.ConstructEvent(eventJson, signature, webhookSecret);

                // Log the webhook event
                var webhookEvent = new StripeWebhookEvent
                {
                    StripeEventId = stripeEvent.Id,
                    EventType = stripeEvent.Type,
                    EventData = eventJson,
                    ProcessingStatus = "Processing"
                };

                _context.StripeWebhookEvents.Add(webhookEvent);
                await _context.SaveChangesAsync();

                // Process the event based on type
                var processed = await ProcessStripeEventAsync(stripeEvent, webhookEvent);

                // Update webhook event status
                webhookEvent.ProcessingStatus = processed ? "Completed" : "Failed";
                webhookEvent.ProcessedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return processed;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Stripe webhook event");
                return false;
            }
        }

        private async Task<Customer> GetOrCreateStripeCustomerAsync(string userId, string email)
        {
            // Check if customer already exists in our database
            var existingPayment = await _context.StripePayments
                .FirstOrDefaultAsync(p => p.UserId == userId && !string.IsNullOrEmpty(p.StripeCustomerId));

            if (existingPayment != null)
            {
                var customerService = new CustomerService();
                try
                {
                    return await customerService.GetAsync(existingPayment.StripeCustomerId);
                }
                catch
                {
                    // Customer might have been deleted, create a new one
                }
            }

            // Create new customer
            var options = new CustomerCreateOptions
            {
                Email = email,
                Metadata = new Dictionary<string, string>
                {
                    { "user_id", userId }
                }
            };

            var service = new CustomerService();
            return await service.CreateAsync(options);
        }

        private async Task CreateOrUpdateUserSubscriptionAsync(StripePayment payment)
        {
            var existingSubscription = await _context.UserSubscriptions
                .FirstOrDefaultAsync(us => us.UserId == payment.UserId && us.Status == "Active");

            if (existingSubscription != null)
            {
                // Update existing subscription
                existingSubscription.SubscriptionPlanId = payment.SubscriptionPlanId;
                existingSubscription.StartDate = DateTime.UtcNow;
                existingSubscription.EndDate = DateTime.UtcNow.AddYears(1); // Annual subscription
                existingSubscription.NextBillingDate = DateTime.UtcNow.AddYears(1);
                existingSubscription.StripeCustomerId = payment.StripeCustomerId;
            }
            else
            {
                // Create new subscription
                var subscription = new UserSubscription
                {
                    UserId = payment.UserId,
                    SubscriptionPlanId = payment.SubscriptionPlanId,
                    StripeCustomerId = payment.StripeCustomerId,
                    Status = "Active",
                    StartDate = DateTime.UtcNow,
                    EndDate = DateTime.UtcNow.AddYears(1), // Annual subscription
                    NextBillingDate = DateTime.UtcNow.AddYears(1),
                    AutoRenew = true
                };

                _context.UserSubscriptions.Add(subscription);
            }

            // Update user profile with subscription plan
            var userProfile = await _context.Profiles.FirstOrDefaultAsync(p => p.Id == payment.UserId);
            if (userProfile != null)
            {
                userProfile.SubscriptionPlanId = payment.SubscriptionPlanId;
                userProfile.ModifiedAt = DateTime.UtcNow;
                userProfile.ModifiedBy = payment.UserId;
            }

            // Mark email as confirmed now that payment is complete and registration is finished
            await _emailVerificationService.MarkEmailAsVerifiedAsync(payment.UserId);

            _logger.LogInformation("Marked email as verified for user {UserId} after successful payment", payment.UserId);
        }

        private async Task<bool> ProcessStripeEventAsync(Event stripeEvent, StripeWebhookEvent webhookEvent)
        {
            try
            {
                switch (stripeEvent.Type)
                {
                    case "checkout.session.completed":
                        var session = stripeEvent.Data.Object as Session;
                        if (session != null)
                        {
                            return await ProcessPaymentSuccessAsync(session.Id);
                        }
                        break;

                    case "payment_intent.succeeded":
                        // Handle successful payment intent
                        break;

                    case "payment_intent.payment_failed":
                        // Handle failed payment intent
                        break;

                    default:
                        _logger.LogInformation("Unhandled Stripe event type: {EventType}", stripeEvent.Type);
                        return true; // Mark as processed even if we don't handle it
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Stripe event {EventType}", stripeEvent.Type);
                webhookEvent.ProcessingError = ex.Message;
                return false;
            }
        }
    }
}
