﻿using Microsoft.AspNetCore.Mvc;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.DealerDashboard;
namespace PremierAutoTag.Server.DataServices.Controller.DealerDashboard;
[ApiController, Route("api/[controller]/[action]")]
public class DealerDashboardsListingController : ControllerBase, IDealerDashboardListingDataService
{

	private readonly IDealerDashboardListingDataService dataService;

	public DealerDashboardsListingController(IDealerDashboardListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpGet]
	public async Task<PagedDataList<DealerDashboardListingBusinessObject>> GetPaginatedItems([FromQuery] DealerDashboardFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
