﻿using PremierAutoTag.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
public class SubscriptionPlanFormBusinessObject
{
	public long Id { get; set; }

	[Required(ErrorMessage = "Plan name is required")]
	[StringLength(100, ErrorMessage = "Plan name cannot exceed 100 characters")]
	public string Name { get; set; } = "";

	[StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
	public string? Description { get; set; }

	[Required(ErrorMessage = "Annual price is required")]
	[Range(0, 999999.99, ErrorMessage = "Annual price must be between 0 and 999,999.99")]
	public decimal AnnualPrice { get; set; }

	[Required(ErrorMessage = "Maximum vehicles is required")]
	[Range(1, int.MaxValue, ErrorMessage = "Maximum vehicles must be at least 1")]
	public int MaxVehicles { get; set; }

	[Required(ErrorMessage = "Maximum users is required")]
	[Range(1, int.MaxValue, ErrorMessage = "Maximum users must be at least 1")]
	public int MaxUsers { get; set; }

	[Range(0, 100, ErrorMessage = "Discount percentage must be between 0 and 100")]
	public int StandAloneQuotesDiscountPercent { get; set; }

	[Range(0, 100, ErrorMessage = "Discount percentage must be between 0 and 100")]
	public int RegistrationTitleServicesDiscountPercent { get; set; }

	public bool HasDirectSubmission { get; set; }

	public bool HasDedicatedClientManager { get; set; }

	[StringLength(1000, ErrorMessage = "Features cannot exceed 1000 characters")]
	public string? Features { get; set; }

	public bool IsPopular { get; set; }

	[Range(0, int.MaxValue, ErrorMessage = "Display order must be non-negative")]
	public int DisplayOrder { get; set; }

	public bool IsActive { get; set; } = true;
}
