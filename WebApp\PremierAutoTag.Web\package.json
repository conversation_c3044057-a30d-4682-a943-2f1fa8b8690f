{"name": "premierautotag-web", "version": "1.0.0", "description": "Tailwind CSS build configuration for PremierAutoTag.Web", "scripts": {"build-css": "tailwindcss -i ./wwwroot/css/input.css -o ./wwwroot/css/output.css --minify", "build-css-dev": "tailwindcss -i ./wwwroot/css/input.css -o ./wwwroot/css/output.css", "watch-css": "tailwindcss -i ./wwwroot/css/input.css -o ./wwwroot/css/output.css --watch", "prebuild": "npm run build-css", "prestart": "npm run build-css-dev"}, "devDependencies": {"autoprefixer": "^10.4.21", "glob": "^11.0.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}, "keywords": ["tailwindcss", "blazor", "dotnet"], "author": "PremierAutoTag Team", "license": "MIT"}