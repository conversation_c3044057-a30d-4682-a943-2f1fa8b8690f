﻿using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Options;

namespace PremierAutoTag.Framework.Core.DataProtections
{
    public class DataProtection : IDataProtection
    {
        private readonly SecureDataSetting _secureDataSetting;
        private readonly IDataProtector protector;
        public DataProtection(IDataProtectionProvider dataProtectionProvider, IOptions<SecureDataSetting> secureDataSetting)
        {
            _secureDataSetting = secureDataSetting.Value;
            protector = dataProtectionProvider.CreateProtector(_secureDataSetting.DataProtectionKey);
        }
        public string Encode(string data)
        {
            return protector.Protect(data);
        }
        public string Decode(string data)
        {
            string result = string.Empty;
            try
            {
                result = protector.Unprotect(data);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                throw;
            }
            return result;
        }
        public string EncodeTTL(string data, double lifeTimeInSeconds = 1800)
        {
            var timeLimitedDataProtector = protector.ToTimeLimitedDataProtector();
            return timeLimitedDataProtector.Protect(data, TimeSpan.FromSeconds(lifeTimeInSeconds));
        }
        public string DecodeTTL(string data)
        {
            string result = string.Empty;
            try
            {
                var timeLimitedDataProtector = protector.ToTimeLimitedDataProtector();
                DateTimeOffset timeSpan = new();
                result = timeLimitedDataProtector.Unprotect(data, out timeSpan);
                if (timeSpan < DateTime.UtcNow)
                {
                    throw new Exception();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                throw;
            }
            return result;
        }
    }
}
