﻿@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Razor.Features.SubscriptionPlan;
@using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Authorization
@inherits FormBase<SubscriptionPlanFormBusinessObject,SubscriptionPlanFormViewModel, long, ISubscriptionPlanFormDataService>

@if (SelectedItem != null)
{
    <!-- Subscription Plan Form - Tailwind CSS Styled -->
    <div class="bg-white h-full">
        <!-- Form Content with proper padding and spacing -->
        <div class="relative p-6 space-y-8">
            <EditForm Enhance method="post" Model="SelectedItem" OnValidSubmit="OnFormSubmit" FormName="SubscriptionPlan">
                <DataAnnotationsValidator></DataAnnotationsValidator>
                <ValidationSummary class="text-red-500 bg-red-50 border border-red-200 rounded-md p-4 mb-6"></ValidationSummary>

                <!-- Form disabled overlay when busy -->
                @if (IsBusy)
                {
                    <div class="absolute inset-0 bg-white bg-opacity-50 z-10 rounded-lg"></div>
                }

                <!-- Basic Information Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-2">
                        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                        <p class="text-sm text-gray-500">Enter the basic details for the subscription plan.</p>
                    </div>

                    <!-- Plan Name and Annual Price -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <label for="name" class="block text-sm font-medium text-gray-700">Plan Name *</label>
                            <InputText @bind-Value="SelectedItem.Name"
                                       class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm"
                                       id="name"
                                       placeholder="Enter plan name" />
                            <ValidationMessage For="@(() => SelectedItem.Name)" class="text-red-500 text-sm" />
                        </div>
                        <div class="space-y-2">
                            <label for="annualPrice" class="block text-sm font-medium text-gray-700">Annual Price *</label>
                            <InputNumber @bind-Value="SelectedItem.AnnualPrice"
                                         class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm"
                                         id="annualPrice"
                                         placeholder="0.00" />
                            <ValidationMessage For="@(() => SelectedItem.AnnualPrice)" class="text-red-500 text-sm" />
                        </div>
                    </div>
                </div>

                    <!-- Description -->
                    <div class="space-y-2">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <InputTextArea @bind-Value="SelectedItem.Description"
                                       class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm resize-none"
                                       id="description"
                                       rows="3"
                                       placeholder="Enter plan description" />
                        <ValidationMessage For="@(() => SelectedItem.Description)" class="text-red-500 text-sm" />
                    </div>

                <!-- Plan Limits Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-2">
                        <h3 class="text-lg font-medium text-gray-900">Plan Limits</h3>
                        <p class="text-sm text-gray-500">Set the maximum limits for this subscription plan.</p>
                    </div>

                    <!-- Maximum Vehicles and Users -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <label for="maxVehicles" class="block text-sm font-medium text-gray-700">Maximum Vehicles *</label>
                            <InputNumber @bind-Value="SelectedItem.MaxVehicles"
                                         class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm"
                                         id="maxVehicles"
                                         placeholder="1" />
                            <ValidationMessage For="@(() => SelectedItem.MaxVehicles)" class="text-red-500 text-sm" />
                        </div>
                        <div class="space-y-2">
                            <label for="maxUsers" class="block text-sm font-medium text-gray-700">Maximum Users *</label>
                            <InputNumber @bind-Value="SelectedItem.MaxUsers"
                                         class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm"
                                         id="maxUsers"
                                         placeholder="1" />
                            <ValidationMessage For="@(() => SelectedItem.MaxUsers)" class="text-red-500 text-sm" />
                        </div>
                    </div>
                </div>

                <!-- Discount Settings Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-2">
                        <h3 class="text-lg font-medium text-gray-900">Discount Settings</h3>
                        <p class="text-sm text-gray-500">Configure discount percentages for different services.</p>
                    </div>

                    <!-- Discount Percentages -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <label for="standAloneQuotesDiscount" class="block text-sm font-medium text-gray-700">Stand Alone Quotes Discount (%)</label>
                            <InputNumber @bind-Value="SelectedItem.StandAloneQuotesDiscountPercent"
                                         class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm"
                                         id="standAloneQuotesDiscount"
                                         placeholder="0" />
                            <ValidationMessage For="@(() => SelectedItem.StandAloneQuotesDiscountPercent)" class="text-red-500 text-sm" />
                        </div>
                        <div class="space-y-2">
                            <label for="registrationTitleServicesDiscount" class="block text-sm font-medium text-gray-700">Registration & Title Services Discount (%)</label>
                            <InputNumber @bind-Value="SelectedItem.RegistrationTitleServicesDiscountPercent"
                                         class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm"
                                         id="registrationTitleServicesDiscount"
                                         placeholder="0" />
                            <ValidationMessage For="@(() => SelectedItem.RegistrationTitleServicesDiscountPercent)" class="text-red-500 text-sm" />
                        </div>
                    </div>
                </div>

                <!-- Additional Settings Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-2">
                        <h3 class="text-lg font-medium text-gray-900">Additional Settings</h3>
                        <p class="text-sm text-gray-500">Configure display order and plan features.</p>
                    </div>

                    <!-- Display Order and Features -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <label for="displayOrder" class="block text-sm font-medium text-gray-700">Display Order</label>
                            <InputNumber @bind-Value="SelectedItem.DisplayOrder"
                                         class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm"
                                         id="displayOrder"
                                         placeholder="0" />
                            <ValidationMessage For="@(() => SelectedItem.DisplayOrder)" class="text-red-500 text-sm" />
                        </div>
                        <div class="space-y-2">
                            <label for="features" class="block text-sm font-medium text-gray-700">Features (JSON or comma-separated)</label>
                            <InputTextArea @bind-Value="SelectedItem.Features"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm resize-none"
                                           id="features"
                                           rows="2"
                                           placeholder="Enter features" />
                            <ValidationMessage For="@(() => SelectedItem.Features)" class="text-red-500 text-sm" />
                        </div>
                    </div>
                </div>

                <!-- Plan Options Section -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-2">
                        <h3 class="text-lg font-medium text-gray-900">Plan Options</h3>
                        <p class="text-sm text-gray-500">Configure additional plan features and settings.</p>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div class="flex items-center space-x-3">
                            <InputCheckbox @bind-Value="SelectedItem.HasDirectSubmission"
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                           id="hasDirectSubmission" />
                            <label class="block text-sm text-gray-700" for="hasDirectSubmission">
                                Has Direct Submission
                            </label>
                        </div>
                        <div class="flex items-center space-x-3">
                            <InputCheckbox @bind-Value="SelectedItem.HasDedicatedClientManager"
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                           id="hasDedicatedClientManager" />
                            <label class="block text-sm text-gray-700" for="hasDedicatedClientManager">
                                Has Dedicated Client Manager
                            </label>
                        </div>
                        <div class="flex items-center space-x-3">
                            <InputCheckbox @bind-Value="SelectedItem.IsPopular"
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                           id="isPopular" />
                            <label class="block text-sm text-gray-700" for="isPopular">
                                Mark as Popular Plan
                            </label>
                        </div>
                        <div class="flex items-center space-x-3">
                            <InputCheckbox @bind-Value="SelectedItem.IsActive"
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                           id="isActive" />
                            <label class="block text-sm text-gray-700" for="isActive">
                                Active
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="sticky bottom-0 bg-white border-t border-gray-200 pt-6 mt-8">
                    <div class="flex flex-col sm:flex-row gap-4 justify-end">
                        <button type="button"
                                disabled="@IsBusy"
                                class="inline-flex items-center justify-center px-6 py-3 border text-sm font-medium rounded-lg transition-colors duration-200 @(IsBusy ? "border-gray-200 text-gray-400 bg-gray-50 cursor-not-allowed" : "border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary")"
                                @onclick="() => CloseDialog()">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Cancel
                        </button>
                        <button type="submit"
                                disabled="@IsBusy"
                                class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white transition-colors duration-200 shadow-sm @(IsBusy ? "bg-gray-400 cursor-not-allowed" : "bg-primary hover:bg-primaryDark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary")">
                            @if (IsBusy)
                            {
                                <!-- Loading spinner -->
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <text>Saving...</text>
                            }
                            else
                            {
                                <!-- Save icon -->
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <text>Save Plan</text>
                            }
                        </button>
                    </div>
                </div>
            </EditForm>
        </div>
    </div>
}
