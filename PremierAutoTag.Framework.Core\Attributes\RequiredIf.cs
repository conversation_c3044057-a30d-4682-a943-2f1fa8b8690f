﻿using System.ComponentModel.DataAnnotations;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false, Inherited = true)]
public class RequiredIfAttribute : ValidationAttribute
{
    private readonly string _otherProperty;
    private readonly object _targetValue;

    public RequiredIfAttribute(string otherProperty, object targetValue)
    {
        _otherProperty = otherProperty;
        _targetValue = targetValue;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        var otherPropertyInfo = validationContext.ObjectType.GetProperty(_otherProperty);

        if (otherPropertyInfo == null)
        {
            return new ValidationResult($"Property {_otherProperty} not found.");
        }

        var otherPropertyValue = otherPropertyInfo.GetValue(validationContext.ObjectInstance);

        if (object.Equals(otherPropertyValue, _targetValue) && value == null)
        {
            return new ValidationResult(ErrorMessage ?? $"{validationContext.MemberName} is required because {_otherProperty} is {_targetValue}.");
        }

        return ValidationResult.Success;
    }
}