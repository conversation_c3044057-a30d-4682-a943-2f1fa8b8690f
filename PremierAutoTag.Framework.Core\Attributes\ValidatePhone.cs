﻿using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace PremierAutoTag.Framework.Core.Attributes
{
    public class ValidatePhone : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            var emailOrPhone = value as string;

            // Is this a valid email address?
            if (!string.IsNullOrEmpty(emailOrPhone) && this.IsValidPhoneNumber(emailOrPhone))
            {
                // Assume phone number
                return true;
            }

            // Not valid email address or phone
            return false;
        }


        private bool IsValidPhoneNumber(string phoneNumberToValidate)
        {
            // Regualr expression from https://stackoverflow.com/a/8909045/894792 for phone numbers
            //var regex = new Regex(@"^\+92[0-9][0-9\s]{9,13}$");
            //var regex = new Regex(@"^\+92\d{10}$");
            // var regex = new Regex(@"^\+92(?:[0-9] ?){9,13}[0-9]$");
            //  var regex = new Regex(@"^(\+(92)?(0)?)(3)([0-9]{9})$");
            //var regex = new Regex(@"^\+92[0-9]{10}$");
            //var regex = new Regex(@"^(?:\+)?\d{1,15}$");
            var regex = new Regex(@"^(?:(\+923\d{9})|(03\d{9}))$");
            return regex.IsMatch(phoneNumberToValidate);
        }
    }
}
