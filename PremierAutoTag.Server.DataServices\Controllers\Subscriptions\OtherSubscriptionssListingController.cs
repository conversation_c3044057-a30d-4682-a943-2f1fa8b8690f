﻿using Microsoft.AspNetCore.Mvc;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.OtherSubscriptions;
namespace PremierAutoTag.Server.DataServices.Controller.OtherSubscriptions;
[ApiController, Route("api/[controller]/[action]")]
public class OtherSubscriptionssListingController : ControllerBase, IOtherSubscriptionsListingDataService
{

	private readonly IOtherSubscriptionsListingDataService dataService;

	public OtherSubscriptionssListingController(IOtherSubscriptionsListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpGet]
	public async Task<PagedDataList<OtherSubscriptionsListingBusinessObject>> GetPaginatedItems([FromQuery] OtherSubscriptionsFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
