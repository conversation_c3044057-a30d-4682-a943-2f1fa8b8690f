﻿@page "/change-password"
@layout MainLayout
@attribute [Authorize]

@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Framework.Core.UIServices;
@using PremierAutoTag.Razor.Features.Profiles.Form;
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.ServiceContracts.Features.ChangePassword;
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Logging
@inherits FormBase<ChangePasswordFormBusinessObject,ChangePasswordFormViewModel, string, IChangePasswordFormDataService>

<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:p-8">
            <div class="mb-6">
                <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">Change Password</h1>
                <p class="text-gray-600">Update your account password for security</p>
            </div>

            @if (SelectedItem != null)
            {
                <EditForm Enhance method="post" Model="SelectedItem" OnValidSubmit="OnFormSubmit" FormName="ChangePassword">
                    <DataAnnotationsValidator />
                    <ValidationSummary class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700" />

                    <div class="space-y-6">
                        <!-- Current Password -->
                        <div>
                            <label for="currentPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                Current Password <span class="text-red-500">*</span>
                            </label>
                            <InputText type="password"
                                      @bind-Value="SelectedItem.CurrentPassword"
                                      id="currentPassword"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                                      placeholder="Enter your current password"
                                      autocomplete="current-password" />
                            <ValidationMessage For="() => SelectedItem.CurrentPassword" class="text-red-600 text-sm mt-1" />
                        </div>

                        <!-- New Password -->
                        <div>
                            <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                New Password <span class="text-red-500">*</span>
                            </label>
                            <InputText type="password"
                                      @bind-Value="SelectedItem.NewPassword"
                                      id="newPassword"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                                      placeholder="Enter your new password"
                                      autocomplete="new-password" />
                            <ValidationMessage For="() => SelectedItem.NewPassword" class="text-red-600 text-sm mt-1" />
                            <p class="text-sm text-gray-500 mt-1">Password must be at least 6 characters long</p>
                        </div>

                        <!-- Confirm New Password -->
                        <div>
                            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                Confirm New Password <span class="text-red-500">*</span>
                            </label>
                            <InputText type="password"
                                      @bind-Value="SelectedItem.ConfirmPassword"
                                      id="confirmPassword"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                                      placeholder="Confirm your new password"
                                      autocomplete="new-password" />
                            <ValidationMessage For="() => SelectedItem.ConfirmPassword" class="text-red-600 text-sm mt-1" />
                        </div>

                        <!-- Form Actions -->
                        <div class="flex flex-col sm:flex-row gap-3 pt-4">
                            <button type="submit"
                                    disabled="@IsBusy"
                                    class="flex-1 bg-primary hover:bg-primaryDark disabled:bg-gray-400 text-white font-medium py-2.5 px-4 rounded-lg transition-colors focus:ring-2 focus:ring-primary focus:ring-offset-2">
                                @if (IsBusy)
                                {
                                    <span class="flex items-center justify-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Changing Password...
                                    </span>
                                }
                                else
                                {
                                    <span>Change Password</span>
                                }
                            </button>

                            <button type="button"
                                    @onclick="ResetForm"
                                    class="flex-1 sm:flex-none bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2.5 px-4 rounded-lg transition-colors">
                                Reset
                            </button>
                        </div>
                    </div>
                </EditForm>
            }
            else
            {
                <div class="flex items-center justify-center py-8">
                    <div class="text-center">
                        <svg class="animate-spin h-8 w-8 text-primary mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="text-gray-600">Loading...</p>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Set the user ID from authenticated user - framework will handle the rest automatically
            if (AuthenticatedUser != null && !string.IsNullOrEmpty(AuthenticatedUser.UserId))
            {
                Id = AuthenticatedUser.UserId;
            }

            // Let framework handle automatic data loading via GetItemByIdAsync() and ConvertBusinessModelToViewModel()
            await base.OnInitializedAsync();
        }
        catch (Exception ex)
        {
            ValidationError = $"Error initializing change password form: {ex.Message}";
            Logger.LogError(ex, "Error initializing ChangePassword component for user {UserId}", AuthenticatedUser?.UserId);
        }
    }

    // Framework automatically handles conversion using Clone<T>() extension method
    // No need to override ConvertViewModelToBusinessModel and ConvertBusinessModelToViewModel
    // unless custom logic is required

    public override async Task OnAfterSaveAsync(string key)
    {
        // Show success message using framework's AlertService
        AlertService?.Show("Password changed successfully!", MessageTypes.Success);

        // Clear the form for security
        if (SelectedItem != null)
        {
            SelectedItem.CurrentPassword = "";
            SelectedItem.NewPassword = "";
            SelectedItem.ConfirmPassword = "";
        }

        await base.OnAfterSaveAsync(key);
    }

    private void ResetForm()
    {
        if (SelectedItem != null)
        {
            SelectedItem.CurrentPassword = "";
            SelectedItem.NewPassword = "";
            SelectedItem.ConfirmPassword = "";
        }
    }
}
