﻿@page "/Account/Login"
@layout WebsiteLayout

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.Server.Data.Entities

@inject SignInManager<ApplicationUser> SignInManager
@inject ILogger<Login> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Log in</PageTitle>

<section class="">
    <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <EditForm Enhance Model="Input" method="post" OnValidSubmit="LoginUser" FormName="login" class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
            <DataAnnotationsValidator />
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="text-red-500 text-sm mb-2">@errorMessage</div>
            }
            <div>
                <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Login to your account</h1>
                <h2 class="text-gray-600 text-sm md:text-base text-center">
                    Welcome back! Please enter your details.
                </h2>
            </div>

            <InputText @bind-Value="Input.Email"
                       class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                       placeholder="Your Email" />
            <ValidationMessage For="() => Input.Email" class="text-red-500 text-sm mt-1" />

            <InputText @bind-Value="Input.Password" type="password"
                       class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                       placeholder="Password" />
            <ValidationMessage For="() => Input.Password" class="text-red-500 text-sm mt-1" />

            <div class="flex items-center justify-between ">
                <div class="flex items-center">
                    <label for="remember" class="flex items-center cursor-pointer">
                        <div class="custom-checkbox">
                            <InputCheckbox id="remember" @bind-Value="Input.RememberMe" />
                            <span class="checkmark"></span>
                        </div>
                        <span class="ml-2 text-sm text-gray-500">Remember me</span>
                    </label>
                </div>

                <a href="Account/ForgotPassword"
                   class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4 ">
                    Forgot
                    password
                </a>
            </div>

            <button class="w-full py-2 md:py-2.5 rounded-lg bg-primary hover:bg-primaryDark text-white text-sm md:text-base font-semibold"
                    type="submit">
                Login
            </button>

            <div class="flex justify-center gap-1">
                <span class="text-sm text-gray-600">Don’t have an account?</span>
                <a href="/Account/Register"
                   class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4 ">
                    Sign
                    up
                </a>
            </div>

        </EditForm>
    </div>
</section>

@code {
    private string? errorMessage;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (HttpMethods.IsGet(HttpContext.Request.Method))
        {
            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);
        }
    }

    public async Task LoginUser()
    {
        // This doesn't count login failures towards account lockout
        // To enable password failures to trigger account lockout, set lockoutOnFailure: true
        var result = await SignInManager.PasswordSignInAsync(Input.Email, Input.Password, Input.RememberMe, lockoutOnFailure: false);
        if (result.Succeeded)
        {
            Logger.LogInformation("User logged in.");

            // If there's a specific return URL, use it; otherwise use role-based redirection
            if (!string.IsNullOrEmpty(ReturnUrl) && ReturnUrl != "/")
            {
                RedirectManager.RedirectTo(ReturnUrl);
            }
            else
            {
                // Use role-based redirection to appropriate dashboard
                var user = await UserManager.FindByEmailAsync(Input.Email);
                if (user != null)
                {
                    await RedirectManager.RedirectToRoleBasedDashboard(user.Id);
                }
                else
                {
                    RedirectManager.RedirectTo("/");
                }
            }
        }
        else if (result.RequiresTwoFactor)
        {
            RedirectManager.RedirectTo(
                "Account/LoginWith2fa",
                new() { ["returnUrl"] = ReturnUrl, ["rememberMe"] = Input.RememberMe });
        }
        else if (result.IsLockedOut)
        {
            Logger.LogWarning("User account locked out.");
            RedirectManager.RedirectTo("Account/Lockout");
        }
        else
        {
            errorMessage = "Error: Invalid login attempt.";
        }
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";

        [Required]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [Display(Name = "Remember me?")]
        public bool RememberMe { get; set; }
    }
}
