﻿using Microsoft.EntityFrameworkCore;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.Features.MySubscription;

namespace PremierAutoTag.Server.DataServices.Features.MySubscription;
public class MySubscriptionServerSideListingDataService : ServerSideListingDataService<MySubscriptionListingBusinessObject, MySubscriptionFilterBusinessObject>, IMySubscriptionListingDataService
{
	private readonly ApplicationDbContext _context;

	public MySubscriptionServerSideListingDataService(ApplicationDbContext context)
	{
		_context = context;
	}

	public override IQueryable<MySubscriptionListingBusinessObject> GetQuery(MySubscriptionFilterBusinessObject filterBusinessObject)
	{
		var query = from us in _context.UserSubscriptions
					 where !us.IsDelete
					 join sp in _context.SubscriptionPlans on us.SubscriptionPlanId equals sp.Id
					 let latestPayment = _context.StripePayments
						 .Where(p => p.UserId == us.UserId && p.SubscriptionPlanId == us.SubscriptionPlanId && !p.IsDelete)
						 .OrderByDescending(p => p.CreatedAt)
						 .FirstOrDefault()
					 select new MySubscriptionListingBusinessObject
					 {
						 // User Subscription Information
						 Id = us.Id,
						 UserId = us.UserId,
						 SubscriptionPlanId = us.SubscriptionPlanId,
						 Status = us.Status,
						 StartDate = us.StartDate,
						 EndDate = us.EndDate,
						 NextBillingDate = us.NextBillingDate,
						 CancelledAt = us.CancelledAt,
						 CancellationReason = us.CancellationReason,
						 AutoRenew = us.AutoRenew,

						 // Subscription Plan Details
						 PlanName = sp.Name,
						 PlanDescription = sp.Description,
						 AnnualPrice = sp.AnnualPrice,
						 MaxVehicles = sp.MaxVehicles,
						 MaxUsers = sp.MaxUsers,
						 StandAloneQuotesDiscountPercent = sp.StandAloneQuotesDiscountPercent,
						 RegistrationTitleServicesDiscountPercent = sp.RegistrationTitleServicesDiscountPercent,
						 HasDirectSubmission = sp.HasDirectSubmission,
						 HasDedicatedClientManager = sp.HasDedicatedClientManager,
						 Features = sp.Features,
						 RequiresPayment = sp.RequiresPayment,

						 // Payment Information (for paid plans)
						 StripeCustomerId = us.StripeCustomerId,
						 StripeSubscriptionId = us.StripeSubscriptionId,
						 PaymentMethod = latestPayment != null ? "Credit Card" : null,
						 LastPaymentDate = latestPayment != null ? latestPayment.PaymentCompletedAt ?? latestPayment.CreatedAt : null,
						 LastPaymentAmount = latestPayment != null ? latestPayment.Amount : null,
						 PaymentStatus = latestPayment != null ? latestPayment.Status : null
					 };

		// Apply user-specific filter (most important filter)
		if (!string.IsNullOrEmpty(filterBusinessObject.UserId))
		{
			query = query.Where(x => x.UserId == filterBusinessObject.UserId);
		}

		// Apply status filter
		if (!string.IsNullOrEmpty(filterBusinessObject.Status))
		{
			query = query.Where(x => x.Status == filterBusinessObject.Status);
		}

		// Apply active filter
		if (filterBusinessObject.IsActive.HasValue)
		{
			if (filterBusinessObject.IsActive.Value)
			{
				query = query.Where(x => x.Status == "Active");
			}
			else
			{
				query = query.Where(x => x.Status != "Active");
			}
		}

		// Order by most recent first
		query = query.OrderByDescending(x => x.StartDate);

		return query;
	}
}
