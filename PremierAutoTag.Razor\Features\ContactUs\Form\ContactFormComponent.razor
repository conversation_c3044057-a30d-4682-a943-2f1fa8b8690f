﻿@page "/contact"

@rendermode @(new InteractiveServerRenderMode())
@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Razor.Features.Contact;
@using PremierAutoTag.ServiceContracts.Features.Contact;
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@inherits FormBase<ContactFormBusinessObject,ContactFormViewModel, long, IContactFormDataService>

@if (SelectedItem != null)
{
    <EditForm Enhance method="post" Model="SelectedItem" OnValidSubmit="OnFormSubmit" FormName="Contact">
        <DataAnnotationsValidator></DataAnnotationsValidator>
        <div class="grid grid-cols-12 px-4 md:px-auto">
            <div class="col-span-12 md:col-span-8 md:col-start-3">
                <div class="mb-6 md:mb-16">
                    <h2 class="text-gray-900 text-center font-semibold text-2xl md:text-4xl mb-4">Contact us</h2>
                    <span class="text-gray-500 text-center text-sm md:text-lg block">
                        Get in touch with us for any
                        questions or
                        assistance
                        with
                        your vehicle’s registration, license plates, or renewals—our team is here to help!
                    </span>
                </div>

                @if (!string.IsNullOrEmpty(SuccessMessage))
                {
                    <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">@SuccessMessage</p>
                            </div>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(ValidationError))
                {
                    <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-red-800">@ValidationError</p>
                            </div>
                        </div>
                    </div>
                }



                <div class="flex flex-col gap-4 md:gap-8">
                    <div>
                        <label class="text-gray-900 font-medium text-sm mb-2 block">Your name (optional)</label>
                        <InputText @bind-Value="SelectedItem.Name" class="w-full py-2 md:py-3 border focus:ring-1 focus:ring-gray-300 bg-white outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3" placeholder="Enter your full name" />
                    </div>
                    <div>
                        <label class="text-gray-900 font-medium text-sm mb-2 block">Your email *</label>
                        <InputText @bind-Value="SelectedItem.Email" class="w-full py-2 md:py-3 border focus:ring-1 focus:ring-gray-300 bg-white outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3" placeholder="Enter your email address" />
                        <ValidationMessage For="@(() => SelectedItem.Email)" class="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                        <label class="text-gray-900 font-medium text-sm mb-2 block">Your phone number *</label>
                        <InputText @bind-Value="SelectedItem.PhoneNumber" class="w-full py-2 md:py-3 border focus:ring-1 focus:ring-gray-300 bg-white outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3" placeholder="Enter your phone number" />
                        <ValidationMessage For="@(() => SelectedItem.PhoneNumber)" class="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                        <label class="text-gray-900 font-medium text-sm mb-2 block">Subject *</label>
                        <InputText @bind-Value="SelectedItem.Subject" class="w-full py-2 md:py-3 border focus:ring-1 focus:ring-gray-300 bg-white outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3" placeholder="Let us know how we can help you" />
                        <ValidationMessage For="@(() => SelectedItem.Subject)" class="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                        <label class="text-gray-900 font-medium text-sm mb-2 block">Your message *</label>
                        <InputTextArea @bind-Value="SelectedItem.Message" class="w-full py-2 md:py-3 border focus:ring-1 focus:ring-gray-300 bg-white outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3 min-h-[120px]" placeholder="Please provide details about your inquiry" />
                        <ValidationMessage For="@(() => SelectedItem.Message)" class="text-red-500 text-sm mt-1" />
                    </div>

                    <button type="submit" disabled="@IsBusy" class="w-fit px-3 md:px-5 py-2 md:py-2.5 flex items-center gap-2 justify-center rounded-lg bg-primary cursor-pointer hover:bg-primaryDark text-white text-sm md:text-base font-medium">
                        @if (IsBusy)
                        {
                            <span class="flex items-center gap-2">
                                <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Saving...
                            </span>
                        }
                        else
                        {
                            <text>Send Message</text>
                        }
                    </button>
                </div>

            </div>
        </div>
    </EditForm>
}

@code {

    public string? SuccessMessage { get; set; }
    public override async Task OnAfterSaveAsync(long key)
    {
       SelectedItem = await CreateSelectedItem();
       SuccessMessage = "Email sent successfully";
       await Task.CompletedTask;
    }
}