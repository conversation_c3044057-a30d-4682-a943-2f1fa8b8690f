using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Server.Data.Entities
{
    [Index(nameof(Id))]
    public class SubscriptionPlan : BaseEntity
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = "";

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        public decimal AnnualPrice { get; set; }

        [Required]
        public int MaxVehicles { get; set; }

        [Required]
        public int MaxUsers { get; set; }

        // Discount percentages for different services
        public int StandAloneQuotesDiscountPercent { get; set; }

        public int RegistrationTitleServicesDiscountPercent { get; set; }

        // Feature flags
        public bool HasDirectSubmission { get; set; }

        public bool HasDedicatedClientManager { get; set; }

        [StringLength(1000)]
        public string? Features { get; set; }

        public bool IsPopular { get; set; }

        public int DisplayOrder { get; set; }

        // Payment requirement flag - automatically set based on price
        public bool RequiresPayment { get; set; }

        // Navigation properties
        public virtual ICollection<UserProfile> UserProfiles { get; set; } = new List<UserProfile>();
    }
}
