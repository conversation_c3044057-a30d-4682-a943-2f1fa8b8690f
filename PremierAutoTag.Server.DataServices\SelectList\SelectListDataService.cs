﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.SelectList;
using System.Text.Json;

namespace PremierAutoTag.Server.DataServices.SelectList
{
    public class SelectListDataService : ISelectListDataService
    {
        private readonly IDistributedCache _cache;
     
        private readonly ApplicationDbContext _context;

        public SelectListDataService(ApplicationDbContext context, IDistributedCache cache)
        {
            _context = context;
            _cache = cache;
        }

       
    }
}
