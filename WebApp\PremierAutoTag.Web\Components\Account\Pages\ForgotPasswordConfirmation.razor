﻿@page "/Account/ForgotPasswordConfirmation"
@layout WebsiteLayout

@using PremierAutoTag.Razor.Layout

<PageTitle>Password Reset Sent</PageTitle>

<section class="">
    <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <div class="bg-white max-w-2xl w-full flex flex-col gap-4 md:gap-6 rounded-3xl p-6 md:p-10 border border-gray-200">

            <!-- Success Icon -->
            <div class="flex justify-center mb-4">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
            </div>

            <!-- Header -->
            <div class="text-center">
                <h1 class="text-black font-bold mb-2 text-xl md:text-2xl">Check Your Email</h1>
                <h2 class="text-gray-600 text-sm md:text-base">
                    We've sent password reset instructions to your email address.
                </h2>
            </div>

            <!-- Message -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">
                            Please check your email and click the link to reset your password.
                            If you don't see the email, check your spam folder.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="text-center text-gray-600 text-sm">
                <p class="mb-2">Didn't receive the email?</p>
                <ul class="text-left space-y-1 mb-4">
                    <li>• Check your spam or junk folder</li>
                    <li>• Make sure you entered the correct email address</li>
                    <li>• Wait a few minutes for the email to arrive</li>
                </ul>
            </div>

            <!-- Actions -->
            <div class="flex flex-col gap-3">
                <a href="/Account/ForgotPassword"
                   class="w-full py-2 md:py-2.5 rounded-lg bg-primary hover:bg-primaryDark text-white text-sm md:text-base font-semibold text-center">
                    Try Again
                </a>

                <a href="/Account/Login"
                   class="w-full py-2 md:py-2.5 rounded-lg border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm md:text-base font-semibold text-center">
                    Back to Login
                </a>
            </div>

            <!-- Footer -->
            <div class="flex justify-center gap-1 text-center">
                <span class="text-sm text-gray-600">Need help?</span>
                <a href="/contact"
                   class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4">
                    Contact Support
                </a>
            </div>

        </div>
    </div>
</section>
