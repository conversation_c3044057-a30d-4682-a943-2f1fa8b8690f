﻿
using Microsoft.EntityFrameworkCore;
using PremierAutoTag.Framework.Core;

namespace PremierAutoTag.Server.DataServices
{
    public abstract class ServerSideListingDataService<TListingModel, TFilterBusinessObject> : IListingDataService<TListingModel, TFilterBusinessObject>
       where TFilterBusinessObject : BaseFilterBusinessObject
    {
        public async Task<PagedDataList<TListingModel>> GetPaginatedItems(TFilterBusinessObject filterBusinessObject)
        {
            return await MaterializeQueryAsync(filterBusinessObject, GetQuery(filterBusinessObject), GetTotalRows());
        }

        public async Task<PagedDataList<TListingModel>> MaterializeQueryAsync(TFilterBusinessObject filterBusinessObject, IQueryable<TListingModel> query, int totalRows)
        {
            var resultBusinessObject = new PagedDataList<TListingModel>();
            if (query != null)
            {

                if (filterBusinessObject.UsePagination)
                {
                    resultBusinessObject.TotalRows = totalRows;

                    if (resultBusinessObject.TotalRows == -1)
                        resultBusinessObject.TotalRows = query.Count();

                    resultBusinessObject.TotalPages = Convert.ToInt32(Math.Ceiling(resultBusinessObject.TotalRows / (double)filterBusinessObject.RowsPerPage));

                    query = query.Skip((filterBusinessObject.CurrentIndex - 1) * filterBusinessObject.RowsPerPage).Take(filterBusinessObject.RowsPerPage);
                }

                if (query is IAsyncEnumerable<TListingModel>)
                {
                    resultBusinessObject.Items = await query.ToListAsync();
                }
                else
                {
                    resultBusinessObject.Items = query.ToList();
                }
            }
            return await OnItemsMaterialized(resultBusinessObject);
        }

        public virtual Task<PagedDataList<TListingModel>> OnItemsMaterialized(PagedDataList<TListingModel> pagedDataList)
        {
            return Task.FromResult(pagedDataList);
        }

        public int GetTotalRows()
        {
            return -1;
        }

        public abstract IQueryable<TListingModel> GetQuery(TFilterBusinessObject filterBusinessObject);

        public IQueryable<TListingModel> EmptyResult()
        {
            return Array.Empty<TListingModel>().AsQueryable();
        }
    }
}
