using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.UIServices;
using PremierAutoTag.Razor.Features.AdminDashboard;
using PremierAutoTag.Razor.SelectList;
using PremierAutoTag.ServiceContracts.Features.AdminDashboard;
using PremierAutoTag.ServiceContracts.SelectList;
using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
                                                               using PremierAutoTag.Razor.Features.SubscriptionPlan; 
                                                               using PremierAutoTag.ServiceContracts.Features.UserDashboard;
                                                               using PremierAutoTag.Razor.Features.UserDashboard;
                                                               using PremierAutoTag.ServiceContracts.Features.DealerDashboard;
                                                               using PremierAutoTag.Razor.Features.DealerDashboard;
                                                               using PremierAutoTag.Razor.Services;
                                                               using PremierAutoTag.ServiceContracts.Services;
                                                               using PremierAutoTag.ServiceContracts.Features.MyProfile;
                                                               using PremierAutoTag.Razor.Features.MyProfile; 
                                                               using PremierAutoTag.ServiceContracts.Features.ChangePassword;
                                                               using PremierAutoTag.Razor.Features.Profiles.Form;
                                                               using PremierAutoTag.ServiceContracts.Features.Contact;
                                                               using PremierAutoTag.Razor.Features.Contact; 
                                                               using PremierAutoTag.ServiceContracts.Features.DealershipProfile;
                                                               using PremierAutoTag.Razor.Features.DealershipProfile; 
                                                               //##NewServiceNamespace##
namespace PremierAutoTag.Web.Client
{
    public static class ServiceRegistrar
    {
        public static void RegisterServices(this IServiceCollection services)
        {
            services.AddSingleton<AlertService>();
            services.AddScoped<KtDialogService>();
            services.AddScoped<KtNotificationService>();
            services.AddScoped<IMessageCenter, MessageCenter>();
            services.AddScoped<IAuthenticatedUser, WasmClientAuthenticatedUser>();
            services.AddScoped<ILocalStorageService, LocalStorageService>();
            services.AddScoped<ISelectListDataService, SelectListClientDataService>();
            services.AddScoped<IAdminDashboardListingDataService, AdminDashboardClientSideListingDataService>();
            services.AddScoped<ISubscriptionPlanFormDataService, SubscriptionPlanClientSideFormDataService>(); 
 services.AddScoped<ISubscriptionPlanListingDataService, SubscriptionPlanClientSideListingDataService>(); 
 services.AddScoped<IUserDashboardListingDataService, UserDashboardClientSideListingDataService>(); 
 services.AddScoped<IDealerDashboardListingDataService, DealerDashboardClientSideListingDataService>();
            services.AddScoped<IStripePaymentService, StripePaymentClientService>();
 services.AddScoped<IMyProfileFormDataService, MyProfileClientSideFormDataService>(); 
 services.AddScoped<IChangePasswordFormDataService, ChangePasswordClientSideFormDataService>(); 
 services.AddScoped<IContactFormDataService, ContactClientSideFormDataService>(); 
 services.AddScoped<IDealershipProfileFormDataService, DealershipProfileClientSideFormDataService>(); 
 //##NewService##
        }
    }
}
