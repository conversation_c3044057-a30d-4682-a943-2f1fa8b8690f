﻿using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Razor.Features.DealershipProfile;
public class DealershipProfileFormViewModel : ObservableBase
{
	private string _id = "";
	private string _dealerName = "";
	private string _dealerId = "";
	private string _taxId = "";
	private int _stateId;
	private bool _subsidary;
	private string _address = "";
	private string _phone = "";
	private string _authorizedCompany = "";
	private string _administrativeEmail = "";
	private string _administrativePhone = "";
	private DEALERSHIP_IOT_TYPES _dealershipIOTTypes;
	private VEHICAL_ON_IOT_TYPES _vehicalOnIOTTypes;
	private VEHICAL_SOLD_IOT _vehicalSoldIOT;

	public string Id
	{
		get => _id;
		set => SetField(ref _id, value);
	}

	[Required(ErrorMessage = "Dealership name is required")]
	[StringLength(150, ErrorMessage = "Dealership name cannot exceed 150 characters")]
	public string DealerName
	{
		get => _dealerName;
		set => SetField(ref _dealerName, value);
	}

	[Required(ErrorMessage = "Dealer ID is required")]
	[StringLength(50, ErrorMessage = "Dealer ID cannot exceed 50 characters")]
	public string DealerId
	{
		get => _dealerId;
		set => SetField(ref _dealerId, value);
	}

	[Required(ErrorMessage = "Tax ID is required")]
	[StringLength(50, ErrorMessage = "Tax ID cannot exceed 50 characters")]
	public string TaxId
	{
		get => _taxId;
		set => SetField(ref _taxId, value);
	}

	[Required(ErrorMessage = "State is required")]
	public int StateId
	{
		get => _stateId;
		set => SetField(ref _stateId, value);
	}

	public bool Subsidary
	{
		get => _subsidary;
		set => SetField(ref _subsidary, value);
	}

	[Required(ErrorMessage = "Address is required")]
	[StringLength(150, ErrorMessage = "Address cannot exceed 150 characters")]
	public string Address
	{
		get => _address;
		set => SetField(ref _address, value);
	}

	[StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
	public string Phone
	{
		get => _phone;
		set => SetField(ref _phone, value);
	}

	[Required(ErrorMessage = "Authorized company is required")]
	[StringLength(100, ErrorMessage = "Authorized company cannot exceed 100 characters")]
	public string AuthorizedCompany
	{
		get => _authorizedCompany;
		set => SetField(ref _authorizedCompany, value);
	}

	[Required(ErrorMessage = "Administrative email is required")]
	[EmailAddress(ErrorMessage = "Please enter a valid email address")]
	[StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
	public string AdministrativeEmail
	{
		get => _administrativeEmail;
		set => SetField(ref _administrativeEmail, value);
	}

	[StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
	public string AdministrativePhone
	{
		get => _administrativePhone;
		set => SetField(ref _administrativePhone, value);
	}

	[Required(ErrorMessage = "Dealership lot type is required")]
	public DEALERSHIP_IOT_TYPES DealershipIOTTypes
	{
		get => _dealershipIOTTypes;
		set => SetField(ref _dealershipIOTTypes, value);
	}

	[Required(ErrorMessage = "Number of vehicles on lot is required")]
	public VEHICAL_ON_IOT_TYPES VehicalOnIOTTypes
	{
		get => _vehicalOnIOTTypes;
		set => SetField(ref _vehicalOnIOTTypes, value);
	}

	[Required(ErrorMessage = "Average vehicles sold per month is required")]
	public VEHICAL_SOLD_IOT VehicalSoldIOT
	{
		get => _vehicalSoldIOT;
		set => SetField(ref _vehicalSoldIOT, value);
	}
}
