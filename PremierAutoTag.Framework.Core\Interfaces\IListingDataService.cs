﻿namespace PremierAutoTag.Framework.Core
{
    public interface IListingDataService<TListingBusinessObject, TFilterBusinessObject>
     where TFilterBusinessObject : BaseFilterBusinessObject
    {
        public Task<PagedDataList<TListingBusinessObject>> GetPaginatedItems(TFilterBusinessObject filterBusinessObject);

        public int GetTotalRows()
        {
            return -1;
        }
    }

    public class PagedDataList<TListViewModel>
    {
        public PagedDataList()
        {
            Items = new List<TListViewModel>();
        }
        public int TotalRows { get; set; }

        public int TotalPages { get; set; }

        public int RowsPerPage { get; set; }

        public List<TListViewModel> Items { get; set; }
    }
}
