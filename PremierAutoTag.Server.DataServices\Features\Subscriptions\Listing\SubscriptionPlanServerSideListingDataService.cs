﻿using Microsoft.EntityFrameworkCore;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;

namespace PremierAutoTag.Server.DataServices.Features.SubscriptionPlan;
public class SubscriptionPlanServerSideListingDataService : ServerSideListingDataService<SubscriptionPlanListingBusinessObject, SubscriptionPlanFilterBusinessObject>, ISubscriptionPlanListingDataService
{
	private readonly ApplicationDbContext _context;

	public SubscriptionPlanServerSideListingDataService(ApplicationDbContext context)
	{
		_context = context;
	}

	public override IQueryable<SubscriptionPlanListingBusinessObject> GetQuery(SubscriptionPlanFilterBusinessObject filterBusinessObject)
	{
		var query = _context.SubscriptionPlans
			.Where(x => !x.IsDelete)
			.Select(x => new SubscriptionPlanListingBusinessObject
			{
				Id = x.Id,
				Name = x.Name,
				Description = x.Description,
				AnnualPrice = x.AnnualPrice,
				MaxVehicles = x.MaxVehicles,
				MaxUsers = x.MaxUsers,
				StandAloneQuotesDiscountPercent = x.StandAloneQuotesDiscountPercent,
				RegistrationTitleServicesDiscountPercent = x.RegistrationTitleServicesDiscountPercent,
				HasDirectSubmission = x.HasDirectSubmission,
				HasDedicatedClientManager = x.HasDedicatedClientManager,
				Features = x.Features,
				IsPopular = x.IsPopular,
				DisplayOrder = x.DisplayOrder,
				RequiresPayment = x.RequiresPayment,
				IsActive = x.IsActive,
				CreatedAt = x.CreatedAt,
				ModifiedAt = x.ModifiedAt,
				CreatedBy = x.CreatedBy,
				ModifiedBy = x.ModifiedBy
			});

		// Apply filters
		if (!string.IsNullOrWhiteSpace(filterBusinessObject.Name))
		{
			query = query.Where(x => x.Name.Contains(filterBusinessObject.Name));
		}

		if (filterBusinessObject.MinPrice.HasValue)
		{
			query = query.Where(x => x.AnnualPrice >= filterBusinessObject.MinPrice.Value);
		}

		if (filterBusinessObject.MaxPrice.HasValue)
		{
			query = query.Where(x => x.AnnualPrice <= filterBusinessObject.MaxPrice.Value);
		}

		if (filterBusinessObject.IsActive.HasValue)
		{
			query = query.Where(x => x.IsActive == filterBusinessObject.IsActive.Value);
		}

		if (filterBusinessObject.IsPopular.HasValue)
		{
			query = query.Where(x => x.IsPopular == filterBusinessObject.IsPopular.Value);
		}

		if (filterBusinessObject.HasDirectSubmission.HasValue)
		{
			query = query.Where(x => x.HasDirectSubmission == filterBusinessObject.HasDirectSubmission.Value);
		}

		if (filterBusinessObject.HasDedicatedClientManager.HasValue)
		{
			query = query.Where(x => x.HasDedicatedClientManager == filterBusinessObject.HasDedicatedClientManager.Value);
		}

		// Default ordering by DisplayOrder, then by Name
		query = query.OrderBy(x => x.DisplayOrder).ThenBy(x => x.Name);

		return query;
	}
}
