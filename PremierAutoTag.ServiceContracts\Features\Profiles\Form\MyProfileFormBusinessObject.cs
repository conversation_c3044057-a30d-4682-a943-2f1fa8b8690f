﻿using PremierAutoTag.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.ServiceContracts.Features.MyProfile;
public class MyProfileFormBusinessObject
{
	public string Id { get; set; } = "";

	[Required(ErrorMessage = "First name is required")]
	[StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
	public string FirstName { get; set; } = "";

	[Required(ErrorMessage = "Last name is required")]
	[StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
	public string LastName { get; set; } = "";

	[StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
	public string Phone { get; set; } = "";

	public string Email { get; set; } = "";
}
