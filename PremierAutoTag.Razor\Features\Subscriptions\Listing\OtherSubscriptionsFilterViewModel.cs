﻿using PremierAutoTag.Framework.Core;
namespace PremierAutoTag.Razor.Features.OtherSubscriptions;
public class OtherSubscriptionsFilterViewModel :  BaseFilterViewModel
{
	private string? _userId;
	private bool? _isActive;
	private bool? _isPopular;
	private decimal? _minPrice;
	private decimal? _maxPrice;

	// User-specific filter to exclude current user's subscription plan
	public string? UserId
	{
		get => _userId;
		set => SetField(ref _userId, value);
	}

	// Optional filters for subscription plans
	public bool? IsActive
	{
		get => _isActive;
		set => SetField(ref _isActive, value);
	}

	public bool? IsPopular
	{
		get => _isPopular;
		set => SetField(ref _isPopular, value);
	}

	public decimal? MinPrice
	{
		get => _minPrice;
		set => SetField(ref _minPrice, value);
	}

	public decimal? MaxPrice
	{
		get => _maxPrice;
		set => SetField(ref _maxPrice, value);
	}
}
