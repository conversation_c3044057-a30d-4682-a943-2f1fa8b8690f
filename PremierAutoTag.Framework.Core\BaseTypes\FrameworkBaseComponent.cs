﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using PremierAutoTag.Framework.Core.UIServices;
using System.Reflection;
using System.Reflection.Metadata;
using System.Runtime.CompilerServices;

namespace PremierAutoTag.Framework.Core
{
    public class FrameworkBaseComponent : ComponentBase
    {

        [Parameter]
        public string? ModalSize { get; set; }

        [Parameter]
        public bool DisplayDialogCross { get; set; } = true;

        [Parameter]
        public ModalDialogConfig? DialogConfig { get; set; }

        [Inject]
        public NavigationManager? NavigationManager { get; set; }

        [Inject]
        public IMessageCenter? MessageCenter { get; set; }

        [Inject]
        public ILocalStorageService? StorageService { get; set; }

        [Inject]
        public KtDialogService? DialogService { get; set; }

        [Inject]
        public KtNotificationService? NotificationService { get; set; }

        [Inject]
        public AlertService AlertService { get; set; } = null!;

        [Inject]
        protected IServiceScopeFactory? ScopeFactory { get; set; }

        [Inject]
        public IJSRuntime? JsRuntime { get; set; }

        [Inject]
        public AuthenticationStateProvider AuthStateProvider { get; set; } = null!;

        public int TimeZoneOffset { get; set; } = 0;

        [Inject]
        public IAuthenticatedUser AuthenticatedUser { get; set; } = null!;


        public bool IsInteractiveMode { get; set; }
        protected override async Task OnInitializedAsync()
        {
            if (AuthStateProvider == null)
                throw new ArgumentNullException(nameof(AuthStateProvider));

            if (AuthenticatedUser == null)
                throw new ArgumentNullException($"Authenticated user");

            var state = await AuthStateProvider.GetAuthenticationStateAsync();
            var user = state?.User;

            if (user != null && user.Identity != null && user.Identity.IsAuthenticated)
            {
                AuthenticatedUser.UserId = user.GetUserId();
                AuthenticatedUser.ProfileName = user.GetProfileName();
                AuthenticatedUser.Phone = user.GetPhone();
                AuthenticatedUser.Email = user.GetEmail();
                AuthenticatedUser.Username = user.GetUserName();
            }

            PubSub.Hub.Default.Subscribe<Tuple<string, string, dynamic>>((x) =>
            {
                if (x.Item1 == OperationId && x.Item2 == "Dialog Closed")
                {
                    DialogService_OnClose(x.Item3);
                }
            });

            // get time zone - only in interactive mode
            if (StorageService != null && RendererInfo.IsInteractive)
            {
                var timezoneOffset = await StorageService.GetValue("timezoneoffset");
                if (!string.IsNullOrEmpty(timezoneOffset) && double.TryParse(timezoneOffset, out double offset))
                {
                    TimeZoneOffset = (int)offset;
                }
            }
            await base.OnInitializedAsync();
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            IsInteractiveMode = true;
            if (firstRender && JsRuntime is not null)
            {

                var timezoneOffset = await JsRuntime.InvokeAsync<string>("getCookie", "timezoneoffset");
                if (!string.IsNullOrEmpty(timezoneOffset) && double.TryParse(timezoneOffset, out double offset))
                {
                    TimeZoneOffset = (int)offset;
                }

            }
        }

        public string OperationId { get; set; } = Guid.NewGuid().ToString().ToLower();

        protected KeyValuePair<string, object?> P(object? value, [CallerArgumentExpression("value")] string? variableName = null)
        {
            if (variableName == null) throw new ArgumentNullException(nameof(variableName));
            return new KeyValuePair<string, object?>(variableName.Split('.').Last(), value);
        }


        [Inject]
        private ILogger<FrameworkBaseComponent>? Logger { get; set; }




        public static Type? GetTypeByFullName(string typeFullName)
        {
            // Loop through all assemblies in the current AppDomain
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies().Where(x => x.FullName?.StartsWith("K") == true))
            {
                // Look for the type in each assembly
                var type = assembly.GetType(typeFullName);
                if (type != null)
                {
                    return type; // Return the type if found
                }
            }

            return null; // Return null if the type was not found
        }

        /// <summary>
        ///  This is being used from public portal, will see it in 2nd phase.
        /// </summary>
        /// <typeparam name="TKey"></typeparam>
        /// <param name="dialogType"></param>
        /// <param name="assembly"></param>
        /// <param name="title"></param>
        /// <param name="id"></param>
        /// <param name="parameters"></param>
        protected void ShowCenterDialog2<TKey>(string dialogType, Assembly? assembly, string? title, TKey? id, params List<KeyValuePair<string, object?>> parameters)
        {
            if (!string.IsNullOrEmpty(dialogType))
            {
                // Get the type of the component based on its name
                var componentType = assembly == null ? GetTypeByFullName(dialogType) : assembly.GetType(dialogType);

                if (componentType != null && typeof(ComponentBase).IsAssignableFrom(componentType))
                {
                    ShowDialog(componentType, title ?? "", id, Size.Xl, Position_.Center, true, parameters);

                }
                else
                {
                    // Handle the case when the component type is not found or not a Blazor component
                    // For example, display an error message
                    Console.WriteLine($"Component '{componentType}' not found or not a Blazor component.");
                }
            }
        }

        public enum Size
        {
            Sm,
            Md,
            Xl,
            Xl2,
            Xl3,
            Xl4,
            Xl5,
            Xl6,
            Xl7,
            Xl8,
        }

        public enum Position_
        {
            None = 0,
            Right,
            Center
        }

        protected void ShowDialog<T>(string title, object? id, Size dialogSize = Size.Xl,
            Position_ position = Position_.Right, bool showCrossIcon = true,
            params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog(typeof(T), title, id, dialogSize, position, showCrossIcon, parameters);
        }


        protected void ShowDialog(Type dialogType, string title, object? id, Size dialogSize = Size.Xl,
           Position_ position = Position_.Right, bool showCrossIcon = true,
           params List<KeyValuePair<string, object?>> parameters)
        {

            var config = new ModalDialogConfig()
            {
                Component = dialogType,
                Title = title,
                ShowCrossIcon = DisplayDialogCross,
            };

            var parameters_ = parameters.ToDictionary();
            parameters_.Add("DialogConfig", config);
            parameters_.Add("Id", id);
            parameters_.Add("OperationId", OperationId);

            if (DialogService == null)
                throw new Exception("Dialog service is not initialized");

            switch (position)
            {
                case Position_.Center:
                    config.PositionClasses = "justify-center";
                    config.DialogContainerClasses = "grid place-items-center";
                    break;
                case Position_.Right:
                    config.PositionClasses = "justify-end min-h-full";
                    config.DialogContainerClasses = "flex justify-end";
                    break;
            }
            switch (dialogSize)
            {
                case Size.Sm:
                    config.SizeClasses = "max-w-sm w-sm";
                    break;
                case Size.Md:
                    config.SizeClasses = "max-w-md w-md";
                    break;
                case Size.Xl:
                    config.SizeClasses = "max-w-xl w-xl";
                    break;
                case Size.Xl2:
                    config.SizeClasses = "max-w-2xl w-2xl";
                    break;
                case Size.Xl3:
                    config.SizeClasses = "max-w-3xl w-3xl";
                    break;
                case Size.Xl4:
                    config.SizeClasses = "max-w-4xl w-4xl";
                    break;
                case Size.Xl5:
                    config.SizeClasses = "max-w-5xl w-5xl";
                    break;
                case Size.Xl6:
                    config.SizeClasses = "max-w-6xl w-6xl";
                    break;
                case Size.Xl7:
                    config.SizeClasses = "max-w-7xl w-7xl";
                    break;
                case Size.Xl8:
                    config.SizeClasses = "max-w-screen-2xl w-screen-2xl";
                    break;
            }

            config.Parameters = parameters_!;
            DialogService.ShowDialogAsync(config);
            StateHasChanged();

        }


        [Obsolete]
        protected void ShowSideDialog<T, TKey>(string title, TKey? id, params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog<T>(title, id, Size.Xl, Position_.Right, true, parameters);
        }

        [Obsolete]
        protected void ShowCenterDialog<T, TKey>(string title, TKey? id, params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog<T>(title, id, Size.Xl3, Position_.Center, true, parameters);
        }

        public virtual void DialogService_OnClose(dynamic obj)
        {
            Console.WriteLine("Dialog closed");
        }

        protected Task CloseDialog()
        {
            if (DialogConfig != null)
            {
                //await JsRuntime.InvokeVoidAsync("closeDialog", DialogConfig.Id);
                //await Task.Delay(50);
                DialogService?.Dialogs.Remove(DialogConfig);
            }
            return Task.CompletedTask;
        }

    }
}
