﻿using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;
using PremierAutoTag.Framework.Core;

public class CustomAuthStateProvider : AuthenticationStateProvider
{
    private readonly ILocalStorageService localStorage;
    private ClaimsPrincipal? currentUser = null;

    public static string? auth_token;
    public CustomAuthStateProvider(ILocalStorageService localStorage)
    {
        this.localStorage = localStorage;
    }

    public override Task<AuthenticationState> GetAuthenticationStateAsync() => LogInAsyncCore();
    //Task.FromResult(new AuthenticationState(currentUser));

    public Task LogInAsync()
    {
        var loginTask = LogInAsyncCore();
        NotifyAuthenticationStateChanged(loginTask); 
        return loginTask; 
    }

    async Task<AuthenticationState> LogInAsyncCore()
    {
        var userId = await localStorage.GetValue("kt_profileid");
        if (string.IsNullOrEmpty(userId))
        {
            currentUser = new ClaimsPrincipal(new ClaimsIdentity());
        }
        else
        { 
            auth_token = await localStorage.GetValue("auth_token");
            var identity = new ClaimsIdentity("custom");
            identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, await localStorage.GetValue("kt_profileid") ?? ""));
            identity.AddClaim(new Claim(ClaimTypes.Name, await localStorage.GetValue(ClaimTypes.Name) ?? string.Empty));
            identity.AddClaim(new Claim("kt_profilename", await localStorage.GetValue("kt_profilename") ?? string.Empty));
            identity.AddClaim(new Claim("kt_profileemail", await localStorage.GetValue("kt_profileemail") ?? string.Empty));
            identity.AddClaim(new Claim("kt_profilephone", await localStorage.GetValue("kt_profilephone") ?? string.Empty));
            identity.AddClaim(new Claim("kt_profileimage", await localStorage.GetValue("kt_profileimage") ?? string.Empty));
            //identity.AddClaim(new Claim("kt_roleclaims", await localStorage.GetValue("kt_roleclaims") ?? string.Empty));
            identity.AddClaim(new Claim("CookieAccepted", await localStorage.GetValue("CookieAccepted") ?? string.Empty));
            var roles = await localStorage.GetValue(ClaimTypes.Role) ?? string.Empty;
            foreach (var role in roles.Split(','))
            {
                identity.AddClaim(new Claim(ClaimTypes.Role, role));
            }

            currentUser = new ClaimsPrincipal(identity);
        }
        return new AuthenticationState(currentUser);
    }

    public async Task Logout()
    {
        await localStorage.RemoveValue("kt_profileid");
        await localStorage.RemoveValue(ClaimTypes.Name);
        await localStorage.RemoveValue(ClaimTypes.Role);
        await localStorage.RemoveValue("kt_profilename");
        await localStorage.RemoveValue("kt_profileemail");
        await localStorage.RemoveValue("kt_profilephone");
        await localStorage.RemoveValue("CookieAccepted");
        currentUser = new ClaimsPrincipal(new ClaimsIdentity());
        NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(currentUser)));
    }

    public async Task<AuthenticationState> GetUpdatedClaimsFromServer(Dictionary<string, string> dictionary)
    {
        var userId = await localStorage.GetValue("kt_profileid");
        if (!string.IsNullOrEmpty(userId))
        {
            if (currentUser?.Identity is ClaimsIdentity identity)
            {
                foreach (var kvp in dictionary)
                {
                    var _removeclaim = identity.FindFirst(kvp.Key.ToString());
                    if (_removeclaim != null)
                    {
                        identity.RemoveClaim(_removeclaim);
                        identity.AddClaim(new Claim(kvp.Key.ToString(), kvp.Value.ToString()));
                        await localStorage.SetValue(kvp.Value.ToString(), kvp.Key.ToString());
                    }
                }

                currentUser = new ClaimsPrincipal(identity);
                NotifyAuthenticationStateChanged(
              Task.FromResult(new AuthenticationState(currentUser)));
            }
        }

        return new AuthenticationState(currentUser ?? new ClaimsPrincipal());
    }
}
