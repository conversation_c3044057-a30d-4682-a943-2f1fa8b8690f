﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Logging;
using PremierAutoTag.Server.Data.Entities;

namespace PremierAutoTag.Server.Data;

public class DataSeeding
{
    //Required because when class is made static an ILogger cannot be injected
    private DataSeeding() { }
    
    public static async Task Seed(DbContext context)
    {
        var userManager = context.GetService<UserManager<ApplicationUser>>();
        var roleManager = context.GetService<RoleManager<IdentityRole>>();
        var logger = context.GetService<ILogger<DataSeeding>>();
        
        logger.LogInformation("Seeding authentication data...");
        
        await CreateAppRoles(roleManager);
        await CreateInitialSuperAdmin(userManager);
    }

    private static async Task CreateInitialSuperAdmin(UserManager<ApplicationUser> userManager)
    {
        var adminUser = new ApplicationUser
        {
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsDeactivated = false,
            CreatedBy = "SYSTEM"
        };
        
        if (await userManager.FindByEmailAsync(adminUser.Email) == null)
        {
            await userManager.CreateAsync(adminUser, "+ChangeM@@2025+");
        }

        var admin = await userManager.FindByEmailAsync(adminUser.Email);
        
        await userManager.AddToRoleAsync(admin!, AppRoles.Admin);
    }

    private static async Task CreateAppRoles(RoleManager<IdentityRole> roleManager)
    {
        List<IdentityRole> roles =
        [
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = AppRoles.Admin,
                NormalizedName = "Admin"
            },

            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = AppRoles.User,
                NormalizedName = "User"
            },

            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = AppRoles.Dealer,
                NormalizedName = "Dealer"
            }
        ];

        foreach (var role in roles)
        {
            if (!await roleManager.RoleExistsAsync(role.Name!)) await roleManager.CreateAsync(role);
        }
    }
}