﻿using System.Text;
using System.Text.Json;
using System.Net.Http.Json;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Extensions;
using PremierAutoTag.ServiceContracts;
using PremierAutoTag.ServiceContracts.Features.Contact;
namespace PremierAutoTag.Razor.Features.Contact;
public class ContactClientSideFormDataService : IContactFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public ContactClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<long> SaveAsync(ContactFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<long>($"api/ContactsForm/Save", formBusinessObject);
	}
	public async Task<ContactFormBusinessObject?> GetItemByIdAsync(long id)
	{
		return await _httpClient.GetFromJsonAsync<ContactFormBusinessObject>($"api/ContactsForm/GetItemById?id=" + id);
	}
}
