﻿
using Azure;
using Azure.Communication.Email;
using Microsoft.Extensions.Options;
using PremierAutoTag.ServiceContracts.Models;
using PremierAutoTag.ServiceContracts.Services;
using PremierAutoTag.ServiceContracts.Settings;

namespace PremierAutoTag.Server.DataServices.Services
{
    public class AzureEmailService : IEmailService
    {
        private readonly EmailSettings _emailSetting;
        private readonly EmailClient _emailClient;
        public AzureEmailService(IOptions<EmailSettings> emailSetting)
        {
            _emailSetting = emailSetting.Value;
            _emailClient = new EmailClient(_emailSetting.ApiKey);
        }

        public async Task<bool> SendEmailAsync(EmailEvent request)
        {
            var emailContent = new EmailContent(request.Subject)
            {
                Html = $"<html><body>{request.Message}</body></html>"
            };

            var to = request.To.Select(x => new EmailAddress(x.Address)).ToList();
            var cc = request.To.Select(x => new EmailAddress(x.Address)).ToList();
            var bcc = request.To.Select(x => new EmailAddress(x.Address)).ToList();

            var emailRecipients = new EmailRecipients(to, cc, bcc);
            var emailMessage = new EmailMessage(_emailSetting.DefaultEmail, emailRecipients, emailContent);

            try
            {
                var emailSendOperation = await _emailClient.SendAsync(WaitUntil.Completed, emailMessage);
                Console.WriteLine($"Email Sent. Status = {emailSendOperation.Value.Status}");

                var operationId = emailSendOperation.Id;
                Console.WriteLine($"Email operation id = {operationId}");
                if (emailSendOperation.Value.Status == EmailSendStatus.Succeeded)
                {
                    return true;
                }

            }
            catch (RequestFailedException ex)
            {
                Console.WriteLine($"Email send operation failed with error code: {ex.ErrorCode}, message: {ex.Message}");
            }
            return false;
        }
    }
}