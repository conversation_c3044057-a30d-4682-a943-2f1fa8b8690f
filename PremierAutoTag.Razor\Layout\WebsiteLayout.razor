﻿@using PremierAutoTag.Framework.Core
@using Microsoft.AspNetCore.Components.Authorization
@inherits FrameworkLayoutBaseComponent


<header class="bg-white w-full fixed top-0 border-b border-b-gray-m-200 z-50">
    <div class="py-2 bg-primary hidden md:block">
        <span class="text-white text-center block text-base font-medium">
            Accepting TSA PreCheck Enrollment at our
            Norwalk
            Office
        </span>
    </div>
    <nav class="flex items-center justify-between md:px-12 lg:px-16  p-4 mx-auto ">

        <div class="flex ">
            <a class="-m-1.5 p-1.5" href="/html/premerauto/index.html">
                <span class="sr-only">Your Company</span>
                <img alt="" loading="lazy" decoding="async" class="h-9 lg:h-14" src="images/logo.png">
            </a>
        </div>

        <ul class="items-center  justify-center flex-grow hidden gap-2 xl:gap-6 lg:flex ">
            <li class="">
                <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                   href="">Home</a>
            </li>

            <li class="">
                <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                   href="aboutus.html">About us</a>
            </li>

            <li class="">
                <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                   href="locations.html">Locations/Hours</a>
            </li>

            <li class="">
                <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                   href="FAQ.html">FAQ</a>
            </li>

            <li class="">
                <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                   href="tips.html">DMV Tips</a>
            </li>
            <li class="">
                <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                   href="/contact">Contact</a>
            </li>
        </ul>
        <div class="flex items-center gap-3">
            <AuthorizeView>
                <Authorized Context="authContext">
                    <div class="flex items-center gap-3">
                        <span class="text-sm md:text-base text-gray-700">
                            Welcome, <span class="font-medium">@GetUserDisplayName()</span>
                        </span>
                        <a href="@GetDashboardUrl(authContext.User)"
                           class="bg-primary hover:bg-primaryDark text-white py-1.5 md:py-2.5 px-3 md:px-4 text-sm md:text-sm font-medium rounded-lg">
                            Go to Dashboard
                        </a>
                    </div>
                </Authorized>
                <NotAuthorized>
                    <div class="flex items-center gap-3">
                        <a href="/Account/Login"
                           class="bg-white py-1.5 md:py-2.5 px-3 md:px-4 hover:bg-gray-50 text-gray-900 text-sm md:text-sm font-medium hover:border hover:border-gray-300 border border-transparent rounded-lg">
                            Log In
                        </a>
                        <a href="/Account/Register"
                           class="rounded-lg text-white bg-primary hover:bg-primaryDark px-5 py-2.5">
                            Register
                        </a>
                    </div>
                </NotAuthorized>
            </AuthorizeView>
            <div class="flex lg:hidden">
                <button id="mobileMenuBtn" type="button"
                        class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700">
                    <span class="sr-only">Open main menu</span><svg class="w-6 h-6" fill="none" viewBox="0 0 24 24"
                                                                    stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round"
                              d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
                    </svg>
                </button>
            </div>
        </div>
    </nav>
    <div class="hidden lg:hidden" id="mobileMenu" role="dialog" aria-modal="true">
        <div class="fixed inset-0 z-10 hidden bg-white opacity-50"></div>
        <div class="fixed inset-y-0 right-0 z-20 w-full p-4 overflow-y-auto bg-white md:px-6 md:py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
            <div class="flex items-center justify-between pt-1">
                <a href="#" class="">
                    <span class="sr-only">
                        Your
                        Company
                    </span>
                    <img alt="" loading="lazy" width="194" height="50" decoding="async" class="w-auto h-9"
                         style="color:transparent" src="images/logo.png">
                </a>
                <button type="button" id="mobileMenuBtn2" class=" -m-2.5 rounded-md p-2.5 text-gray-700">
                    <span class="sr-only">
                        Close
                        menu
                    </span><svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="flow-root mt-6">
                <div class="-my-6 divide-y divide-gray-500/10">
                    <div class="py-6 space-y-1">
                        <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                           href="/">Home</a>
                        <a href="aboutus.html"
                           class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100">
                            About
                            Us
                        </a>
                        <a href="locations.html"
                           class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100">Locations/Hours</a>
                        <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                           href="FAQ.html">FAQ</a>
                        <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                           href="tips.html">DMV Tips</a>
                        <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                           href="contactUs.html">Contact</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
<main>
    @Body
</main>
<footer class="pt-8 md:pt-16 bg-gray-50 mt-auto mb-0">
    <div class="container px-2 mx-auto lg:px-0">
        <div class="grid grid-cols-12 px-2 lg:px-0">
            <div class="flex flex-col items-center justify-center col-span-12 mb-6 md:mb-12 md:col-span-6 md:col-start-4">
                <div class=" mb-6">
                    <a href="" class="flex items-center">
                        <img src="images/footerLogo.svg" class="h-8 md:h-12" alt="Logo">
                    </a>
                </div>
                <ul class="flex flex-wrap justify-center mb-2 gap-4 md:gap-10 ">
                    <li class="md:mb-4">
                        <a href=""
                           class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">
                            About
                            us
                        </a>
                    </li>
                    <li class="md:mb-4">
                        <a href="aboutus.html"
                           class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">Locations</a>
                    </li>
                    <li class="md:mb-4">
                        <a href="times.html"
                           class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">
                            Walk-in
                            Hours
                        </a>
                    </li>
                    <li class="">
                        <a href="contact.htmt"
                           class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">
                            Contact us
                        </a>
                    </li>
                    <li class="">
                        <a href="tips.html"
                           class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">
                            DMV Tips
                        </a>
                    </li>
                    <li class="">
                        <a href="FAQ.html"
                           class="text-base font-normal text-gray-500 hover:underline underline-offset-4 smoothScroll">
                            FAQ
                        </a>
                    </li>
                </ul>
                <div>
                    <span class="text-gray-500 text-center block">
                        © 2024 Premier Auto Tag Services, Inc. All rights
                        reserved.
                    </span>
                </div>
            </div>
        </div>

    </div>
</footer>

@code {
    private string GetUserDisplayName()
    {
        if (AuthenticatedUser == null)
            return "User";

        // Try to get a proper display name from AuthenticatedUser
        if (!string.IsNullOrEmpty(AuthenticatedUser.ProfileName))
            return AuthenticatedUser.ProfileName;

        if (!string.IsNullOrEmpty(AuthenticatedUser.Username))
            return AuthenticatedUser.Username;

        if (!string.IsNullOrEmpty(AuthenticatedUser.Email))
            return AuthenticatedUser.Email;

        return "User";
    }

    private string GetDashboardUrl(System.Security.Claims.ClaimsPrincipal user)
    {
        // Check user roles from claims to determine appropriate dashboard
        if (user.IsInRole("Admin"))
        {
            return "/dashboard/admin";
        }
        else if (user.IsInRole("Dealer"))
        {
            return "/dashboard/dealer";
        }
        else if (user.IsInRole("User"))
        {
            return "/dashboard/user";
        }
        else
        {
            // Fallback to main dashboard route which will redirect based on role
            return "/dashboard";
        }
    }
}