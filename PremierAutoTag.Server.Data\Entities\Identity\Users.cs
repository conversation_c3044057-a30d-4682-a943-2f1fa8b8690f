﻿using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Server.Data.Entities
{
    public class ApplicationUser : IdentityUser
    {
        public int? MobileOTP { get; set; }
        public int? EmailOTP { get; set; }
        public bool IsDeactivated { get; set; }

        [MaxLength(36)]
        public string? CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [MaxLength(36)]
        public string? UpdatedBy { get; set; }
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
