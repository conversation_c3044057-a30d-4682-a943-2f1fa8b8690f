﻿using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Extensions;
using PremierAutoTag.ServiceContracts.Features.AdminDashboard;
using System.Net.Http.Json;
namespace PremierAutoTag.Razor.Features.AdminDashboard;
public class AdminDashboardClientSideListingDataService : IAdminDashboardListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public AdminDashboardClientSideListingDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	
	public async Task<PagedDataList<AdminDashboardListingBusinessObject>> GetPaginatedItems(AdminDashboardFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<AdminDashboardListingBusinessObject>>($"api/AdminDashboardsListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}
