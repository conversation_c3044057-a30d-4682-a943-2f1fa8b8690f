using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;

namespace PremierAutoTag.ServiceContracts.Services
{
    public interface IStripePaymentService
    {
        Task<string> CreateCheckoutSessionAsync(CreateCheckoutSessionRequest request);
        Task<bool> ProcessPaymentSuccessAsync(string sessionId);
        Task<bool> ProcessPaymentFailureAsync(string sessionId, string errorMessage);
        Task<bool> ProcessWebhookEventAsync(string eventJson, string signature);
    }

    public class CreateCheckoutSessionRequest
    {
        public long SubscriptionPlanId { get; set; }
        public string UserId { get; set; } = "";
        public string UserEmail { get; set; } = "";
        public string SuccessUrl { get; set; } = "";
        public string CancelUrl { get; set; } = "";
        public Dictionary<string, string>? Metadata { get; set; }
    }

    public class StripeCheckoutResult
    {
        public bool Success { get; set; }
        public string? CheckoutUrl { get; set; }
        public string? SessionId { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
