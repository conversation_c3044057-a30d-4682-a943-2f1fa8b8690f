﻿@page "/"
<Radzen.Blazor.RadzenNotification />
<EditForm Model="M" OnValidSubmit="GenerateDynamicCode">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="row">
        <div class="col-md-12">
            <div class="form-check form-check-inline">
                <input class="form-check-input" name="project" type="radio" checked @onclick="@(()=>M.Project = ProjectEnumType.Portal)" id="inlineCheckbox1" >
                <label class="form-check-label" for="inlineCheckbox1">Portal</label>
            </div>
           @*  <div class="form-check form-check-inline">
                <input class="form-check-input" name="project" type="radio" @onclick="@(()=>M.Project = ProjectEnumType.PublicPortal)" id="inlineCheckbox2">
                <label class="form-check-label" for="inlineCheckbox2">Public Website</label>
            </div> *@
        </div>
        <div class="col-md-12">
            <label>Base path (including src folder) </label>
            <InputText  type="text" class="form-control" @bind-Value=M.BasePath placeholder="e.g. C:\"></InputText> 
        </div>
          
        <div class="col-md-6 pt-4">
            <div class="form-group">
                <label>Directory Name</label>
                <InputText @bind-Value='M.DirectoryName' class="form-control" placeholder="Profile\Article" />
            </div>
        </div>

        <div class="col-md-6 pt-4">
            <div class="form-group">
                <label>Module Namespace</label>
                <InputText  @bind-Value='M.NameSpace' class="form-control" placeholder="Profile.Article" />
            </div>

        </div>
        <div class="col-md-6 pt-4">
            <div class="form-group">
                <label>ComponentPrefix</label>
                <InputText @bind-Value='M.ComponentPrefix' class="form-control" placeholder="ComponentPrefix e.g. Article" />
            </div>
        </div>
        <div class="col-md-6 pt-4">
            <div class="form-group">
                <label>Primary Key</label>
                <InputText @bind-Value='M.PkType' class="form-control" placeholder="Primary Key e.g. Guid, int" />
            </div>
        </div>

        <div class="col-12 pt-4"><hr /></div>
        <div class="col-md-3">
            <label>Generate Form </label>
            <InputCheckbox @bind-Value=M.GenerateForm></InputCheckbox>
        </div>
        <div class="col-md-3">
            <label>Generate Listing </label>
            <InputCheckbox @bind-Value=M.GenerateListing></InputCheckbox>
        </div> 

        <div class="col-md-12">
            <button type="submit" class="btn btn-primary m-2" >Generate Code</button> 
        </div>
    </div>



</EditForm>


