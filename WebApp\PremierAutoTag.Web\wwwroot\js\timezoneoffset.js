﻿let timezone = this.getCookie('timezonename');
let timezoneoffset = this.getCookie('timezoneoffset');

let currentTimeZoneName = getTimezoneName();
let currentTimeZoneOffset = getTimezoneOffset();

//if cookies not set, then get 'timezone' & 'timezoneoffset' from browser also save in cookies
if (timezone != currentTimeZoneName && timezoneoffset != currentTimeZoneOffset) {
    timezone = this.getTimezoneName();
    timezoneoffset = this.getTimezoneOffset().toString();

    this.setCookie('timezonename', escape(timezone), 365);
    this.setCookie('timezoneoffset', timezoneoffset, 365);
}

function getTimezoneName() {
    const today = new Date();
    const short = today.toLocaleDateString(undefined);
    const full = today.toLocaleDateString(undefined, { timeZoneName: 'long' });

    // Trying to remove date from the string in a locale-agnostic way
    const shortIndex = full.indexOf(short);

    if (shortIndex >= 0) {
        const trimmed = full.substring(0, shortIndex) + full.substring(shortIndex + short.length);

        // by this time `trimmed` should be the timezone's name with some punctuation -// trim it from both sides
        return trimmed.replace(/^[\s,.\-:;]+|[\s,.\-:;]+$/g, '');

    } else {
        // in some magic case when short representation of date is not present in the long one, just return the long one as a fallback, since it should contain the timezone's name
        return full;
    }
}

function getTimezoneOffset() {
    var n = (new Date).getTimezoneOffset()
        , t = Math.abs(n)
        , r = t / 60 | 0
        , u = t % 60 / 60
        , i = r + u;
    return n > 0 ? i * -1 : i
}


function setCookie(name, value, days) {
    var expires = "";
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + "=" + (value || "") + expires + "; path=/";
}

function getCookie(name) {
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

function eraseCookie(name) {
    document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

