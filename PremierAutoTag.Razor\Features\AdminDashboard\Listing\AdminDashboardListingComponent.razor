﻿@page "/dashboard/admin"
@layout MainLayout
@attribute [Authorize(Roles = "Admin")]


@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms

@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Razor.Features.AdminDashboard;
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.ServiceContracts.Features.AdminDashboard;
@inherits ListingBase<AdminDashboardListingViewModel,AdminDashboardListingBusinessObject,AdminDashboardFilterViewModel,AdminDashboardFilterBusinessObject, IAdminDashboardListingDataService>

@*<button  class="btn btn-primary" type="button" @onclick='()=> ShowCenterDialog<AdminDashboardFormComponent>(default(long), "Create New AdminDashboard")'>Add New</button>*@

@if(Items == null)
{}
else
{
if(Items.Count() == 0)
{}
else
{
@foreach(var item in Items)
{
	<!-- Add item layout here -->
}
<EditForm Model="PaginationStrip">
	 @*<PaginationStrip @bind-Value="PaginationStrip" TotalPages="TotalPages" TotalRows="TotalRows"></PaginationStrip> *@
</EditForm>
}
}

