﻿namespace PremierAutoTag.ServiceContracts.Models
{
    public class EmailEvent
    {
        public EmailEvent()
        {
            To = new();
            Cc = new();
            BCc = new();
            Subject = string.Empty;
            Message = string.Empty;
        }
        public List<EmailAddressModel> To { get; set; }
        public List<EmailAddressModel> Cc { get; set; }
        public List<EmailAddressModel> BCc { get; set; }
        public string Subject { get; set; }
        public string Message { get; set; }

    }
}
