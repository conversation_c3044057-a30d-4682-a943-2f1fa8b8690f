﻿using Microsoft.EntityFrameworkCore;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.Server.Data.Entities;
using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;

namespace PremierAutoTag.Server.DataServices.Features.SubscriptionPlan;
public class SubscriptionPlanServerSideFormDataService : ISubscriptionPlanFormDataService
{
	private readonly ApplicationDbContext _context;

	public SubscriptionPlanServerSideFormDataService(ApplicationDbContext context)
	{
		_context = context;
	}

	public async Task<long> SaveAsync(SubscriptionPlanFormBusinessObject formBusinessObject)
	{
		try
		{
			Server.Data.Entities.SubscriptionPlan entity;

			if (formBusinessObject.Id == 0)
			{
				// Create new subscription plan
				entity = new Server.Data.Entities.SubscriptionPlan
				{
					Name = formBusinessObject.Name,
					Description = formBusinessObject.Description,
					AnnualPrice = formBusinessObject.AnnualPrice,
					MaxVehicles = formBusinessObject.MaxVehicles,
					MaxUsers = formBusinessObject.MaxUsers,
					StandAloneQuotesDiscountPercent = formBusinessObject.StandAloneQuotesDiscountPercent,
					RegistrationTitleServicesDiscountPercent = formBusinessObject.RegistrationTitleServicesDiscountPercent,
					HasDirectSubmission = formBusinessObject.HasDirectSubmission,
					HasDedicatedClientManager = formBusinessObject.HasDedicatedClientManager,
					Features = formBusinessObject.Features,
					IsPopular = formBusinessObject.IsPopular,
					DisplayOrder = formBusinessObject.DisplayOrder,
					IsActive = formBusinessObject.IsActive,
					RequiresPayment = formBusinessObject.AnnualPrice > 0 // Automatically set based on price
				};

				_context.SubscriptionPlans.Add(entity);
			}
			else
			{
				// Update existing subscription plan
				entity = await _context.SubscriptionPlans
					.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id && !x.IsDelete)
					?? throw new InvalidOperationException($"Subscription plan with ID {formBusinessObject.Id} not found");

				entity.Name = formBusinessObject.Name;
				entity.Description = formBusinessObject.Description;
				entity.AnnualPrice = formBusinessObject.AnnualPrice;
				entity.MaxVehicles = formBusinessObject.MaxVehicles;
				entity.MaxUsers = formBusinessObject.MaxUsers;
				entity.StandAloneQuotesDiscountPercent = formBusinessObject.StandAloneQuotesDiscountPercent;
				entity.RegistrationTitleServicesDiscountPercent = formBusinessObject.RegistrationTitleServicesDiscountPercent;
				entity.HasDirectSubmission = formBusinessObject.HasDirectSubmission;
				entity.HasDedicatedClientManager = formBusinessObject.HasDedicatedClientManager;
				entity.Features = formBusinessObject.Features;
				entity.IsPopular = formBusinessObject.IsPopular;
				entity.DisplayOrder = formBusinessObject.DisplayOrder;
				entity.IsActive = formBusinessObject.IsActive;
				entity.RequiresPayment = formBusinessObject.AnnualPrice > 0; // Automatically set based on price

				_context.SubscriptionPlans.Update(entity);
			}

			await _context.SaveChangesAsync();
			return entity.Id;
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Error saving subscription plan: {ex.Message}", ex);
		}
	}

	public async Task<SubscriptionPlanFormBusinessObject?> GetItemByIdAsync(long id)
	{
		try
		{
			var entity = await _context.SubscriptionPlans
				.FirstOrDefaultAsync(x => x.Id == id && !x.IsDelete);

			if (entity == null)
				return null;

			return new SubscriptionPlanFormBusinessObject
			{
				Id = entity.Id,
				Name = entity.Name,
				Description = entity.Description,
				AnnualPrice = entity.AnnualPrice,
				MaxVehicles = entity.MaxVehicles,
				MaxUsers = entity.MaxUsers,
				StandAloneQuotesDiscountPercent = entity.StandAloneQuotesDiscountPercent,
				RegistrationTitleServicesDiscountPercent = entity.RegistrationTitleServicesDiscountPercent,
				HasDirectSubmission = entity.HasDirectSubmission,
				HasDedicatedClientManager = entity.HasDedicatedClientManager,
				Features = entity.Features,
				IsPopular = entity.IsPopular,
				DisplayOrder = entity.DisplayOrder,
				IsActive = entity.IsActive,
				RequiresPayment = entity.RequiresPayment
			};
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Error retrieving subscription plan: {ex.Message}", ex);
		}
	}
}
