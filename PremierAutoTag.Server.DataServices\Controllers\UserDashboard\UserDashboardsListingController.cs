﻿using Microsoft.AspNetCore.Mvc;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.UserDashboard;
namespace PremierAutoTag.Server.DataServices.Controller.UserDashboard;
[ApiController, Route("api/[controller]/[action]")]
public class UserDashboardsListingController : ControllerBase, IUserDashboardListingDataService
{

	private readonly IUserDashboardListingDataService dataService;

	public UserDashboardsListingController(IUserDashboardListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpGet]
	public async Task<PagedDataList<UserDashboardListingBusinessObject>> GetPaginatedItems([FromQuery] UserDashboardFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
