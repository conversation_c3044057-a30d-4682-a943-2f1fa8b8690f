﻿using PremierAutoTag.Framework.Core.Attributes;

namespace PremierAutoTag.Framework.Core
{
    public interface IFormDataService<TFormModel, TKey>
    {
        public Task<TFormModel?> GetItemByIdAsync(TKey id);

        /// <summary>
        /// Saves viewmodel data into database, please do not call context.SaveChanges, framwork does it automatically
        /// </summary>
        /// <param name="formViewModel"></param>
        /// <returns></returns>
        Task<TKey> SaveAsync(TFormModel formViewModel);

    }
}
