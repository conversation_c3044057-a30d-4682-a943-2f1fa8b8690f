namespace PremierAutoTag.ServiceContracts.Models
{
    public class DealerEmailVerificationResult
    {
        public bool IsValid { get; set; }
        public bool IsExpired { get; set; }
        public string? ErrorMessage { get; set; }
        public string? UserId { get; set; }
        public int RegistrationStep { get; set; }

        public static DealerEmailVerificationResult Success(string userId, int registrationStep)
        {
            return new DealerEmailVerificationResult
            {
                IsValid = true,
                IsExpired = false,
                UserId = userId,
                RegistrationStep = registrationStep
            };
        }

        public static DealerEmailVerificationResult Invalid(string errorMessage)
        {
            return new DealerEmailVerificationResult
            {
                IsValid = false,
                IsExpired = false,
                ErrorMessage = errorMessage
            };
        }

        public static DealerEmailVerificationResult Expired()
        {
            return new DealerEmailVerificationResult
            {
                IsValid = false,
                IsExpired = true,
                ErrorMessage = "Verification token has expired"
            };
        }
    }
}
