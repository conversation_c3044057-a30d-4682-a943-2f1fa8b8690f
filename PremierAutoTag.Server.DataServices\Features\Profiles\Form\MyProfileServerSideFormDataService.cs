﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.Server.Data.Entities;
using PremierAutoTag.ServiceContracts.Features.MyProfile;

namespace PremierAutoTag.Server.DataServices.Features.MyProfile;
public class MyProfileServerSideFormDataService : IMyProfileFormDataService
{
	private readonly ApplicationDbContext _context;
	private readonly UserManager<ApplicationUser> _userManager;

	public MyProfileServerSideFormDataService(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
	{
		_context = context;
		_userManager = userManager;
	}

	public async Task<string> SaveAsync(MyProfileFormBusinessObject formBusinessObject)
	{
		try
		{
			// Find the user profile
			var userProfile = await _context.Profiles
				.FirstOrDefaultAsync(p => p.Id == formBusinessObject.Id);

			if (userProfile == null)
			{
				// Create new profile if it doesn't exist
				userProfile = new UserProfile
				{
					Id = formBusinessObject.Id,
					FirstName = formBusinessObject.FirstName.Trim(),
					LastName = formBusinessObject.LastName.Trim(),
					Phone = formBusinessObject.Phone?.Trim() ?? "",
					DealerName = "",
					DealerId = "",
					TaxId = "",
					Address = "",
					AuthorizedCompany = "",
					AdministrativeEmail = formBusinessObject.Email,
					AdministrativePhone = formBusinessObject.Phone?.Trim() ?? "",
					StateId = 0,
					Subsidary = false,
					DEALERSHIP_IOT_TYPES = 0,
					VEHICAL_ON_IOT_TYPES = 0,
					VEHICAL_SOLD_IOT = 0,
					SubscriptionPlanId = null,
					CreatedBy = formBusinessObject.Id,
					CreatedAt = DateTime.UtcNow,
					ModifiedBy = formBusinessObject.Id,
					ModifiedAt = DateTime.UtcNow
				};

				_context.Profiles.Add(userProfile);
			}
			else
			{
				// Update existing profile
				userProfile.FirstName = formBusinessObject.FirstName.Trim();
				userProfile.LastName = formBusinessObject.LastName.Trim();
				userProfile.Phone = formBusinessObject.Phone?.Trim() ?? "";
				userProfile.ModifiedBy = formBusinessObject.Id;
				userProfile.ModifiedAt = DateTime.UtcNow;
			}

			await _context.SaveChangesAsync();
			return userProfile.Id;
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Error saving profile: {ex.Message}", ex);
		}
	}

	public async Task<MyProfileFormBusinessObject?> GetItemByIdAsync(string id)
	{
		try
		{
			// Get user and profile data
			var user = await _userManager.FindByIdAsync(id);
			if (user == null)
				return null;

			var userProfile = await _context.Profiles
				.FirstOrDefaultAsync(p => p.Id == id);

			// Return business object with data from both user and profile
			return new MyProfileFormBusinessObject
			{
				Id = id,
				FirstName = userProfile?.FirstName ?? "",
				LastName = userProfile?.LastName ?? "",
				Phone = userProfile?.Phone ?? user.PhoneNumber ?? "",
				Email = user.Email ?? ""
			};
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Error retrieving profile: {ex.Message}", ex);
		}
	}
}
