﻿using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.ServiceContracts.Features.DealershipProfile;
namespace PremierAutoTag.Server.DataServices.Features.DealershipProfile;
public class DealershipProfileServerSideFormDataService : IDealershipProfileFormDataService
{

	private readonly ApplicationDbContext _context;

	public DealershipProfileServerSideFormDataService (ApplicationDbContext context)
	{
		_context = context;
	}
	public async Task<string> SaveAsync(DealershipProfileFormBusinessObject formBusinessObject)
	{
		throw new NotImplementedException();
	}
	public async Task<DealershipProfileFormBusinessObject?> GetItemByIdAsync(string id)
	{
		throw new NotImplementedException();
	}
}
