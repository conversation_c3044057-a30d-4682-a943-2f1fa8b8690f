﻿using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.DB;
using PremierAutoTag.Server.Data.Entities;
using PremierAutoTag.ServiceContracts.Features.DealershipProfile;
using Microsoft.EntityFrameworkCore;

namespace PremierAutoTag.Server.DataServices.Features.DealershipProfile;
public class DealershipProfileServerSideFormDataService : IDealershipProfileFormDataService
{
	private readonly ApplicationDbContext _context;

	public DealershipProfileServerSideFormDataService(ApplicationDbContext context)
	{
		_context = context;
	}

	public async Task<string> SaveAsync(DealershipProfileFormBusinessObject formBusinessObject)
	{
		try
		{
			var userProfile = await _context.Profiles.FirstOrDefaultAsync(p => p.Id == formBusinessObject.Id);

			if (userProfile == null)
			{
				// Create new profile if it doesn't exist
				userProfile = new UserProfile
				{
					Id = formBusinessObject.Id,
					DealerName = formBusinessObject.DealerName.Trim(),
					DealerId = formBusinessObject.DealerId.Trim(),
					TaxId = formBusinessObject.TaxId.Trim(),
					StateId = formBusinessObject.StateId,
					Subsidary = formBusinessObject.Subsidary,
					Address = formBusinessObject.Address.Trim(),
					Phone = formBusinessObject.Phone?.Trim() ?? "",
					AuthorizedCompany = formBusinessObject.AuthorizedCompany.Trim(),
					AdministrativeEmail = formBusinessObject.AdministrativeEmail.Trim(),
					AdministrativePhone = formBusinessObject.AdministrativePhone?.Trim() ?? "",
					DEALERSHIP_IOT_TYPES = formBusinessObject.DealershipIOTTypes,
					VEHICAL_ON_IOT_TYPES = formBusinessObject.VehicalOnIOTTypes,
					VEHICAL_SOLD_IOT = formBusinessObject.VehicalSoldIOT,
					CreatedBy = formBusinessObject.Id,
					CreatedAt = DateTime.UtcNow,
					ModifiedBy = formBusinessObject.Id,
					ModifiedAt = DateTime.UtcNow
				};

				_context.Profiles.Add(userProfile);
			}
			else
			{
				// Update existing profile
				userProfile.DealerName = formBusinessObject.DealerName.Trim();
				userProfile.DealerId = formBusinessObject.DealerId.Trim();
				userProfile.TaxId = formBusinessObject.TaxId.Trim();
				userProfile.StateId = formBusinessObject.StateId;
				userProfile.Subsidary = formBusinessObject.Subsidary;
				userProfile.Address = formBusinessObject.Address.Trim();
				userProfile.Phone = formBusinessObject.Phone?.Trim() ?? "";
				userProfile.AuthorizedCompany = formBusinessObject.AuthorizedCompany.Trim();
				userProfile.AdministrativeEmail = formBusinessObject.AdministrativeEmail.Trim();
				userProfile.AdministrativePhone = formBusinessObject.AdministrativePhone?.Trim() ?? "";
				userProfile.DEALERSHIP_IOT_TYPES = formBusinessObject.DealershipIOTTypes;
				userProfile.VEHICAL_ON_IOT_TYPES = formBusinessObject.VehicalOnIOTTypes;
				userProfile.VEHICAL_SOLD_IOT = formBusinessObject.VehicalSoldIOT;
				userProfile.ModifiedBy = formBusinessObject.Id;
				userProfile.ModifiedAt = DateTime.UtcNow;
			}

			await _context.SaveChangesAsync();
			return formBusinessObject.Id;
		}
		catch (Exception ex)
		{
			throw new Exception($"Error saving dealership profile: {ex.Message}", ex);
		}
	}

	public async Task<DealershipProfileFormBusinessObject?> GetItemByIdAsync(string id)
	{
		try
		{
			var userProfile = await _context.Profiles.FirstOrDefaultAsync(p => p.Id == id);

			if (userProfile == null)
			{
				return null;
			}

			return new DealershipProfileFormBusinessObject
			{
				Id = userProfile.Id,
				DealerName = userProfile.DealerName ?? "",
				DealerId = userProfile.DealerId ?? "",
				TaxId = userProfile.TaxId ?? "",
				StateId = userProfile.StateId,
				Subsidary = userProfile.Subsidary,
				Address = userProfile.Address ?? "",
				Phone = userProfile.Phone ?? "",
				AuthorizedCompany = userProfile.AuthorizedCompany ?? "",
				AdministrativeEmail = userProfile.AdministrativeEmail ?? "",
				AdministrativePhone = userProfile.AdministrativePhone ?? "",
				DealershipIOTTypes = userProfile.DEALERSHIP_IOT_TYPES,
				VehicalOnIOTTypes = userProfile.VEHICAL_ON_IOT_TYPES,
				VehicalSoldIOT = userProfile.VEHICAL_SOLD_IOT
			};
		}
		catch (Exception ex)
		{
			throw new Exception($"Error retrieving dealership profile: {ex.Message}", ex);
		}
	}
}
