﻿@page "/Account/ResendEmailConfirmation"
@layout WebsiteLayout

@using System.ComponentModel.DataAnnotations
@using System.Text
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using PremierAutoTag.Server.Data.Entities
@using PremierAutoTag.ServiceContracts.Services
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.Framework.Core.DataProtections

@inject UserManager<ApplicationUser> UserManager
@inject IEmailVerificationService EmailVerificationService
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject IDataProtection DataProtection

<PageTitle>Resend Email Confirmation</PageTitle>

<section class="">
    <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <div class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
                    <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>

                <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Resend Email Verification</h1>
                <h2 class="text-gray-600 text-sm md:text-base text-center">
                    Enter your email address to receive a new verification link
                </h2>
            </div>

            @if (!string.IsNullOrEmpty(message))
            {
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <p class="text-green-800 text-sm md:text-base">@message</p>
                </div>
            }

            <EditForm Model="Input" FormName="resend-email-confirmation" OnValidSubmit="OnValidSubmitAsync" method="post" class="flex flex-col gap-3 md:gap-4">
                <DataAnnotationsValidator />

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    <InputText @bind-Value="Input.Email"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               autocomplete="email"
                               aria-required="true"
                               placeholder="Enter your email address" />
                    <ValidationMessage For="() => Input.Email" class="text-danger text-sm mt-1" />
                </div>

                <button type="submit"
                        class="w-full py-2 md:py-2.5 px-4 bg-primary hover:bg-primaryDark text-white font-medium text-sm md:text-base rounded-lg focus:ring-1 focus:ring-primary outline-none transition-colors">
                    Resend Verification Email
                </button>
            </EditForm>

            <div class="flex justify-center gap-1">
                <span class="text-sm text-gray-600">Remember your password?</span>
                <a href="/Account/Login"
                   class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4">
                    Back to Login
                </a>
            </div>
        </div>
    </div>
</section>

@code {
    private string? message;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? Data { get; set; }

    protected override void OnInitialized()
    {
        try
        {
            // Pre-populate email if provided in encoded query string
            if (!string.IsNullOrEmpty(Data))
            {
                var decodedEmail = DataProtection.Decode(Data);
                Input.Email = decodedEmail;
            }
        }
        catch (Exception)
        {
            // If decoding fails, leave email empty for user to enter manually
            Input.Email = "";
        }
    }

    private async Task OnValidSubmitAsync()
    {
        try
        {
            var user = await UserManager.FindByEmailAsync(Input.Email!);
            if (user is null)
            {
                // Don't reveal that the user does not exist
                message = "If an account with that email exists, we've sent a verification email.";
                return;
            }

            if (user.EmailConfirmed)
            {
                message = "This email address is already verified.";
                return;
            }

            // Generate new verification token and send email
            var userId = await UserManager.GetUserIdAsync(user);
            var code = await EmailVerificationService.GenerateEmailVerificationTokenAsync(userId);
            var callbackUrl = NavigationManager.GetUriWithQueryParameters(
                NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
                new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code });

            // Check if user is a dealer
            var roles = await UserManager.GetRolesAsync(user);
            var isDealerUser = roles.Contains("Dealer");

            bool emailSent;
            if (isDealerUser)
            {
                // Get dealer name from user profile or use email
                var dealerName = !string.IsNullOrEmpty(user.UserName) ? user.UserName : user.Email ?? "Dealer";
                emailSent = await EmailVerificationService.SendDealerVerificationEmailAsync(
                    userId, Input.Email, callbackUrl, dealerName);
            }
            else
            {
                emailSent = await EmailVerificationService.SendIndividualVerificationEmailAsync(
                    userId, Input.Email, callbackUrl);
            }

            if (emailSent)
            {
                message = "Verification email sent! Please check your inbox and click the verification link.";
            }
            else
            {
                message = "Failed to send verification email. Please try again later.";
            }
        }
        catch (Exception)
        {
            message = "An error occurred while sending the verification email. Please try again later.";
        }
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";
    }
}
