﻿using System.Text;
using System.Text.Json;
using System.Net.Http.Json;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Extensions;
using PremierAutoTag.ServiceContracts;
using PremierAutoTag.ServiceContracts.Features.DealershipProfile;
namespace PremierAutoTag.Razor.Features.DealershipProfile;
public class DealershipProfileClientSideFormDataService : IDealershipProfileFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public DealershipProfileClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<string> SaveAsync(DealershipProfileFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/DealershipProfilesForm/Save", formBusinessObject);
	}
	public async Task<DealershipProfileFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<DealershipProfileFormBusinessObject>($"api/DealershipProfilesForm/GetItemById?id=" + id);
	}
}
