﻿namespace PremierAutoTag.Framework.Core.Utils
{
    public static class FilePath
    {
        public static string GetPath(string userId, string resourceId)
        {
            return $"{userId}/{resourceId}.webp";
        }
        public static string GetPath(string containerName, string userId, string resourceId)
        {
            return $"{containerName}/{userId}/{resourceId}.webp";
        }
        public static string GetPath(string containerName, string userId, string? resourceId, string fileSize)
        {

            return $"{containerName}/{userId}/{fileSize}/{resourceId}.webp";
        }
    }
}
