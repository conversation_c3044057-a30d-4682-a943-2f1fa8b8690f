﻿@page "/Account/ForgotPassword"
@layout WebsiteLayout
@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.Server.Data.Entities
@using PremierAutoTag.ServiceContracts.Services
@using PremierAutoTag.ServiceContracts.Models
@using PremierAutoTag.Server.DataServices.Services

@inject UserManager<ApplicationUser> UserManager
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject IEmailVerificationService EmailVerificationService
@inject IEmailService EmailService
@inject ILogger<ForgotPassword> Logger

<PageTitle>Forgot your password?</PageTitle>

<section class="">
    <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <EditForm Enhance Model="Input" FormName="forgot-password" OnValidSubmit="OnValidSubmitAsync" method="post" class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
            <div class="flex items-center justify-center">
                <image class="md:w-14 w-10 h-10 md:h-14" src="images/forgotkey.svg"></image>
            </div>
            <div>
                <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Forgot password?</h1>
                <h2 class="text-gray-600 text-sm md:text-base text-center">
                    No worries, we’ll send you reset
                    instructions.
                </h2>
            </div>
            <div>
                <label class="text-brightGray font-medium text-sm mb-1 block">Email</label>
                <InputText @bind-Value="Input.Email"
                           class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                           placeholder="Enter your email" />
                <ValidationMessage For="() => Input.Email" class="text-red-500 text-sm mt-1" />
            </div>

            <button class="w-full py-2 md:py-2.5 text-center rounded-lg bg-primary hover:bg-primaryDark text-white text-sm md:text-base font-semibold"
                    type="submit">
                Verify Email
            </button>

            <a href="/Account/Login" class="flex justify-center items-center gap-1 group">
                <span class="group-hover:transition duration-300 group-hover:-translate-x-1">
                    <svg width="21"
                         height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.3334 10H4.66669M4.66669 10L10.5 15.8334M4.66669 10L10.5 4.16669"
                              stroke="#A4A7AE" stroke-width="1.66667" stroke-linecap="round"
                              stroke-linejoin="round" />
                    </svg>
                </span>
                <span class="text-vampireGray hover:text-gray-700 text-sm font-semibold">
                    Back
                    to log in
                </span>
            </a>

        </EditForm>
    </div>
</section>


@* <h1>Forgot your password?</h1>
<h2>Enter your email.</h2>
<hr />
<div class="row">
    <div class="col-md-4">
        <EditForm Model="Input" FormName="forgot-password" OnValidSubmit="OnValidSubmitAsync" method="post">
            <DataAnnotationsValidator />
            <ValidationSummary class="text-danger" role="alert" />

            <div class="form-floating mb-3">
                <InputText @bind-Value="Input.Email" id="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
                <label for="Input.Email" class="form-label">Email</label>
                <ValidationMessage For="() => Input.Email" class="text-danger" />
            </div>
            <button type="submit" class="w-100 btn btn-lg btn-primary">Reset password</button>
        </EditForm>
     </div>
</div> *@

@code {
    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    private async Task OnValidSubmitAsync()
    {

        var user = await UserManager.FindByEmailAsync(Input.Email);
        if (user is null || !(await UserManager.IsEmailConfirmedAsync(user)))
        {
            // Don't reveal that the user does not exist or is not confirmed
            // But still redirect to confirmation page for security
            RedirectManager.RedirectTo("Account/ForgotPasswordConfirmation");
            return;
        }

        // Generate password reset token
        var code = await UserManager.GeneratePasswordResetTokenAsync(user);
        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri("Account/ResetPassword").AbsoluteUri,
            new Dictionary<string, object?> { ["code"] = code, ["email"] = Input.Email });

        // Send password reset email using the same email service as registration
        var emailSent = await SendPasswordResetEmailAsync(user, Input.Email, callbackUrl);

        if (emailSent)
        {
            Logger.LogInformation("Password reset email sent to {Email} for user {UserId}", Input.Email, user.Id);
        }
        else
        {
            Logger.LogWarning("Failed to send password reset email to {Email} for user {UserId}", Input.Email, user.Id);
        }

        // Always redirect to confirmation page regardless of email success for security
        RedirectManager.RedirectTo("Account/ForgotPasswordConfirmation");

    }

    private async Task<bool> SendPasswordResetEmailAsync(ApplicationUser user, string email, string resetUrl)
    {
        try
        {
            var emailContent = GeneratePasswordResetEmailContent(resetUrl, user);

            var emailEvent = new EmailEvent
                {
                    To = new List<EmailAddressModel> { new EmailAddressModel(email) },
                    Subject = "Reset Your AutoTag Password",
                    Message = emailContent
                };

            // Use the injected email service directly
            return await EmailService.SendEmailAsync(emailEvent);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error sending password reset email to {Email}", email);
            return false;
        }
    }

    private string GeneratePasswordResetEmailContent(string resetUrl, ApplicationUser user)
    {
        return $@"
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
            <div style='text-align: center; margin-bottom: 30px;'>
                <h1 style='color: #1f2937; margin-bottom: 10px;'>Reset Your Password</h1>
                <p style='color: #6b7280; font-size: 16px;'>We received a request to reset your AutoTag account password.</p>
            </div>

            <div style='background-color: #f9fafb; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <p style='color: #374151; margin-bottom: 20px;'>Hello,</p>
                <p style='color: #374151; margin-bottom: 20px;'>
                    You requested to reset your password for your AutoTag account. Click the button below to reset your password:
                </p>

                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{resetUrl}'
                       style='background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;'>
                        Reset Password
                    </a>
                </div>

                <p style='color: #6b7280; font-size: 14px; margin-top: 20px;'>
                    If you didn't request this password reset, please ignore this email. Your password will remain unchanged.
                </p>

                <p style='color: #6b7280; font-size: 14px; margin-top: 10px;'>
                    This link will expire in 24 hours for security reasons.
                </p>
            </div>

            <div style='text-align: center; color: #9ca3af; font-size: 12px;'>
                <p>© 2024 AutoTag. All rights reserved.</p>
            </div>
        </div>";
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";
    }
}
