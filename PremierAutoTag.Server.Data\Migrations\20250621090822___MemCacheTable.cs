﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PremierAutoTag.Server.Data.Migrations
{
    /// <inheritdoc />
    public partial class __MemCacheTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("CREATE TABLE [dbo].[__MemCache](\r\n\t[Id] [nvarchar](449) NOT NULL,\r\n\t[Value] [varbinary](max) NOT NULL,\r\n\t[ExpiresAtTime] [datetimeoffset](7) NOT NULL,\r\n\t[SlidingExpirationInSeconds] [bigint] NULL,\r\n\t[AbsoluteExpiration] [datetimeoffset](7) NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[Id] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]");

        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
