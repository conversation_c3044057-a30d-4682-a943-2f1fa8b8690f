using Microsoft.AspNetCore.Identity;
using PremierAutoTag.Server.Data;
using PremierAutoTag.Server.Data.Entities;
using System.Security.Claims;

namespace PremierAutoTag.Web.Services
{
    public interface IRoleBasedRedirectService
    {
        Task<string> GetDashboardUrlForUserAsync(ClaimsPrincipal user);
        Task<string> GetDashboardUrlForUserAsync(string userId);
        string GetDefaultDashboardUrl();
    }

    public class RoleBasedRedirectService : IRoleBasedRedirectService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<RoleBasedRedirectService> _logger;

        public RoleBasedRedirectService(
            UserManager<ApplicationUser> userManager,
            ILogger<RoleBasedRedirectService> logger)
        {
            _userManager = userManager;
            _logger = logger;
        }

        public async Task<string> GetDashboardUrlForUserAsync(ClaimsPrincipal user)
        {
            try
            {
                if (user?.Identity?.IsAuthenticated != true)
                {
                    return "/Account/Login";
                }

                var applicationUser = await _userManager.GetUserAsync(user);
                if (applicationUser == null)
                {
                    _logger.LogWarning("Could not find user for authenticated principal");
                    return "/Account/Login";
                }

                return await GetDashboardUrlForUserAsync(applicationUser.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error determining dashboard URL for user");
                return GetDefaultDashboardUrl();
            }
        }

        public async Task<string> GetDashboardUrlForUserAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("Could not find user with ID: {UserId}", userId);
                    return "/Account/Login";
                }

                var roles = await _userManager.GetRolesAsync(user);
                
                // Priority order: Admin > Dealer > User
                if (roles.Contains(AppRoles.Admin))
                {
                    _logger.LogInformation("Redirecting admin user {UserId} to admin dashboard", userId);
                    return "/dashboard/admin";
                }
                else if (roles.Contains(AppRoles.Dealer))
                {
                    _logger.LogInformation("Redirecting dealer user {UserId} to dealer dashboard", userId);
                    return "/dashboard/dealer";
                }
                else if (roles.Contains(AppRoles.User))
                {
                    _logger.LogInformation("Redirecting user {UserId} to user dashboard", userId);
                    return "/dashboard/user";
                }
                else
                {
                    _logger.LogWarning("User {UserId} has no recognized roles, redirecting to default dashboard", userId);
                    return GetDefaultDashboardUrl();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error determining dashboard URL for user ID: {UserId}", userId);
                return GetDefaultDashboardUrl();
            }
        }

        public string GetDefaultDashboardUrl()
        {
            // Default to user dashboard if role cannot be determined
            return "/dashboard/user";
        }
    }
}
