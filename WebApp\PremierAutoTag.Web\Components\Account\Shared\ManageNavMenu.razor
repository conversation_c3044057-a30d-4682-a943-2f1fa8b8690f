@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Routing

<div class="nav flex-column">
    <div class="nav-item">
        <NavLink class="nav-link" href="Account/Manage" Match="NavLinkMatch.All">
            Profile
        </NavLink>
    </div>
    <div class="nav-item">
        <NavLink class="nav-link" href="Account/Manage/Email">
            Email
        </NavLink>
    </div>
    <div class="nav-item">
        <NavLink class="nav-link" href="Account/Manage/ChangePassword">
            Password
        </NavLink>
    </div>
    <div class="nav-item">
        <NavLink class="nav-link" href="Account/Manage/TwoFactorAuthentication">
            Two-factor authentication
        </NavLink>
    </div>
    <div class="nav-item">
        <NavLink class="nav-link" href="Account/Manage/PersonalData">
            Personal data
        </NavLink>
    </div>
</div>
