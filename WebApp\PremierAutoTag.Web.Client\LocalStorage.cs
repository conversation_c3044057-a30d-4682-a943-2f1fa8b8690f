﻿using Microsoft.AspNetCore.Components.WebAssembly.Http;
using Microsoft.JSInterop;
using PremierAutoTag.Framework.Core;
using System.Runtime.CompilerServices;

namespace PremierAutoTag.Web.Client
{
    public class LocalStorageService : ILocalStorageService
    {
        private readonly IJSRuntime _jsRuntime;
        public LocalStorageService(IJSRuntime jSRuntime)
        {
            _jsRuntime = jSRuntime;
        }

        public async Task<string?> GetValue([CallerMemberName] string memberName = "")
        {
            return await _jsRuntime.InvokeAsync<string>("localStorage.getItem", memberName);
        }

        public async Task RemoveValue([CallerMemberName] string memberName = "")
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", memberName);
        }

        public async Task SetValue(string? value, [CallerMemberName] string memberName = "")
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", memberName, value);
        }
    }

    public class CookieHandler : DelegatingHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request, CancellationToken cancellationToken)
        {
            request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);
            return base.SendAsync(request, cancellationToken);
        }
    }
}
