using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using System.Text;
using PremierAutoTag.Server.Data.Entities;
using PremierAutoTag.ServiceContracts.Services;
using PremierAutoTag.ServiceContracts.Models;

namespace PremierAutoTag.Server.DataServices.Services
{
    public class EmailVerificationService : IEmailVerificationService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IEmailService _emailService;

        public EmailVerificationService(UserManager<ApplicationUser> userManager, IEmailService emailService)
        {
            _userManager = userManager;
            _emailService = emailService;
        }

        public async Task<bool> SendIndividualVerificationEmailAsync(string userId, string email, string verificationUrl)
        {
            var emailContent = GenerateIndividualVerificationEmailContent(verificationUrl);
            
            var emailEvent = new EmailEvent
            {
                To = new List<EmailAddressModel> { new EmailAddressModel(email) },
                Subject = "Verify Your AutoTag Account",
                Message = emailContent
            };

            return await _emailService.SendEmailAsync(emailEvent);
        }

        public async Task<bool> SendDealerVerificationEmailAsync(string userId, string email, string verificationUrl, string dealerName)
        {
            var emailContent = GenerateDealerVerificationEmailContent(verificationUrl, dealerName);
            
            var emailEvent = new EmailEvent
            {
                To = new List<EmailAddressModel> { new EmailAddressModel(email) },
                Subject = "Verify Your AutoTag Dealer Account",
                Message = emailContent
            };

            return await _emailService.SendEmailAsync(emailEvent);
        }

        public async Task<string> GenerateEmailVerificationTokenAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                throw new ArgumentException("User not found", nameof(userId));

            var token = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            return WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(token));
        }

        public async Task<EmailVerificationResult> ValidateEmailVerificationTokenAsync(string userId, string token)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                    return EmailVerificationResult.Invalid("User not found");

                if (user.EmailConfirmed)
                    return EmailVerificationResult.Invalid("Email is already verified");

                var decodedToken = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(token));
                var result = await _userManager.ConfirmEmailAsync(user, decodedToken);

                if (result.Succeeded)
                    return EmailVerificationResult.Success(userId);

                return EmailVerificationResult.Invalid("Invalid verification token");
            }
            catch (Exception ex)
            {
                return EmailVerificationResult.Invalid($"Verification failed: {ex.Message}");
            }
        }

        public async Task<bool> MarkEmailAsVerifiedAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return false;

            user.EmailConfirmed = true;
            var result = await _userManager.UpdateAsync(user);
            return result.Succeeded;
        }

        public async Task<bool> IsEmailVerifiedAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            return user?.EmailConfirmed ?? false;
        }

        private string GenerateIndividualVerificationEmailContent(string verificationUrl)
        {
            return $@"
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <div style='text-align: center; margin-bottom: 30px;'>
                        <h1 style='color: #2563eb; margin-bottom: 10px;'>Welcome to AutoTag!</h1>
                        <p style='color: #6b7280; font-size: 16px;'>Please verify your email address to complete your registration</p>
                    </div>
                    
                    <div style='background-color: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 30px;'>
                        <h2 style='color: #374151; margin-bottom: 15px;'>Verify Your Email Address</h2>
                        <p style='color: #6b7280; margin-bottom: 25px; line-height: 1.6;'>
                            Thank you for registering with AutoTag! To complete your account setup and start using our services, 
                            please click the button below to verify your email address.
                        </p>
                        
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{verificationUrl}' 
                               style='background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; 
                                      border-radius: 6px; font-weight: 600; display: inline-block;'>
                                Verify Email Address
                            </a>
                        </div>
                        
                        <p style='color: #9ca3af; font-size: 14px; margin-top: 20px;'>
                            If the button doesn't work, copy and paste this link into your browser:<br>
                            <a href='{verificationUrl}' style='color: #2563eb; word-break: break-all;'>{verificationUrl}</a>
                        </p>
                    </div>
                    
                    <div style='text-align: center; color: #9ca3af; font-size: 12px;'>
                        <p>This verification link will expire in 24 hours for security reasons.</p>
                        <p>If you didn't create an account with AutoTag, please ignore this email.</p>
                    </div>
                </div>";
        }

        private string GenerateDealerVerificationEmailContent(string verificationUrl, string dealerName)
        {
            return $@"
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <div style='text-align: center; margin-bottom: 30px;'>
                        <h1 style='color: #2563eb; margin-bottom: 10px;'>Welcome to AutoTag!</h1>
                        <p style='color: #6b7280; font-size: 16px;'>Dealer Account Verification Required</p>
                    </div>
                    
                    <div style='background-color: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 30px;'>
                        <h2 style='color: #374151; margin-bottom: 15px;'>Verify Your Dealer Account</h2>
                        <p style='color: #6b7280; margin-bottom: 15px; line-height: 1.6;'>
                            Hello <strong>{dealerName}</strong>,
                        </p>
                        <p style='color: #6b7280; margin-bottom: 25px; line-height: 1.6;'>
                            Thank you for registering your dealership with AutoTag! To continue with your registration process 
                            and access subscription plans, please verify your email address by clicking the button below.
                        </p>
                        
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{verificationUrl}' 
                               style='background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; 
                                      border-radius: 6px; font-weight: 600; display: inline-block;'>
                                Verify Email & Continue Registration
                            </a>
                        </div>
                        
                        <div style='background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;'>
                            <p style='color: #92400e; margin: 0; font-size: 14px;'>
                                <strong>Important:</strong> You must verify your email before proceeding to Step 2 of the registration process.
                            </p>
                        </div>
                        
                        <p style='color: #9ca3af; font-size: 14px; margin-top: 20px;'>
                            If the button doesn't work, copy and paste this link into your browser:<br>
                            <a href='{verificationUrl}' style='color: #2563eb; word-break: break-all;'>{verificationUrl}</a>
                        </p>
                    </div>
                    
                    <div style='text-align: center; color: #9ca3af; font-size: 12px;'>
                        <p>This verification link will expire in 24 hours for security reasons.</p>
                        <p>If you didn't create a dealer account with AutoTag, please ignore this email.</p>
                    </div>
                </div>";
        }
    }
}
