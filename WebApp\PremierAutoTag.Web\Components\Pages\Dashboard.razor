@page "/dashboard"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using PremierAutoTag.Web.Services

@attribute [Authorize]
@inject IRoleBasedRedirectService RoleBasedRedirectService
@inject NavigationManager NavigationManager
@inject ILogger<Dashboard> Logger

<PageTitle>Dashboard</PageTitle>

@code {
    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationStateTask { get; set; }

    [SupplyParameterFromQuery]
    public string? Payment { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            if (AuthenticationStateTask == null)
            {
                NavigationManager.NavigateTo("/Account/Login", forceLoad: true);
                return;
            }

            var authState = await AuthenticationStateTask;
            if (!authState.User.Identity?.IsAuthenticated == true)
            {
                NavigationManager.NavigateTo("/Account/Login", forceLoad: true);
                return;
            }

            // Get the appropriate dashboard URL based on user role
            var dashboardUrl = await RoleBasedRedirectService.GetDashboardUrlForUserAsync(authState.User);
            
            // Preserve payment status if present
            if (!string.IsNullOrEmpty(Payment))
            {
                dashboardUrl += $"?payment={Payment}";
            }

            // Redirect to the role-specific dashboard
            NavigationManager.NavigateTo(dashboardUrl, forceLoad: true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error redirecting to role-based dashboard");
            NavigationManager.NavigateTo("/Account/Login", forceLoad: true);
        }
    }
}
