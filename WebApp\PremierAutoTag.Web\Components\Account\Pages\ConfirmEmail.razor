﻿@page "/Account/ConfirmEmail"
@layout WebsiteLayout

@using System.Text
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using PremierAutoTag.Server.Data.Entities
@using PremierAutoTag.Server.Data.DB
@using PremierAutoTag.Razor.Layout
@using Microsoft.EntityFrameworkCore
@using PremierAutoTag.Framework.Core.DataProtections
@using PremierAutoTag.ServiceContracts.Services

@inject UserManager<ApplicationUser> UserManager
@inject IdentityRedirectManager RedirectManager
@inject NavigationManager NavigationManager
@inject ILogger<ConfirmEmail> Logger
@inject PremierAutoTag.Web.Services.IRoleBasedRedirectService RoleBasedRedirectService
@inject ApplicationDbContext DbContext
@inject IDataProtection DataProtection
@inject IEmailVerificationService EmailVerificationService

<PageTitle>Email Verification</PageTitle>

<section class="">
    <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <div class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
            @if (isVerifying)
            {
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
                        <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Verifying Your Email</h1>
                    <h2 class="text-gray-600 text-sm md:text-base text-center">
                        Please wait while we verify your email address...
                    </h2>
                </div>
            }
            else if (verificationSuccess)
            {
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>

                    <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Email Verified Successfully!</h1>
                    <h2 class="text-gray-600 text-sm md:text-base text-center mb-6">
                        @if (isDealerUser)
                        {
                            <span>Your email address has been confirmed. You can now continue with your dealer registration process.</span>
                        }
                        else
                        {
                            <span>Email has been verified successfully and the registration has been completed. You can now log in to access your AutoTag account.</span>
                        }
                    </h2>
                </div>



                <div class="flex flex-col gap-3 md:gap-4">
                    @if (isDealerUser)
                    {
                        <a href="@GetDealerRegistrationUrl()"
                           class="w-full py-2 md:py-2.5 px-4 bg-primary hover:bg-primaryDark text-white font-medium text-sm md:text-base rounded-lg focus:ring-1 focus:ring-primary outline-none transition-colors text-center">
                            Continue to Registration
                        </a>
                    }
                    else
                    {
                        <a href="/Account/Login"
                           class="w-full py-2 md:py-2.5 px-4 bg-primary hover:bg-primaryDark text-white font-medium text-sm md:text-base rounded-lg focus:ring-1 focus:ring-primary outline-none transition-colors text-center">
                            Continue to Login
                        </a>
                    }
                </div>
            }
            else
            {
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                        <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>

                    <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Verification Failed</h1>
                    <h2 class="text-gray-600 text-sm md:text-base text-center mb-6">
                        @statusMessage
                    </h2>
                </div>

                <div class="flex flex-col gap-3 md:gap-4">
                    <a href="/Account/ResendEmailConfirmation"
                       class="w-full py-2 md:py-2.5 px-4 bg-primary hover:bg-primaryDark text-white font-medium text-sm md:text-base rounded-lg focus:ring-1 focus:ring-primary outline-none transition-colors text-center">
                        Resend Verification Email
                    </a>

                    <a href="/Account/Login"
                       class="w-full py-2 md:py-2.5 px-4 bg-white hover:bg-gray-50 text-gray-700 font-medium text-sm md:text-base rounded-lg border border-gray-300 focus:ring-1 focus:ring-gray-300 outline-none transition-colors text-center">
                        Back to Login
                    </a>
                </div>
            }
        </div>
    </div>
</section>

@code {
    private string? statusMessage;
    private bool isVerifying = true;
    private bool verificationSuccess = false;
    private bool isDealerUser = false;
    private string? verifiedUserId;
    private int _dealerRegistrationStep = 2;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromQuery]
    private string? UserId { get; set; }

    [SupplyParameterFromQuery]
    private string? Code { get; set; }

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (Code is null)
        {
            isVerifying = false;
            statusMessage = "Invalid verification link. Please check your email and try again.";
            return;
        }

        try
        {
            // Check if this is a dealer verification token (new format) or regular token (old format)
            if (UserId is null)
            {
                // New dealer verification format - code contains user ID and step
                await HandleDealerEmailVerification();
            }
            else
            {
                // Old format - separate userId and code parameters
                await HandleRegularEmailVerification();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during email verification");
            isVerifying = false;
            statusMessage = "An error occurred during verification. Please try again later.";
        }
    }

    private async Task HandleDealerEmailVerification()
    {
        var result = await EmailVerificationService.ValidateDealerEmailVerificationTokenAsync(Code!);

        isVerifying = false;

        if (result.IsValid)
        {
            verificationSuccess = true;
            verifiedUserId = result.UserId;

            // Check if this is a dealer user and store registration info for button
            var user = await UserManager.FindByIdAsync(result.UserId!);
            if (user != null)
            {
                var roles = await UserManager.GetRolesAsync(user);
                isDealerUser = roles.Contains("Dealer");

                if (isDealerUser)
                {
                    // Store dealer registration info for the button
                    _dealerRegistrationStep = result.RegistrationStep + 1;
                }
            }

            statusMessage = "Thank you for confirming your email.";
        }
        else
        {
            statusMessage = result.ErrorMessage ?? "Error confirming your email. The verification link may have expired.";
        }
    }

    private async Task HandleRegularEmailVerification()
    {
        var user = await UserManager.FindByIdAsync(UserId!);
        if (user is null)
        {
            HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
            statusMessage = $"Error loading user with ID {UserId}";
            isVerifying = false;
            return;
        }

        if (user.EmailConfirmed)
        {
            statusMessage = "Email is already verified.";
            verificationSuccess = true;
            isVerifying = false;
            verifiedUserId = UserId;
            return;
        }

        var code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(Code!));
        var result = await UserManager.ConfirmEmailAsync(user, code);

        isVerifying = false;

        if (result.Succeeded)
        {
            verificationSuccess = true;
            verifiedUserId = UserId;
            statusMessage = "Thank you for confirming your email.";
        }
        else
        {
            statusMessage = "Error confirming your email. The verification link may have expired.";
        }
    }

    private string GetDealerRegistrationUrl()
    {
        if (!string.IsNullOrEmpty(verifiedUserId))
        {
            var encodedData = DataProtection.Encode($"{verifiedUserId}|{_dealerRegistrationStep}");
            return $"/Account/Register/Dealership?code={encodedData}";
        }
        return "/Account/Register/Dealership";
    }


}
