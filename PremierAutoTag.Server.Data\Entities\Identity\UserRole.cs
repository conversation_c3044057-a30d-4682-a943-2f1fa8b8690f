﻿using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Server.Data.Entities
{
    public class UserRole : IdentityUserRole<string>
    {
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        [StringLength(450)]
        public string? CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; }

    }
}
