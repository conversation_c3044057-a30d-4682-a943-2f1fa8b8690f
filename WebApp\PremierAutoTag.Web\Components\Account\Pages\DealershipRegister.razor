﻿@page "/Account/Register/Dealership"
@layout WebsiteLayout

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using Microsoft.AspNetCore.Http
@using PremierAutoTag.Framework.Core.DataProtections
@using PremierAutoTag.Framework.Core.Enums
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.Server.Data.Entities
@using PremierAutoTag.Server.Data.DB
@using PremierAutoTag.ServiceContracts.Services
@using Microsoft.EntityFrameworkCore
@using PremierAutoTag.Server.Data

@inject IDataProtection DataProtection
@inject UserManager<ApplicationUser> UserManager
@inject IUserStore<ApplicationUser> UserStore
@inject SignInManager<ApplicationUser> SignInManager
@inject RoleManager<IdentityRole> RoleManager
@inject ILogger<Register> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject ApplicationDbContext DbContext
@inject IStripePaymentService StripePaymentService
@inject IHttpContextAccessor HttpContextAccessor
@inject PremierAutoTag.Web.Services.IRoleBasedRedirectService RoleBasedRedirectService
@inject IEmailVerificationService EmailVerificationService
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Register</PageTitle>


<section class="">
    @if (_decodedStep == REGISTRATION_STEPS.Registration)
    {
        <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
            <EditForm Enhance Model="Input" asp-route-returnUrl="@ReturnUrl" method="post" OnValidSubmit="RegisterUser" FormName="register" class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
                <DataAnnotationsValidator />
                <div>
                    <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Create your account</h1>
                    <h2 class="text-gray-600 text-sm md:text-base text-center">
                        Please fill the below fields to create
                        your account
                    </h2>
                </div>
                <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <InputText @bind-Value="Input.FirstName"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="First Name" />
                        <ValidationMessage For="() => Input.FirstName" class="text-red-500 text-sm mt-1" />
                    </div>
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <InputText @bind-Value="Input.LastName"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Last Name" />
                        <ValidationMessage For="() => Input.LastName" class="text-red-500 text-sm mt-1" />
                    </div>
                </div>

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">
                        Email <span class="text-red-500">*</span>
                    </label>
                    <InputText @bind-Value="Input.Email"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="Your Email" />
                    <ValidationMessage For="() => Input.Email" class="text-red-500 text-sm mt-1" />
                </div>

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">
                        Phone Number <span class="text-red-500">*</span>
                    </label>
                    <InputText @bind-Value="Input.Phone"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="Your Phone Number" />
                    <ValidationMessage For="() => Input.Phone" class="text-red-500 text-sm mt-1" />
                </div>

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">Password</label>
                    <InputText @bind-Value="Input.Password"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="••••••••" />
                    <ValidationMessage For="() => Input.Password" class="text-red-500 text-sm mt-1" />
                </div>

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">Confirm Password</label>
                    <InputText @bind-Value="Input.ConfirmPassword"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="••••••••" />
                    <ValidationMessage For="() => Input.ConfirmPassword" class="text-red-500 text-sm mt-1" />
                </div>

                <div class="flex flex-col gap-2">
                    <div class="flex items-center gap-2">
                        <image src="images/checkIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                        <image src="images/checkGreenIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                        <span class="text-xs md:text-sm text-vampireGray block">Must be at least 8 characters</span>
                    </div>

                    <div class="flex items-center gap-2">
                        <image src="images/checkIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                        <image src="images/checkGreenIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                        <span class="text-xs md:text-sm text-vampireGray block">
                            Must contain one special
                            character
                        </span>
                    </div>
                </div>

                <button class="w-full text-center py-2 md:py-2.5 rounded-lg bg-primary hover:bg-primaryDark text-white text-sm md:text-base font-semibold"
                        type="submit">
                    Get Started
                </button>

                <div class="flex justify-center gap-1">
                    <span class="text-sm text-gray-600">Already have an account?</span>
                    <a href="/Account/Login"
                       class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4 ">
                        Log In
                    </a>
                </div>

            </EditForm>
        </div>
    }
    else if (_decodedStep == REGISTRATION_STEPS.Dealer)
    {
        <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
            <EditForm Enhance Model="InputDealer" asp-route-returnUrl="@ReturnUrl" method="post" OnValidSubmit="SaveDealerInfo" FormName="dealerInfo" class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
                <DataAnnotationsValidator />
                <div>
                    <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Create your account</h1>
                    <h2 class="text-gray-600 text-sm md:text-base text-center">
                        Please fill the below fields to create
                        your account
                    </h2>
                </div>
                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">
                        Dealership Name <span class="text-red-500">*</span>
                    </label>
                    <InputText @bind-Value="InputDealer.DealerName"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="" />
                </div>

                <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Dealership ID <span class="text-red-500">*</span>
                        </label>
                        <InputText @bind-Value="InputDealer.DealerId"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="" />
                    </div>
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Tax ID <span class="text-red-500">*</span>
                        </label>
                        <InputText @bind-Value="InputDealer.TaxId"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="" />
                    </div>
                </div>

                <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Registered State
                        </label>
                        <div class=" relative flex md:!w-full flex-col mb-3 md:mb-0 gap-1 text-slate-700 ">
                            <svg xmlns=" http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                 class="absolute w-6 h-6 pointer-events-none right-2 top-2">
                                <path fillRule="evenodd"
                                      d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                                      clipRule="evenodd" />
                            </svg>
                            <InputSelect @bind-Value="InputDealer.StateId"
                                         class="w-full appearance-none rounded-lg md:rounded-lg pe-12 text-[#212529] border border-gray-200 focus:border-gray-200 focus:ring-0 bg-white  px-4 py-2    focus-visible:outline-offset-2 focus-visible:outline-0 disabled:cursor-not-allowed disabled:opacity-75 ">
                                <option value="0">Choose State</option>
                                <option value="2">Connecticut</option>
                            </InputSelect>
                        </div>
                    </div>

                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Subsidiary of another of another company <span class="text-red-500">*</span>
                        </label>
                        <div class="flex items-center gap-3 mt-3">
                            <InputRadioGroup @bind-Value="InputDealer.Subsidary" class="flex flex-col gap-2">
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <InputRadio class="peer hidden" Value="true" />
                                    <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                        <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                    </span>
                                    <span class="text-gray-700 text-sm md:text-base font-medium">Yes</span>
                                </label>

                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <InputRadio class="peer hidden" Value="false" />
                                    <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                        <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                    </span>
                                    <span class="text-gray-700 text-sm md:text-base font-medium">No</span>
                                </label>
                            </InputRadioGroup>

                        </div>
                    </div>
                </div>

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">
                        Address <span class="text-red-500">*</span>
                    </label>
                    <InputText @bind-Value="InputDealer.Address"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="" />
                </div>

                <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Dealership Phone # <span class="text-red-500">*</span>
                        </label>
                        <InputText @bind-Value="InputDealer.Phone"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="" />
                    </div>
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Authorized Company <span class="text-red-500">*</span>
                        </label>
                        <InputText @bind-Value="InputDealer.AuthorizedCompany"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="" />
                    </div>
                </div>

                <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Administrative email address <span class="text-red-500">*</span>
                        </label>
                        <InputText @bind-Value="InputDealer.AdministrativeEmail"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="" />
                    </div>
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Administrative phone <span class="text-red-500">*</span>
                        </label>
                        <InputText @bind-Value="InputDealer.AdministrativePhone"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="" />
                    </div>
                </div>

                <div class="border-b border-b-gray-200 pb-5">
                    <label for="" class="font-semibold text-sm md:text-lg text-gray-m-800">Dealership lots</label>
                    <div class="flex items-center gap-3 mt-3">

                        <InputRadioGroup @bind-Value="InputDealer.DealershipIOT" class="flex flex-col gap-2">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="DEALERSHIP_IOT_TYPES.Sole" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">Sole</span>
                            </label>

                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="DEALERSHIP_IOT_TYPES.OnetoThree" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">1 - 3</span>
                            </label>

                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="DEALERSHIP_IOT_TYPES.Threeplus" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">3+</span>
                            </label>
                        </InputRadioGroup>
                    </div>

                </div>

                <div class="border-b border-b-gray-200 pb-5">
                    <label for="" class="font-semibold text-sm md:text-lg text-gray-m-800">
                        Number of vehicles on
                        lot
                    </label>
                    <div class="flex items-center gap-3 mt-3">

                        <InputRadioGroup @bind-Value="InputDealer.NoOfVehicalsOnIOT" class="flex flex-col gap-2">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_ON_IOT_TYPES.OnetoTwentySix" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">1 - 26</span>
                            </label>

                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_ON_IOT_TYPES.TwentySixtoHundred" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">26 - 100</span>
                            </label>

                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_ON_IOT_TYPES.HundredPlus" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">100+</span>
                            </label>
                        </InputRadioGroup>
                    </div>

                </div>

                <div class="border-b border-b-gray-200 pb-5">
                    <label for="" class="font-semibold text-sm md:text-lg text-gray-m-800">
                        Average vehicles sold per
                        month
                    </label>
                    <div class="flex items-center gap-3 mt-3">
                        <InputRadioGroup @bind-Value="InputDealer.AvgVehicalsSoldPerMonth" class="flex flex-col gap-2">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_SOLD_IOT.OnetoTen" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">1 - 10</span>
                            </label>

                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_SOLD_IOT.EleventoTwentyFive" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">11 - 25</span>
                            </label>

                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_SOLD_IOT.TwentyFivePlus" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">26+</span>
                            </label>
                        </InputRadioGroup>

                    </div>

                </div>

                <div class="flex items-center">


                    <label for="remember" class="mt-1 custom-checkbox">
                        <InputCheckbox type="checkbox" id="remember" @bind-Value="InputDealer.Consent" />
                        <span class="checkmark"></span>

                    </label>

                    <div class="ml-2 mb-0.5 text-xs md:text-sm">
                        <label for="remember" class="text-gray-700">
                            By save & continue you agree the <a href="tems.html"
                                                                class="font-semibold underline underline-offset-2">term and condition</a> of
                            CarRegistration
                        </label>
                    </div>
                </div>


                <button class="w-full py-2.5 flex items-center gap-2 justify-center rounded-lg bg-primary cursor-pointer group hover:bg-primaryDark text-white text-base md:text-base font-semibold"
                        type="submit">
                    Continue <span class="group-hover:transition duration-300 group-hover:translate-x-1">
                        <svg width="16"
                             height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.913 8.45783C15.9995 8.23906 16.0222 7.99833 15.978 7.76609C15.9339 7.53386 15.8249 7.32055 15.665 7.15317L11.0933 2.36541C10.9878 2.25109 10.8617 2.15991 10.7223 2.09718C10.5828 2.03445 10.4329 2.00143 10.2811 2.00005C10.1294 1.99866 9.97885 2.02895 9.83839 2.08913C9.69793 2.14931 9.57032 2.23819 9.463 2.35057C9.35569 2.46296 9.27083 2.5966 9.21336 2.7437C9.15589 2.8908 9.12697 3.04841 9.12829 3.20734C9.12961 3.36627 9.16114 3.52333 9.22104 3.66936C9.28094 3.81539 9.36801 3.94747 9.47717 4.05788L12.0991 6.80366H1.14293C0.839806 6.80366 0.549097 6.92977 0.334756 7.15423C0.120416 7.3787 0 7.68315 0 8.0006C0 8.31805 0.120416 8.62249 0.334756 8.84696C0.549097 9.07143 0.839806 9.19754 1.14293 9.19754H12.0991L9.47831 11.9421C9.36915 12.0525 9.28208 12.1846 9.22218 12.3306C9.16228 12.4767 9.13075 12.6337 9.12943 12.7927C9.12811 12.9516 9.15703 13.1092 9.2145 13.2563C9.27197 13.4034 9.35683 13.537 9.46415 13.6494C9.57146 13.7618 9.69907 13.8507 9.83953 13.9109C9.97999 13.9711 10.1305 14.0013 10.2823 14C10.434 13.9986 10.584 13.9656 10.7234 13.9028C10.8629 13.8401 10.989 13.7489 11.0944 13.6346L15.6661 8.84683C15.772 8.7354 15.8559 8.60322 15.913 8.45783V8.45783Z"
                                  fill="white" />
                        </svg>
                    </span>
                </button>

                <div class="flex justify-center gap-1">
                    <span class="text-sm text-gray-600">Already have an account?</span>
                    <a href="/Account/Login"
                       class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4 ">
                        Log
                        In
                    </a>
                </div>

            </EditForm>
        </div>
    }
    else if (_decodedStep == REGISTRATION_STEPS.Subscription)
    {
        <div class="md:pt-40 pt-24 pb-12 flex justify-center p-2 md:px-0">
            <div class="bg-white container  w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">

                <div>
                    <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Choose your plan</h1>
                    <h2 class="text-gray-600 text-sm md:text-base text-center">
                        Your details has been saved you please
                        login or
                        create account to proceed the next step of regitration
                    </h2>
                </div>

                @if (_subscriptionPlans.Any())
                {
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach (var plan in _subscriptionPlans)
                        {
                            <div class="border rounded-xl p-6 hover:shadow-lg transition-shadow @(plan.IsPopular ? "border-primary bg-primary/5" : "border-gray-200")">
                                @if (plan.IsPopular)
                                {
                                    <div class="bg-primary text-white text-xs font-medium px-3 py-1 rounded-full w-fit mb-4">
                                        Most Popular
                                    </div>
                                }

                                <div class="pb-4 border-b border-gray-200 mb-4">
                                    <h2 class="text-lg font-semibold text-gray-800 mb-2">@plan.Name</h2>
                                    <div class="flex items-baseline">
                                        <span class="text-4xl font-bold text-gray-900">
                                            @if (plan.AnnualPrice == 0)
                                            {
                                                <text>Free</text>
                                            }
                                            else
                                            {
                                                <text>$@plan.AnnualPrice.ToString("N0")</text>
                                            }
                                        </span>
                                        @if (plan.AnnualPrice > 0)
                                        {
                                            <span class="text-gray-600 ml-2">/year</span>
                                        }
                                    </div>
                                    @if (!string.IsNullOrEmpty(plan.Description))
                                    {
                                        <p class="text-gray-600 text-sm mt-2">@plan.Description</p>
                                    }
                                </div>

                                <div class="space-y-3 mb-6">
                                    <div class="flex items-center gap-2">
                                        <img src="images/itemCheckedIcon.svg" class="w-5 h-5" alt="">
                                        <span class="text-sm text-gray-600">
                                            @(plan.MaxVehicles == -1 ? "Unlimited" : plan.MaxVehicles.ToString()) vehicles
                                        </span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <img src="images/itemCheckedIcon.svg" class="w-5 h-5" alt="">
                                        <span class="text-sm text-gray-600">
                                            @(plan.MaxUsers == -1 ? "Unlimited" : plan.MaxUsers.ToString()) users
                                        </span>
                                    </div>
                                    @if (plan.StandAloneQuotesDiscountPercent > 0)
                                    {
                                        <div class="flex items-center gap-2">
                                            <img src="images/itemCheckedIcon.svg" class="w-5 h-5" alt="">
                                            <span class="text-sm text-gray-600">
                                                @plan.StandAloneQuotesDiscountPercent% discount on quotes
                                            </span>
                                        </div>
                                    }
                                    @if (plan.RegistrationTitleServicesDiscountPercent > 0)
                                    {
                                        <div class="flex items-center gap-2">
                                            <img src="images/itemCheckedIcon.svg" class="w-5 h-5" alt="">
                                            <span class="text-sm text-gray-600">
                                                @plan.RegistrationTitleServicesDiscountPercent% discount on registration/title services
                                            </span>
                                        </div>
                                    }
                                    @if (plan.HasDirectSubmission)
                                    {
                                        <div class="flex items-center gap-2">
                                            <img src="images/itemCheckedIcon.svg" class="w-5 h-5" alt="">
                                            <span class="text-sm text-gray-600">Direct submission to DMV</span>
                                        </div>
                                    }
                                    @if (plan.HasDedicatedClientManager)
                                    {
                                        <div class="flex items-center gap-2">
                                            <img src="images/itemCheckedIcon.svg" class="w-5 h-5" alt="">
                                            <span class="text-sm text-gray-600">Dedicated client manager</span>
                                        </div>
                                    }
                                </div>

                                @if (plan.AnnualPrice == 0)
                                {
                                    <!-- Free Plan Form -->
                                    <form data-enhance method="post">
                                        <input type="hidden" name="SelectedPlanId" value="@plan.Id" />
                                        <button type="submit" name="action" value="SelectFreePlan"
                                                class="w-full px-6 py-3 rounded-lg font-medium transition-colors @(plan.IsPopular ? "bg-primary hover:bg-primaryDark text-white" : "bg-gray-100 hover:bg-gray-200 text-gray-800")">
                                            Choose @plan.Name
                                        </button>
                                    </form>
                                }
                                else
                                {
                                    <!-- Paid Plan Form -->
                                    <form data-enhance method="post">
                                        <input type="hidden" name="SelectedPlanId" value="@plan.Id" />
                                        <button type="submit" name="action" value="SelectPaidPlan"
                                                class="w-full px-6 py-3 rounded-lg font-medium transition-colors @(plan.IsPopular ? "bg-primary hover:bg-primaryDark text-white" : "bg-gray-100 hover:bg-gray-200 text-gray-800")">
                                            Choose @plan.Name
                                        </button>
                                    </form>
                                }
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-8">
                        <p class="text-gray-600">Loading subscription plans...</p>
                    </div>
                }
            </div>
        </div>
    }
</section>


@code {
    private IEnumerable<IdentityError>? identityErrors;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromForm]
    private InputDealerModel InputDealer { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    [SupplyParameterFromQuery(Name = "step")]
    public string CurrentStep { get; set; } = string.Empty;

    [SupplyParameterFromForm]
    public long SelectedPlanId { get; set; }

    [SupplyParameterFromForm]
    public string Action { get; set; } = "";

    private REGISTRATION_STEPS _decodedStep;
    private List<SubscriptionPlan> _subscriptionPlans = new();

    private HttpContext HttpContext => HttpContextAccessor.HttpContext!;

    private string? Message => identityErrors is null ? null : $"Error: {string.Join(", ", identityErrors.Select(error => error.Description))}";

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Default to Registration step if no step is provided
            if (string.IsNullOrEmpty(CurrentStep))
            {
                _decodedStep = REGISTRATION_STEPS.Registration;
            }
            else
            {
                var data = DataProtection?.Decode(CurrentStep);
                _decodedStep = (REGISTRATION_STEPS)Enum.Parse(typeof(REGISTRATION_STEPS), data!);

                // Check email verification for steps beyond Registration
                if (_decodedStep != REGISTRATION_STEPS.Registration)
                {
                    await CheckEmailVerificationStatus();
                }
            }

            // Load subscription plans for the subscription step
            if (_decodedStep == REGISTRATION_STEPS.Subscription)
            {
                await LoadSubscriptionPlans();

                // Handle subscription plan selection
                if (!string.IsNullOrEmpty(Action) && SelectedPlanId > 0)
                {
                    if (Action == "SelectFreePlan")
                    {
                        await HandleFreePlanSelection();
                    }
                    else if (Action == "SelectPaidPlan")
                    {
                        await HandlePaidPlanSelection();
                    }
                }
            }
        }
        catch (Exception)
        {
            // If decoding fails, default to Registration step
            _decodedStep = REGISTRATION_STEPS.Registration;
        }
    }

    public async Task RegisterUser(EditContext editContext)
    {
        var user = CreateUser();

        await UserStore.SetUserNameAsync(user, Input.Email, CancellationToken.None);
        var emailStore = GetEmailStore();
        await emailStore.SetEmailAsync(user, Input.Email, CancellationToken.None);
        user.PhoneNumber = Input.Phone;

        var result = await UserManager.CreateAsync(user, Input.Password);

        if (!result.Succeeded)
        {
            identityErrors = result.Errors;
            return;
        }

        Logger.LogInformation("User created a new account with password.");

        // Assign Dealer role to dealership users
        var userId = await UserManager.GetUserIdAsync(user);
        await UserManager.AddToRoleAsync(user, AppRoles.Dealer);

        // Generate email verification token and send verification email
        var code = await EmailVerificationService.GenerateEmailVerificationTokenAsync(userId);
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
            new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code });

        // Send verification email for dealer user
        var emailSent = await EmailVerificationService.SendDealerVerificationEmailAsync(
            userId, Input.Email, callbackUrl, "Dealer");

        if (!emailSent)
        {
            Logger.LogWarning("Failed to send verification email to {Email} for dealer {UserId}", Input.Email, userId);
        }

        // Sign in the user but don't redirect to next step yet
        await SignInManager.SignInAsync(user, isPersistent: false);

        // Redirect to email confirmation page instead of next step
        var encodedData = DataProtection.Encode($"{Input.Email}|dealer");
        RedirectManager.RedirectTo(
            "Account/RegisterConfirmation",
            new() { ["data"] = encodedData });
    }

    public async Task SaveDealerInfo(EditContext editContext)
    {
        try
        {
            // Get the current user
            var user = await UserManager.GetUserAsync(HttpContext.User);
            if (user == null)
            {
                NavigationManager.NavigateTo("/Account/Login");
                return;
            }

            var userId = await UserManager.GetUserIdAsync(user);

            // Create or update UserProfile
            var existingProfile = await DbContext.Profiles.FirstOrDefaultAsync(p => p.Id == userId);

            if (existingProfile == null)
            {
                var userProfile = new UserProfile
                {
                    Id = userId,
                    FirstName = Input.FirstName,
                    LastName = Input.LastName,
                    Phone = Input.Phone,
                    DealerName = InputDealer.DealerName,
                    DealerId = InputDealer.DealerId,
                    TaxId = InputDealer.TaxId,
                    Address = InputDealer.Address,
                    AuthorizedCompany = InputDealer.AuthorizedCompany,
                    AdministrativeEmail = Input.Email,
                    AdministrativePhone = Input.Phone,
                    StateId = int.TryParse(InputDealer.StateId, out var stateId) ? stateId : 0,
                    Subsidary = InputDealer.Subsidary,
                    DEALERSHIP_IOT_TYPES = InputDealer.DealershipIOT,
                    VEHICAL_ON_IOT_TYPES = InputDealer.NoOfVehicalsOnIOT,
                    VEHICAL_SOLD_IOT = InputDealer.AvgVehicalsSoldPerMonth,
                    SubscriptionPlanId = null, // Will be set in subscription step
                    CreatedBy = userId,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true,
                    IsDelete = false
                };

                DbContext.Profiles.Add(userProfile);
            }
            else
            {
                // Update existing profile
                existingProfile.DealerName = InputDealer.DealerName;
                existingProfile.DealerId = InputDealer.DealerId;
                existingProfile.TaxId = InputDealer.TaxId;
                existingProfile.Address = InputDealer.Address;
                existingProfile.AuthorizedCompany = InputDealer.AuthorizedCompany;
                existingProfile.StateId = int.TryParse(InputDealer.StateId, out var stateId2) ? stateId2 : 0;
                existingProfile.Subsidary = InputDealer.Subsidary;
                existingProfile.DEALERSHIP_IOT_TYPES = InputDealer.DealershipIOT;
                existingProfile.VEHICAL_ON_IOT_TYPES = InputDealer.NoOfVehicalsOnIOT;
                existingProfile.VEHICAL_SOLD_IOT = InputDealer.AvgVehicalsSoldPerMonth;
                existingProfile.ModifiedAt = DateTime.UtcNow;
                existingProfile.ModifiedBy = userId;
            }

            await DbContext.SaveChangesAsync();

            // Redirect to the subscription step
            var nextStep = DataProtection?.Encode(REGISTRATION_STEPS.Subscription.ToString());
            NavigationManager.NavigateTo($"/Account/Register/Dealership?step={nextStep}", forceLoad: true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving dealer information");
            // Handle error appropriately
        }
    }

    private ApplicationUser CreateUser()
    {
        try
        {
            return Activator.CreateInstance<ApplicationUser>();
        }
        catch
        {
            throw new InvalidOperationException($"Can't create an instance of '{nameof(ApplicationUser)}'. " +
                $"Ensure that '{nameof(ApplicationUser)}' is not an abstract class and has a parameterless constructor.");
        }
    }

    private IUserEmailStore<ApplicationUser> GetEmailStore()
    {
        if (!UserManager.SupportsUserEmail)
        {
            throw new NotSupportedException("The default UI requires a user store with email support.");
        }
        return (IUserEmailStore<ApplicationUser>)UserStore;
    }

    private async Task LoadSubscriptionPlans()
    {
        _subscriptionPlans = await DbContext.SubscriptionPlans
            .Where(sp => sp.IsActive && !sp.IsDelete)
            .OrderBy(sp => sp.DisplayOrder)
            .ThenBy(sp => sp.AnnualPrice)
            .ToListAsync();
    }

    private async Task HandleFreePlanSelection()
    {
        try
        {
            // Get the current user
            var user = await UserManager.GetUserAsync(HttpContext.User);
            if (user == null)
            {
                NavigationManager.NavigateTo("/Account/Login");
                return;
            }

            var userId = await UserManager.GetUserIdAsync(user);
            var planId = SelectedPlanId;

            // Verify it's a free plan
            var plan = await DbContext.SubscriptionPlans.FirstOrDefaultAsync(p => p.Id == planId);
            if (plan == null || plan.AnnualPrice > 0)
            {
                Logger.LogWarning("Attempted to select non-free plan through free plan form");
                return;
            }

            // Update user profile with subscription plan
            var userProfile = await DbContext.Profiles.FirstOrDefaultAsync(p => p.Id == userId);
            if (userProfile != null)
            {
                userProfile.SubscriptionPlanId = planId;
                userProfile.ModifiedAt = DateTime.UtcNow;
                userProfile.ModifiedBy = userId;
            }

            // Create user subscription record
            var subscription = new UserSubscription
            {
                UserId = userId,
                SubscriptionPlanId = planId,
                Status = "Active",
                StartDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow.AddYears(1), // Free plans still have annual terms
                AutoRenew = true
            };

            DbContext.UserSubscriptions.Add(subscription);
            await DbContext.SaveChangesAsync();

            // Complete registration and redirect to role-based dashboard
            var dashboardUrl = await RoleBasedRedirectService.GetDashboardUrlForUserAsync(userId);
            NavigationManager.NavigateTo(dashboardUrl, forceLoad: true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error completing free plan registration");
        }
    }

    private async Task HandlePaidPlanSelection()
    {
        try
        {
            // Get the current user
            var user = await UserManager.GetUserAsync(HttpContext.User);
            if (user == null)
            {
                NavigationManager.NavigateTo("/Account/Login");
                return;
            }

            var userId = await UserManager.GetUserIdAsync(user);
            var planId = SelectedPlanId;

            // Verify it's a paid plan
            var plan = await DbContext.SubscriptionPlans.FirstOrDefaultAsync(p => p.Id == planId);
            if (plan == null || plan.AnnualPrice == 0)
            {
                Logger.LogWarning("Attempted to select free plan through paid plan form");
                return;
            }

            var request = new CreateCheckoutSessionRequest
            {
                SubscriptionPlanId = planId,
                UserId = userId,
                UserEmail = user.Email ?? "",
                SuccessUrl = $"{NavigationManager.BaseUri}api/Stripe/payment-success",
                CancelUrl = $"{NavigationManager.BaseUri}Account/Register/Dealership?step={DataProtection?.Encode(REGISTRATION_STEPS.Subscription.ToString())}",
                Metadata = new Dictionary<string, string>
                {
                    { "registration_type", "dealership" },
                    { "dealer_name", InputDealer.DealerName },
                    { "dealer_id", InputDealer.DealerId }
                }
            };

            var checkoutUrl = await StripePaymentService.CreateCheckoutSessionAsync(request);
            NavigationManager.NavigateTo(checkoutUrl, forceLoad: true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating Stripe checkout session");
        }
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        public string Email { get; set; } = "";

        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        public string Password { get; set; } = "";

        [DataType(DataType.Password)]
        [Display(Name = "Confirm password")]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = "";

        [Required]
        public string FirstName { get; set; } = "";

        [Required]
        public string LastName { get; set; } = "";

        [Required]
        public string Phone { get; set; } = "";

        public REGISTRATION_STEPS Step { get; set; } = REGISTRATION_STEPS.Registration;
    }

    private sealed class InputDealerModel
    {
        [Required]
        public string DealerName { get; set; } = "";

        [Required]
        public string DealerId { get; set; } = "";

        [Required]
        public string TaxId { get; set; } = "";

        [Required]
        public string StateId { get; set; } = "";

        public bool Subsidary { get; set; }

        [Required]
        public string Address { get; set; } = "";

        [Required]
        public string Phone { get; set; } = "";

        [Required]
        public string AuthorizedCompany { get; set; } = "";

        [Required]
        public string AdministrativeEmail { get; set; } = "";

        [Required]
        public string AdministrativePhone { get; set; } = "";

        [Required]
        public DEALERSHIP_IOT_TYPES DealershipIOT { get; set; }

        [Required]
        public VEHICAL_ON_IOT_TYPES NoOfVehicalsOnIOT { get; set; }

        [Required]
        public VEHICAL_SOLD_IOT AvgVehicalsSoldPerMonth { get; set; }

        public bool Consent { get; set; }

        public REGISTRATION_STEPS Step { get; set; } = REGISTRATION_STEPS.Registration;
    }

    private async Task CheckEmailVerificationStatus()
    {
        try
        {
            // Get current user
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity?.IsAuthenticated == true)
            {
                var userId = UserManager.GetUserId(user);
                if (!string.IsNullOrEmpty(userId))
                {
                    var isEmailVerified = await EmailVerificationService.IsEmailVerifiedAsync(userId);

                    if (!isEmailVerified)
                    {
                        // Redirect back to registration confirmation page
                        var currentUser = await UserManager.FindByIdAsync(userId);
                        if (currentUser != null)
                        {
                            var encodedData = DataProtection.Encode($"{currentUser.Email}|dealer");
                            RedirectManager.RedirectTo(
                                "Account/RegisterConfirmation",
                                new() { ["data"] = encodedData });
                        }
                        else
                        {
                            // If user not found, redirect to registration
                            NavigationManager.NavigateTo("/Account/Register/Dealership", forceLoad: true);
                        }
                    }
                }
            }
            else
            {
                // User not authenticated, redirect to registration
                NavigationManager.NavigateTo("/Account/Register/Dealership", forceLoad: true);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error checking email verification status");
            // On error, redirect to registration step
            NavigationManager.NavigateTo("/Account/Register/Dealership", forceLoad: true);
        }
    }

}
