﻿using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace PremierAutoTag.Framework.Core.ViewModels
{
    public class SelectListItem : INotifyPropertyChanged
    {
        public SelectListItem()
        {

        }
        public SelectListItem(int value, string text)
        {
            IntValue = value;
            Text = text;
        }
        public SelectListItem(int value, string text, bool isChecked = false)
        {
            IntValue = value;
            Text = text;
            Checked = isChecked;
        }

        public SelectListItem(int value, string text, string AdditionalValue, bool isChecked = false)
        {
            IntValue = value;
            Text = text;
            this.AdditionalValue = AdditionalValue;
            Checked = isChecked;
        }

        public SelectListItem(string value, string? text, string AdditionalValue, bool isChecked = false)
        {
            Value = value;
            Text = text;
            this.AdditionalValue = AdditionalValue;
            Checked = isChecked;
        }

        public SelectListItem(string value, string text)
        {
            Value = value;
            Text = text;
        }

        public long? LongValue
        {
            get
            {
                if (long.TryParse(Value, out long value))
                {
                    return value;
                }
                return null;
            }
            set
            {
                Value = value?.ToString();
            }
        }

        public long? IntValue
        {
            get
            {
                if (long.TryParse(Value, out long value))
                {
                    return value;
                }
               return null;
            }
            set
            {
                Value = value.ToString();
            }
        }


        public string? AdditionalValue { get; set; }

        private string? _value;
        public string? Value
        {
            get { return _value; }
            set { _value = value; }
        }

        public string? Text { get; set; }

        private bool _checked;
        public bool Checked
        {
            get { return _checked; }
            set
            {
                if (_checked != value)
                {
                    _checked = value;
                    NotifyPropertyChanged();
                }
            }
        }


        public event PropertyChangedEventHandler? PropertyChanged;

        public void NotifyPropertyChanged([CallerMemberName] string? propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

    }
      
}
