﻿using System.Text;
using System.Text.Json;
using System.Net.Http.Json;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts;
using PremierAutoTag.ServiceContracts.Features.SubscriptionPlan;
namespace PremierAutoTag.Razor.Features.SubscriptionPlan;
public class SubscriptionPlanClientSideFormDataService : ISubscriptionPlanFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public SubscriptionPlanClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<long> SaveAsync(SubscriptionPlanFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<long>($"api/SubscriptionPlansForm/Save", formBusinessObject);
	}
	public async Task<SubscriptionPlanFormBusinessObject?> GetItemByIdAsync(long id)
	{
		return await _httpClient.GetFromJsonAsync<SubscriptionPlanFormBusinessObject>($"api/SubscriptionPlansForm/GetItemById?id=" + id);
	}
}
