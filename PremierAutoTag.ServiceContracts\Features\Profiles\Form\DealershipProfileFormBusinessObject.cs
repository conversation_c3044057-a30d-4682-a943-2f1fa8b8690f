﻿using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.ServiceContracts.Features.DealershipProfile;
public class DealershipProfileFormBusinessObject
{
	public string Id { get; set; } = "";

	[Required(ErrorMessage = "Dealership name is required")]
	[StringLength(150, ErrorMessage = "Dealership name cannot exceed 150 characters")]
	public string DealerName { get; set; } = "";

	[Required(ErrorMessage = "Dealer ID is required")]
	[StringLength(50, ErrorMessage = "Dealer ID cannot exceed 50 characters")]
	public string DealerId { get; set; } = "";

	[Required(ErrorMessage = "Tax ID is required")]
	[StringLength(50, ErrorMessage = "Tax ID cannot exceed 50 characters")]
	public string TaxId { get; set; } = "";

	[Required(ErrorMessage = "State is required")]
	public int StateId { get; set; }

	public bool Subsidary { get; set; }

	[Required(ErrorMessage = "Address is required")]
	[StringLength(150, ErrorMessage = "Address cannot exceed 150 characters")]
	public string Address { get; set; } = "";

	[StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
	public string Phone { get; set; } = "";

	[Required(ErrorMessage = "Authorized company is required")]
	[StringLength(100, ErrorMessage = "Authorized company cannot exceed 100 characters")]
	public string AuthorizedCompany { get; set; } = "";

	[Required(ErrorMessage = "Administrative email is required")]
	[EmailAddress(ErrorMessage = "Please enter a valid email address")]
	[StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
	public string AdministrativeEmail { get; set; } = "";

	[StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
	public string AdministrativePhone { get; set; } = "";

	[Required(ErrorMessage = "Dealership lot type is required")]
	public DEALERSHIP_IOT_TYPES DealershipIOTTypes { get; set; }

	[Required(ErrorMessage = "Number of vehicles on lot is required")]
	public VEHICAL_ON_IOT_TYPES VehicalOnIOTTypes { get; set; }

	[Required(ErrorMessage = "Average vehicles sold per month is required")]
	public VEHICAL_SOLD_IOT VehicalSoldIOT { get; set; }
}
