﻿@page "/dashboard/dealer"
@layout MainLayout
@attribute [Authorize(Roles = "Dealer")]

@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Razor.Features.DealerDashboard;
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.ServiceContracts.Features.DealerDashboard;
@inherits ListingBase<DealerDashboardListingViewModel,DealerDashboardListingBusinessObject,DealerDashboardFilterViewModel,DealerDashboardFilterBusinessObject, IDealerDashboardListingDataService>

@*<button  class="btn btn-primary" type="button" @onclick='()=> ShowCenterDialog<DealerDashboardFormComponent>(default(long), "Create New DealerDashboard")'>Add New</button>*@

@if(Items == null)
{}
else
{
if(Items.Count() == 0)
{}
else
{
@foreach(var item in Items)
{
	<!-- Add item layout here -->
}
<EditForm Model="PaginationStrip">
	 @*<PaginationStrip @bind-Value="PaginationStrip" TotalPages="TotalPages" TotalRows="TotalRows"></PaginationStrip> *@
</EditForm>
}
}

