﻿using PremierAutoTag.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.ServiceContracts.Features.Contact;
public class ContactFormBusinessObject
{
	public long Id { get; set; }

	[Required(ErrorMessage = "Email is required")]
	[EmailAddress(ErrorMessage = "Please enter a valid email address")]
	[StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
	public string Email { get; set; } = "";

	[Required(ErrorMessage = "Phone number is required")]
	[StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
	public string PhoneNumber { get; set; } = "";

	[Required(ErrorMessage = "Subject is required")]
	[StringLength(200, ErrorMessage = "Subject cannot exceed 200 characters")]
	public string Subject { get; set; } = "";

	[Required(ErrorMessage = "Message is required")]
	[StringLength(2000, ErrorMessage = "Message cannot exceed 2000 characters")]
	public string Message { get; set; } = "";

	[StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
	public string? Name { get; set; }
}
