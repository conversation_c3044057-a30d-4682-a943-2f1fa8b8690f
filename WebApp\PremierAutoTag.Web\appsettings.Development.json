{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "data source=localhost\\MSSQLSERVER22;initial catalog=AutoTagDB;User ID=sa;Password=******;MultipleActiveResultSets=True;connection timeout=300;App=EntityFramework; Integrated Security=true;Trusted_Connection=True;TrustServerCertificate=True;"}, "EmailSettings": {"ApiKey": "endpoint=https://epccommunicationservice.uae.communication.azure.com/;accesskey=CgvAVP2m6dKmgu9vEJHxd94Me1KrAt31J4YDPHcb14OXGqh34ioIJQQJ99AGACULyCpwpOZ0AAAAAZCSr0RD", "DefaultEmail": "<EMAIL>"}, "SecureDataSetting": {"DataProtectionKey": "auto@123321"}, "Development": {"ShowEmailVerificationLinks": true}}