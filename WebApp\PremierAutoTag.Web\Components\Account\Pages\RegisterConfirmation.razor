﻿@page "/Account/RegisterConfirmation"
@layout WebsiteLayout

@using System.Text
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using Microsoft.Extensions.Configuration
@using PremierAutoTag.Server.Data.Entities
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.Framework.Core.DataProtections
@using PremierAutoTag.ServiceContracts.Services

@inject UserManager<ApplicationUser> UserManager
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject IConfiguration Configuration
@inject IDataProtection DataProtection
@inject IEmailVerificationService EmailVerificationService

<PageTitle>Email Verification Required</PageTitle>

<section class="">
    <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
        <div class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
                    <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>

                <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">
                    @if (isDealerRegistration)
                    {
                        <text>Verify Your Email to Continue</text>
                    }
                    else
                    {
                        <text>Check Your Email</text>
                    }
                </h1>

                <h2 class="text-gray-600 text-sm md:text-base text-center mb-6">
                    @if (isDealerRegistration)
                    {
                        <text>We've sent a verification email to <strong class="text-gray-800">@decodedEmail</strong>. Please click the verification link in the email to continue with your dealer registration process.</text>
                    }
                    else
                    {
                        <text>We've sent a verification email to <strong class="text-gray-800">@decodedEmail</strong>. Please click the verification link in the email to activate your AutoTag account.</text>
                    }
                </h2>
            </div>

            @if (!string.IsNullOrEmpty(statusMessage))
            {
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p class="text-red-800 text-sm">@statusMessage</p>
                </div>
            }

            @if (emailConfirmationLink is not null)
            {
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                    <p class="text-yellow-800 text-sm mb-3">
                        <strong>Development Mode:</strong> Email service not configured.
                    </p>
                    <a href="@emailConfirmationLink"
                       class="w-full flex justify-center py-2 md:py-2.5 px-4 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 font-medium text-sm md:text-base rounded-lg border border-yellow-300 focus:ring-1 focus:ring-yellow-300 outline-none transition-colors">
                        Click here to verify your email
                    </a>
                </div>
            }

            <div class="flex flex-col gap-3 md:gap-4">
                <div class="text-center">
                    <p class="text-gray-600 text-sm md:text-base">Didn't receive the email? Check your spam folder or</p>
                </div>

                <a href="/Account/ResendEmailConfirmation?data=@(DataProtection.Encode(decodedEmail ?? ""))"
                   class="w-full flex justify-center py-2 md:py-2.5 px-4 bg-white hover:bg-gray-50 text-gray-700 font-medium text-sm md:text-base rounded-lg border border-gray-300 focus:ring-1 focus:ring-gray-300 outline-none transition-colors">
                    Resend Verification Email
                </a>

                <a href="/Account/Login"
                   class="w-full flex justify-center py-2 md:py-2.5 px-4 bg-primary hover:bg-primaryDark text-white font-medium text-sm md:text-base rounded-lg focus:ring-1 focus:ring-primary outline-none transition-colors">
                    Back to Login
                </a>
            </div>

            @if (isDealerRegistration)
            {
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p class="text-blue-800 text-sm md:text-base">
                        <strong>Next Steps:</strong> After verifying your email, you'll be able to continue with Step 2 of the dealer registration process to select your subscription plan.
                    </p>
                </div>
            }
        </div>
    </div>
</section>

@code {
    private string? emailConfirmationLink;
    private string? statusMessage;
    private bool isDealerRegistration = false;
    private string? decodedEmail;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromQuery]
    private string? Data { get; set; }

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            if (string.IsNullOrEmpty(Data))
            {
                RedirectManager.RedirectTo("");
                return;
            }

            // Decode the protected data
            var decodedData = DataProtection.Decode(Data);
            var dataParts = decodedData.Split('|');

            if (dataParts.Length < 1)
            {
                RedirectManager.RedirectTo("");
                return;
            }

            decodedEmail = dataParts[0];

            // Check if this is dealer registration (optional second parameter)
            isDealerRegistration = dataParts.Length > 1 &&
                                  dataParts[1].Equals("dealer", StringComparison.OrdinalIgnoreCase);

            var user = await UserManager.FindByEmailAsync(decodedEmail);
            if (user is null)
            {
                HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
                statusMessage = "Error finding user for unspecified email";
            }
            else if (Configuration.GetValue<bool>("Development:ShowEmailVerificationLinks", false))
            {
                // Development mode: Show direct verification link
                var userId = await UserManager.GetUserIdAsync(user);

                if (isDealerRegistration)
                {
                    // For dealers, use the new encoded format with step information
                    var code = await EmailVerificationService.GenerateDealerEmailVerificationTokenAsync(userId, 1);
                    emailConfirmationLink = NavigationManager.GetUriWithQueryParameters(
                        NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
                        new Dictionary<string, object?> { ["code"] = code });
                }
                else
                {
                    // For regular users, use the old format
                    var code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
                    code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                    emailConfirmationLink = NavigationManager.GetUriWithQueryParameters(
                        NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
                        new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code, ["returnUrl"] = ReturnUrl });
                }
            }
        }
        catch (Exception)
        {
            // If decoding fails or any error occurs, redirect to home
            RedirectManager.RedirectTo("");
        }
    }
}
