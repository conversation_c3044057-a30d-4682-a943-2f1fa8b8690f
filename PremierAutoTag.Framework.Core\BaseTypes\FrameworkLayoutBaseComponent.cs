﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using PremierAutoTag.Framework.Core.UIServices;
using System.Security.Claims;

namespace PremierAutoTag.Framework.Core
{
    public class FrameworkLayoutBaseComponent : LayoutComponentBase
    {
        [Inject]
        public KtDialogService? DialogService { get; set; }

        [Inject]
        public IMessageCenter? MessageCenter { get; set; }

        [Inject]
        public IJSRuntime? JsRuntime { get; set; }

        [CascadingParameter]
        public ClaimsPrincipal? ClaimsPrinciple
        {
            get
            {
                return _user;
            }
            set
            {
                _user = value;
                if (AuthenticatedUser != null && _user != null)
                {
                    AuthenticatedUser.UserId = _user.GetUserId();
                    AuthenticatedUser.ProfileName = _user.GetProfileName();
                    AuthenticatedUser.Phone = _user.GetPhone();
                    AuthenticatedUser.Email = _user.GetEmail();
                    AuthenticatedUser.Username = _user.GetUserName();
                }
            }
        }

        private ClaimsPrincipal? _user;

        [Inject]
        public IAuthenticatedUser? AuthenticatedUser { get; set; }

        [Inject]
        public ILocalStorageService? StorageService { get; set; }
        [Inject]
        public AuthenticationStateProvider? AuthStateProvider { get; set; }

        public int TimeZoneOffset { get; set; } = 0;

        protected override async Task OnInitializedAsync()
        {
            if (AuthStateProvider != null)
            {

                var state = await AuthStateProvider.GetAuthenticationStateAsync();
                if (state != null)
                {
                    if (state.User.Identity?.IsAuthenticated == true)
                    {
                        ClaimsPrinciple = state.User;

                    }
                }

            }

            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && JsRuntime is not null)
            {
                var timezoneOffset = await JsRuntime.InvokeAsync<string>("getCookie", "timezoneoffset");
                if (!string.IsNullOrEmpty(timezoneOffset) && double.TryParse(timezoneOffset, out double offset))
                {
                    TimeZoneOffset = (int)offset;
                }
            }
        }



        public virtual void DialogService_OnClose(dynamic obj)
        {
            Console.WriteLine("Dialog closed");
        }

        public async Task ClientLog(string Text)
        {
            if (JsRuntime != null)
                await JsRuntime.InvokeVoidAsync("browserLog", Text);
        }
    }
}
