﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Communication.Email" Version="1.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Stripe.net" Version="48.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PremierAutoTag.Framework.Core\PremierAutoTag.Framework.Core.csproj" />
    <ProjectReference Include="..\PremierAutoTag.Server.Data\PremierAutoTag.Server.Data.csproj" />
    <ProjectReference Include="..\PremierAutoTag.ServiceContracts\PremierAutoTag.ServiceContracts.csproj" />
  </ItemGroup>

</Project>
