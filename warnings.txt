Build started at 2:51 AM...
Restored E:\Projects\AutoTag\PremierAutoTag.Razor\PremierAutoTag.Razor.csproj (in 77 ms).
Restored E:\Projects\AutoTag\PremierAutoTag.Framework.Core\PremierAutoTag.Framework.Core.csproj (in 9 ms).
Restored E:\Projects\AutoTag\PremierAutoTag.ServiceContracts\PremierAutoTag.ServiceContracts.csproj (in 276 ms).
1>------ Build started: Project: PremierAutoTag.Framework.Core, Configuration: Debug Any CPU ------
Restored E:\Projects\AutoTag\PremierAutoTag.Server.Data\PremierAutoTag.Server.Data.csproj (in 225 ms).
Restored E:\Projects\AutoTag\PremierAutoTag.Server.DataServices\PremierAutoTag.Server.DataServices.csproj (in 170 ms).
Restored E:\Projects\AutoTag\WebApp\PremierAutoTag.Web\PremierAutoTag.Web.csproj (in 316 ms).
Restored E:\Projects\AutoTag\WebApp\PremierAutoTag.Web.Client\PremierAutoTag.Web.Client.csproj (in 347 ms).
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Attributes\RequiredIf.cs(15,41,15,48): warning CS8765: Nullability of type of parameter 'value' doesn't match overridden member (possibly because of nullability attributes).
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Attributes\ValidatePhone.cs(8,30,8,37): warning CS8765: Nullability of type of parameter 'value' doesn't match overridden member (possibly because of nullability attributes).
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(47,23,47,34): warning CS0108: 'FormBase<TFormModel, TFormViewModel, TKey, TService>.OperationId' hides inherited member 'FrameworkBaseComponent.OperationId'. Use the new keyword if hiding was intended.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(314,50,314,65): warning CS8612: Nullability of reference types in type of 'event PropertyChangedEventHandler ListingBase<TListViewModel, TListBusinessObject, TFilterViewModel, TFilterBusinessObject, TService>.PropertyChanged' doesn't match implicitly implemented member 'event PropertyChangedEventHandler? INotifyPropertyChanged.PropertyChanged'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ObservableBase.cs(19,50,19,65): warning CS8612: Nullability of reference types in type of 'event PropertyChangedEventHandler ObservableBase.PropertyChanged' doesn't match implicitly implemented member 'event PropertyChangedEventHandler? INotifyPropertyChanged.PropertyChanged'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ObservableBase.cs(11,99,11,103): warning CS8625: Cannot convert null literal to non-nullable reference type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\InputDateTime.cs(25,35,25,54): warning CS8765: Nullability of type of parameter 'value' doesn't match overridden member (possibly because of nullability attributes).
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\InputDateTime.cs(39,33,39,56): warning CS8765: Nullability of type of parameter 'value' doesn't match overridden member (possibly because of nullability attributes).
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\StringExtensions.cs(66,74,66,78): warning CS8625: Cannot convert null literal to non-nullable reference type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\StringExtensions.cs(66,95,66,99): warning CS8625: Cannot convert null literal to non-nullable reference type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\ViewModels\SelectListItem.cs(105,50,105,65): warning CS8612: Nullability of reference types in type of 'event PropertyChangedEventHandler SelectListItem.PropertyChanged' doesn't match implicitly implemented member 'event PropertyChangedEventHandler? INotifyPropertyChanged.PropertyChanged'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\ViewModels\SelectListItem.cs(8,16,8,30): warning CS8618: Non-nullable event 'PropertyChanged' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the event as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\ViewModels\SelectListItem.cs(12,16,12,30): warning CS8618: Non-nullable event 'PropertyChanged' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the event as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\ViewModels\SelectListItem.cs(17,16,17,30): warning CS8618: Non-nullable event 'PropertyChanged' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the event as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\ViewModels\SelectListItem.cs(24,16,24,30): warning CS8618: Non-nullable event 'PropertyChanged' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the event as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\ViewModels\SelectListItem.cs(32,16,32,30): warning CS8618: Non-nullable event 'PropertyChanged' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the event as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\ViewModels\SelectListItem.cs(40,16,40,30): warning CS8618: Non-nullable event 'PropertyChanged' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the event as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Attributes\ValidatePhone.cs(13,41,13,53): warning CS8604: Possible null reference argument for parameter 'phoneNumberToValidate' in 'bool ValidatePhone.IsValidPhoneNumber(string phoneNumberToValidate)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Attributes\RequiredIf.cs(31,16,31,40): warning CS8603: Possible null reference return.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FrameworkLayoutBaseComponent.cs(32,48,32,53): warning CS8604: Possible null reference argument for parameter 'principal' in 'string ClaimsPrincipleExtensions.GetUserId(ClaimsPrincipal principal)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(74,16,74,24): warning CS8618: Non-nullable property 'Position' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(74,16,74,24): warning CS8618: Non-nullable property 'OperationId' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(74,16,74,24): warning CS8618: Non-nullable property 'Logger' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(121,20,121,70): warning CS8619: Nullability of reference types in value of type 'Task<TFormViewModel>' doesn't match target type 'Task<TFormViewModel?>'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FrameworkLayoutBaseComponent.cs(61,25,61,44): warning CS8602: Dereference of a possibly null reference.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(170,48,170,50): warning CS0168: The variable 'ex' is declared but never used
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\CustomAuthStateProvider.cs(37,68,37,111): warning CS8604: Possible null reference argument for parameter 'value' in 'Claim.Claim(string type, string value)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FrameworkLayoutBaseComponent.cs(94,19,94,28): warning CS8604: Possible null reference argument for parameter 'jsRuntime' in 'ValueTask JSRuntimeExtensions.InvokeVoidAsync(IJSRuntime jsRuntime, string identifier, params object?[]? args)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(212,45,212,54): warning CS8604: Possible null reference argument for parameter 'jsRuntime' in 'Task FormBase<TFormModel, TFormViewModel, TKey, TService>.InitialzeJsScrips(IJSRuntime jsRuntime)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FrameworkBaseComponent.cs(132,89,132,99): warning CS8602: Dereference of a possibly null reference.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\CustomAuthStateProvider.cs(74,39,74,50): warning CS8602: Dereference of a possibly null reference.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\CustomAuthStateProvider.cs(74,39,74,77): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\CustomAuthStateProvider.cs(77,38,77,46): warning CS8602: Dereference of a possibly null reference.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\CustomAuthStateProvider.cs(77,38,77,76): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\CustomAuthStateProvider.cs(86,47,86,55): warning CS8604: Possible null reference argument for parameter 'identity' in 'ClaimsPrincipal.ClaimsPrincipal(IIdentity identity)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\CustomAuthStateProvider.cs(91,40,91,51): warning CS8604: Possible null reference argument for parameter 'user' in 'AuthenticationState.AuthenticationState(ClaimsPrincipal user)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FrameworkBaseComponent.cs(163,47,163,52): warning CS8604: Possible null reference argument for parameter 'title' in 'void FrameworkBaseComponent.ShowDialog(Type dialogType, string title, object? id, Size dialogSize = Size.Xl, Position_ position = Position_.Right, bool showCrossIcon = true, params List<KeyValuePair<string, object?>> parameters)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\IAuthenticatedUser.cs(35,20,35,32): warning CS8603: Possible null reference return.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(326,23,326,27): warning CS8602: Dereference of a possibly null reference.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(335,44,335,55): warning CS8602: Dereference of a possibly null reference.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(335,44,335,58): warning CS8602: Dereference of a possibly null reference.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\Result.cs(8,16,8,22): warning CS8618: Non-nullable property 'Errors' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\Result.cs(93,23,93,27): warning CS8618: Non-nullable property 'Type' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\Result.cs(96,23,96,28): warning CS8618: Non-nullable property 'Title' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\Result.cs(102,23,102,30): warning CS8618: Non-nullable property 'TraceId' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ObservableBase.cs(19,50,19,65): warning CS8618: Non-nullable event 'PropertyChanged' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the event as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\Result.cs(108,16,108,22): warning CS8618: Non-nullable property 'Data' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\StringExtensions.cs(19,27,19,52): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\StringExtensions.cs(22,35,22,54): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\StringExtensions.cs(26,28,26,117): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\StringExtensions.cs(33,20,33,24): warning CS8603: Possible null reference return.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(136,16,136,27): warning CS8618: Non-nullable field '_sortColumn' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(136,16,136,27): warning CS8618: Non-nullable property 'ServiceList' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(136,16,136,27): warning CS8618: Non-nullable field '_items' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(136,16,136,27): warning CS8618: Non-nullable property 'ApplicationState' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Helpers\EnumHelper.cs(17,31,17,81): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Helpers\EnumHelper.cs(20,46,20,51): warning CS8604: Possible null reference argument for parameter 'element' in 'DescriptionAttribute? CustomAttributeExtensions.GetCustomAttribute<DescriptionAttribute>(MemberInfo element)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Helpers\EnumHelper.cs(20,46,20,94): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Helpers\EnumHelper.cs(28,31,28,81): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Helpers\EnumHelper.cs(31,41,31,46): warning CS8604: Possible null reference argument for parameter 'element' in 'UIHintAttribute? CustomAttributeExtensions.GetCustomAttribute<UIHintAttribute>(MemberInfo element)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Helpers\EnumHelper.cs(31,41,31,84): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Helpers\EnumHelper.cs(39,31,39,81): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Helpers\EnumHelper.cs(42,41,42,46): warning CS8604: Possible null reference argument for parameter 'element' in 'UIHintAttribute? CustomAttributeExtensions.GetCustomAttribute<UIHintAttribute>(MemberInfo element)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Helpers\EnumHelper.cs(42,41,42,84): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FrameworkBaseComponent.cs(299,17,299,30): warning CS8602: Dereference of a possibly null reference.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FrameworkBaseComponent.cs(293,30,293,41): warning CS1998: This async method lacks 'await' operators and will run synchronously. Consider using the 'await' operator to await non-blocking API calls, or 'await Task.Run(...)' to do CPU-bound work on a background thread.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(190,35,190,40): warning CS8604: Possible null reference argument for parameter 'scope' in 'Task ListingBase<TListViewModel, TListBusinessObject, TFilterViewModel, TFilterBusinessObject, TService>.LoadSelectLists(IServiceScope scope)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\PropertyExpressionExtensions.cs(22,20,22,69): warning CS8603: Possible null reference return.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\InputDateTime.cs(20,141,20,161): warning CS8604: Possible null reference argument for parameter 'existingValue' in 'EventCallback<ChangeEventArgs> EventCallbackFactoryBinderExtensions.CreateBinder<string>(EventCallbackFactory factory, object receiver, Action<string> setter, string existingValue, CultureInfo? culture = null)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\ObjectExtensions.cs(51,32,51,73): warning CS8600: Converting null literal or possible null value to non-nullable type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\UIServices\DialogService.cs(86,51,86,62): warning CS8618: Non-nullable event 'OnShowAlert' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the event as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\NavigationManagerExtensions.cs(13,53,13,73): warning CS8621: Nullability of reference types in return type of 'string[]? NameValueCollection.GetValues(string? name)' doesn't match the target delegate 'Func<string?, IEnumerable<string>>' (possibly because of nullability attributes).
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\UIServices\DialogService.cs(26,16,26,33): warning CS8618: Non-nullable property 'Title' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\UIServices\DialogService.cs(26,16,26,33): warning CS8618: Non-nullable property 'SizeClasses' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\UIServices\DialogService.cs(26,16,26,33): warning CS8618: Non-nullable property 'PositionClasses' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\UIServices\DialogService.cs(26,16,26,33): warning CS8618: Non-nullable property 'DialogContainerClasses' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\UIServices\DialogService.cs(26,16,26,33): warning CS8618: Non-nullable property 'Component' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\InputDateTime.cs(61,42,61,46): warning CS8625: Cannot convert null literal to non-nullable reference type.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\InputDateTime.cs(81,26,81,33): warning CS8601: Possible null reference assignment.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\InputDateTime.cs(96,26,96,33): warning CS8601: Possible null reference assignment.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(289,21,289,27): warning CS8604: Possible null reference argument for parameter 'logger' in 'void LoggerExtensions.LogError(ILogger logger, string? message, params object?[] args)'.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(281,52,281,54): warning CS0168: The variable 'ex' is declared but never used
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(323,25,323,37): warning CS8602: Dereference of a possibly null reference.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\NavigationManagerExtensions.cs(69,53,69,73): warning CS8621: Nullability of reference types in return type of 'string[]? NameValueCollection.GetValues(string? name)' doesn't match the target delegate 'Func<string?, IEnumerable<string>>' (possibly because of nullability attributes).
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\NavigationManagerExtensions.cs(69,106,69,107): warning CS8601: Possible null reference assignment.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\NavigationManagerExtensions.cs(83,27,83,30): warning CS8618: Non-nullable property 'Key' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\Extensions\NavigationManagerExtensions.cs(84,27,84,32): warning CS8618: Non-nullable property 'Value' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\ListingBase.cs(98,54,98,76): warning CS0169: The field 'ListingBase<TListViewModel, TListBusinessObject, TFilterViewModel, TFilterBusinessObject, TService>.persistingSubscription' is never used
1>E:\Projects\AutoTag\PremierAutoTag.Framework.Core\BaseTypes\FormBase.cs(23,24,23,28): warning BL0007: Component parameter 'PremierAutoTag.Framework.Core.FormBase<TFormModel, TFormViewModel, TKey, TService>.Args' should be auto property
1>PremierAutoTag.Framework.Core -> E:\Projects\AutoTag\PremierAutoTag.Framework.Core\bin\Debug\net9.0\PremierAutoTag.Framework.Core.dll
1>Done building project "PremierAutoTag.Framework.Core.csproj".
2>------ Build started: Project: PremierAutoTag.Server.Data, Configuration: Debug Any CPU ------
3>------ Build started: Project: PremierAutoTag.ServiceContracts, Configuration: Debug Any CPU ------
2>E:\Projects\AutoTag\PremierAutoTag.Server.Data\Entities\UserProfile.cs(12,23,12,25): warning CS8618: Non-nullable property 'Id' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
2>E:\Projects\AutoTag\PremierAutoTag.Server.Data\Entities\UserProfile.cs(21,23,21,33): warning CS8618: Non-nullable property 'DealerName' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
2>E:\Projects\AutoTag\PremierAutoTag.Server.Data\Entities\UserProfile.cs(24,23,24,31): warning CS8618: Non-nullable property 'DealerId' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
2>E:\Projects\AutoTag\PremierAutoTag.Server.Data\Entities\UserProfile.cs(27,23,27,28): warning CS8618: Non-nullable property 'TaxId' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
2>E:\Projects\AutoTag\PremierAutoTag.Server.Data\Entities\UserProfile.cs(59,40,59,55): warning CS8618: Non-nullable property 'ApplicationUser' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable.
2>E:\Projects\AutoTag\PremierAutoTag.Server.Data\DB\ApplicationDbContext.cs(40,48,40,67): warning CS0169: The field 'ApplicationDbContext.authenticatedUserId' is never used
2>E:\Projects\AutoTag\PremierAutoTag.Server.Data\DB\ApplicationDbContext.cs(35,22,35,35): warning EF1002: Method 'ExecuteSqlRaw' inserts interpolated strings directly into the SQL, without any protection against SQL injection. Consider using 'ExecuteSql' instead, which protects against SQL injection, or make sure that the value is sanitized and suppress the warning.
2>PremierAutoTag.Server.Data -> E:\Projects\AutoTag\PremierAutoTag.Server.Data\bin\Debug\net9.0\PremierAutoTag.Server.Data.dll
2>Done building project "PremierAutoTag.Server.Data.csproj".
3>PremierAutoTag.ServiceContracts -> E:\Projects\AutoTag\PremierAutoTag.ServiceContracts\bin\Debug\net9.0\PremierAutoTag.ServiceContracts.dll
4>------ Build started: Project: PremierAutoTag.Server.DataServices, Configuration: Debug Any CPU ------
5>------ Build started: Project: PremierAutoTag.Razor, Configuration: Debug Any CPU ------
4>E:\Projects\AutoTag\PremierAutoTag.Server.DataServices\Base\ServerAuthenticatedUser.cs(35,33,35,36): warning CS8766: Nullability of reference types in return type of 'string? ServerAuthenticatedUser.UserId.get' doesn't match implicitly implemented member 'string IAuthenticatedUser.UserId.get' (possibly because of nullability attributes).
4>E:\Projects\AutoTag\PremierAutoTag.Server.DataServices\Base\ServerAuthenticatedUser.cs(36,38,36,41): warning CS8766: Nullability of reference types in return type of 'string? ServerAuthenticatedUser.ProfileName.get' doesn't match implicitly implemented member 'string IAuthenticatedUser.ProfileName.get' (possibly because of nullability attributes).
4>E:\Projects\AutoTag\PremierAutoTag.Server.DataServices\Base\ServerAuthenticatedUser.cs(37,35,37,38): warning CS8766: Nullability of reference types in return type of 'string? ServerAuthenticatedUser.ImageUrl.get' doesn't match implicitly implemented member 'string IAuthenticatedUser.ImageUrl.get' (possibly because of nullability attributes).
4>E:\Projects\AutoTag\PremierAutoTag.Server.DataServices\Base\ServerAuthenticatedUser.cs(46,33,46,83): warning CS8603: Possible null reference return.
4>E:\Projects\AutoTag\PremierAutoTag.Server.DataServices\Base\ServerAuthenticatedUser.cs(12,16,12,39): warning CS8618: Non-nullable field '_httpContextAccessor' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the field as nullable.
4>E:\Projects\AutoTag\PremierAutoTag.Server.DataServices\Features\Subscriptions\Form\SubscriptionPlanServerSideFormDataService.cs(48,14,49,77): warning CS8600: Converting null literal or possible null value to non-nullable type.
5>PremierAutoTag.Razor -> E:\Projects\AutoTag\PremierAutoTag.Razor\bin\Debug\net9.0\PremierAutoTag.Razor.dll
6>------ Build started: Project: PremierAutoTag.Web.Client, Configuration: Debug Any CPU ------
4>PremierAutoTag.Server.DataServices -> E:\Projects\AutoTag\PremierAutoTag.Server.DataServices\bin\Debug\net9.0\PremierAutoTag.Server.DataServices.dll
4>Done building project "PremierAutoTag.Server.DataServices.csproj".
6>PremierAutoTag.Web.Client -> E:\Projects\AutoTag\WebApp\PremierAutoTag.Web.Client\bin\Debug\net9.0\PremierAutoTag.Web.Client.dll
6>PremierAutoTag.Web.Client (Blazor output) -> E:\Projects\AutoTag\WebApp\PremierAutoTag.Web.Client\bin\Debug\net9.0\wwwroot
7>------ Build started: Project: PremierAutoTag.Web, Configuration: Debug Any CPU ------
7>PremierAutoTag.Web -> E:\Projects\AutoTag\WebApp\PremierAutoTag.Web\bin\Debug\net9.0\PremierAutoTag.Web.dll
========== Build: 7 succeeded, 0 failed, 0 up-to-date, 0 skipped ==========
========== Build completed at 2:51 AM and took 29.012 seconds ==========
