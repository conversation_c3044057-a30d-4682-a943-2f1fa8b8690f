<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link href="./css/output.css" rel="stylesheet">
</head>

<body>
    <header class="bg-white w-full fixed top-0 border-b border-b-gray-m-200 z-50">
        <nav class="flex items-center justify-between md:px-12 lg:px-16  p-4 mx-auto ">

            <div class="flex ">
                <a class="-m-1.5 p-1.5" href="/html/premerauto/index.html"><span class="sr-only">Your Company</span>
                    <img alt="" loading="lazy" decoding="async" class="h-9 lg:h-14" src="images/logo.png">
                </a>
            </div>

            <ul class="items-center  justify-center flex-grow hidden gap-2 xl:gap-6 lg:flex ">
                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="">Home</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="aboutus.html">About us</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="locations.html">Locations/Hours</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="FAQ.html">FAQ</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="tips.html">DMV Tips</a>
                </li>
                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="contact.html">Contact</a>
                </li>
            </ul>

            <div class="flex items-center gap-3">
                <div class="flex items-center gap-3">
                    <a href="login.html"
                        class="bg-white  py-1.5 md:py-2.5 px-3 md:px-4 hover:bg-gray-50 text-gray-900 text-sm md:text-sm font-medium hover:border hover:border-gray-300 border border-transparent   rounded-lg">Log
                        In</a>
                    <a href="register.html" class="rounded-lg text-white bg-primary hover:bg-primaryDark px-5 py-2.5">
                        Register
                    </a>
                </div>
                <div class="flex lg:hidden"><button id="mobileMenuBtn" type="button"
                        class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"><span
                            class="sr-only">Open main menu</span><svg class="w-6 h-6" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
                        </svg></button></div>
            </div>
        </nav>
        <div class="hidden lg:hidden" id="mobileMenu" role="dialog" aria-modal="true">
            <div class="fixed inset-0 z-10 hidden bg-white opacity-50"></div>
            <div
                class="fixed inset-y-0 right-0 z-20 w-full p-4 overflow-y-auto bg-white md:px-6 md:py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
                <div class="flex items-center justify-between pt-1">
                    <a href="#" class="">
                        <span class="sr-only">Your
                            Company</span>
                        <img alt="" loading="lazy" width="194" height="50" decoding="async" class="w-auto h-9"
                            style="color:transparent" src="images/logo.png">
                    </a>
                    <button type="button" id="mobileMenuBtn2" class=" -m-2.5 rounded-md p-2.5 text-gray-700"><span
                            class="sr-only">Close
                            menu</span><svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                        </svg></button>
                </div>
                <div class="flow-root mt-6">
                    <div class="-my-6 divide-y divide-gray-500/10">
                        <div class="py-6 space-y-1">
                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="/">Home</a>
                            <a href="aboutus.html"
                                class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100">About
                                Us</a>
                            <a href="locations.html"
                                class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100">Locations/Hours</a>
                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="FAQ.html">FAQ</a>
                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="tips.html">DMV Tips</a>


                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="contactUs.html">Contact</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <session class="">
        <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
            <form
                class="bg-white max-w-2xl w-full flex flex-col gap-3  md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">

                <div>
                    <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Create your account</h1>
                    <h2 class="text-gray-600 text-sm md:text-base text-center">Please fill the below fields to create
                        your account
                    </h2>
                </div>
                <div class="flex flex-col md:flex-row items-center gap-3 md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">First Name</label>
                        <input value="" type="text"
                            class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                            placeholder="First Name" />
                    </div>
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">Last Name</label>
                        <input value="" type="text"
                            class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                            placeholder="Last Name" />
                    </div>
                </div>

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">Email</label>
                    <input value="" type="text"
                        class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                        placeholder="Your Email" />
                </div>

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">Password</label>
                    <input value="" type="password"
                        class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                        placeholder="••••••••" />
                </div>

                <div class="flex flex-col gap-2">
                    <div class="flex items-center gap-2">
                        <image src="images/checkIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                        <image src="images/checkGreenIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                        <span class="text-xs md:text-sm text-vampireGray block">Must be at least 8 characters</span>
                    </div>

                    <div class="flex items-center gap-2">
                        <image src="images/checkIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                        <image src="images/checkGreenIcon.svg" class="w-4 md:w-5 md:h-5 h-4" />
                        <span class="text-xs md:text-sm text-vampireGray block">Must contain one special
                            character</span>
                    </div>
                </div>

                <a href="dealerRegisterAccount.html"
                    class="w-full py-2 md:py-2.5 flex items-center gap-2 justify-center rounded-lg bg-primary cursor-pointer group hover:bg-primaryDark text-white text-sm md:text-base font-semibold"
                    type="submit">
                    Continue <span class="group-hover:transition duration-300 group-hover:translate-x-1"><svg width="16"
                            height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M15.913 8.45783C15.9995 8.23906 16.0222 7.99833 15.978 7.76609C15.9339 7.53386 15.8249 7.32055 15.665 7.15317L11.0933 2.36541C10.9878 2.25109 10.8617 2.15991 10.7223 2.09718C10.5828 2.03445 10.4329 2.00143 10.2811 2.00005C10.1294 1.99866 9.97885 2.02895 9.83839 2.08913C9.69793 2.14931 9.57032 2.23819 9.463 2.35057C9.35569 2.46296 9.27083 2.5966 9.21336 2.7437C9.15589 2.8908 9.12697 3.04841 9.12829 3.20734C9.12961 3.36627 9.16114 3.52333 9.22104 3.66936C9.28094 3.81539 9.36801 3.94747 9.47717 4.05788L12.0991 6.80366H1.14293C0.839806 6.80366 0.549097 6.92977 0.334756 7.15423C0.120416 7.3787 0 7.68315 0 8.0006C0 8.31805 0.120416 8.62249 0.334756 8.84696C0.549097 9.07143 0.839806 9.19754 1.14293 9.19754H12.0991L9.47831 11.9421C9.36915 12.0525 9.28208 12.1846 9.22218 12.3306C9.16228 12.4767 9.13075 12.6337 9.12943 12.7927C9.12811 12.9516 9.15703 13.1092 9.2145 13.2563C9.27197 13.4034 9.35683 13.537 9.46415 13.6494C9.57146 13.7618 9.69907 13.8507 9.83953 13.9109C9.97999 13.9711 10.1305 14.0013 10.2823 14C10.434 13.9986 10.584 13.9656 10.7234 13.9028C10.8629 13.8401 10.989 13.7489 11.0944 13.6346L15.6661 8.84683C15.772 8.7354 15.8559 8.60322 15.913 8.45783V8.45783Z"
                                fill="white" />
                        </svg>
                    </span>
                </a>

                <div class="flex justify-center gap-1">
                    <span class="text-sm text-gray-600">Already have an account?</span>
                    <a href="login.html"
                        class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4 ">Log
                        In</a>
                </div>

            </form>
        </div>
    </session>




</body>
<script>
    const Button = document.getElementById('mobileMenuBtn');
    const menu = document.getElementById('mobileMenu');
    const Button2 = document.getElementById('mobileMenuBtn2');

    Button.addEventListener('click', () => {
        menu.classList.toggle('hidden');
    });

    Button2.addEventListener('click', () => {
        menu.classList.toggle('hidden');
    });


    window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 80) {
            header.classList.add('!bg-white', 'shadow-xl', "fixed");
            header.classList.remove('absolute');
        } else {
            header.classList.remove('!bg-white', 'shadow-xl', 'fixed');
            header.classList.add('absolute');
        }
    });

</script>

</html>