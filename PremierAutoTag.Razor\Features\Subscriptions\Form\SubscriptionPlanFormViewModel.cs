﻿using PremierAutoTag.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace PremierAutoTag.Razor.Features.SubscriptionPlan;
public class SubscriptionPlanFormViewModel : ObservableBase
{
	private long _id;
	private string _name = "";
	private string? _description;
	private decimal _annualPrice;
	private int _maxVehicles = 1;
	private int _maxUsers = 1;
	private int _standAloneQuotesDiscountPercent;
	private int _registrationTitleServicesDiscountPercent;
	private bool _hasDirectSubmission;
	private bool _hasDedicatedClientManager;
	private string? _features;
	private bool _isPopular;
	private int _displayOrder;
	private bool _isActive = true;

	public long Id
	{
		get => _id;
		set => SetField(ref _id, value);
	}

	[Required(ErrorMessage = "Plan name is required")]
	[StringLength(100, ErrorMessage = "Plan name cannot exceed 100 characters")]
	public string Name
	{
		get => _name;
		set => SetField(ref _name, value);
	}

	[StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
	public string? Description
	{
		get => _description;
		set => SetField(ref _description, value);
	}

	[Required(ErrorMessage = "Annual price is required")]
	[Range(0, 999999.99, ErrorMessage = "Annual price must be between 0 and 999,999.99")]
	public decimal AnnualPrice
	{
		get => _annualPrice;
		set => SetField(ref _annualPrice, value);
	}

	[Required(ErrorMessage = "Maximum vehicles is required")]
	[Range(1, int.MaxValue, ErrorMessage = "Maximum vehicles must be at least 1")]
	public int MaxVehicles
	{
		get => _maxVehicles;
		set => SetField(ref _maxVehicles, value);
	}

	[Required(ErrorMessage = "Maximum users is required")]
	[Range(1, int.MaxValue, ErrorMessage = "Maximum users must be at least 1")]
	public int MaxUsers
	{
		get => _maxUsers;
		set => SetField(ref _maxUsers, value);
	}

	[Range(0, 100, ErrorMessage = "Discount percentage must be between 0 and 100")]
	public int StandAloneQuotesDiscountPercent
	{
		get => _standAloneQuotesDiscountPercent;
		set => SetField(ref _standAloneQuotesDiscountPercent, value);
	}

	[Range(0, 100, ErrorMessage = "Discount percentage must be between 0 and 100")]
	public int RegistrationTitleServicesDiscountPercent
	{
		get => _registrationTitleServicesDiscountPercent;
		set => SetField(ref _registrationTitleServicesDiscountPercent, value);
	}

	public bool HasDirectSubmission
	{
		get => _hasDirectSubmission;
		set => SetField(ref _hasDirectSubmission, value);
	}

	public bool HasDedicatedClientManager
	{
		get => _hasDedicatedClientManager;
		set => SetField(ref _hasDedicatedClientManager, value);
	}

	[StringLength(1000, ErrorMessage = "Features cannot exceed 1000 characters")]
	public string? Features
	{
		get => _features;
		set => SetField(ref _features, value);
	}

	public bool IsPopular
	{
		get => _isPopular;
		set => SetField(ref _isPopular, value);
	}

	[Range(0, int.MaxValue, ErrorMessage = "Display order must be non-negative")]
	public int DisplayOrder
	{
		get => _displayOrder;
		set => SetField(ref _displayOrder, value);
	}

	public bool IsActive
	{
		get => _isActive;
		set => SetField(ref _isActive, value);
	}
}
