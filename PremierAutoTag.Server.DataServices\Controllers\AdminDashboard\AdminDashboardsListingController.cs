﻿using Microsoft.AspNetCore.Mvc;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.AdminDashboard;
namespace PremierAutoTag.Server.DataServices.Controller.AdminDashboard;
[ApiController, Route("api/[controller]/[action]")]
public class AdminDashboardsListingController : ControllerBase, IAdminDashboardListingDataService
{

	private readonly IAdminDashboardListingDataService dataService;

	public AdminDashboardsListingController(IAdminDashboardListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpGet]
	public async Task<PagedDataList<AdminDashboardListingBusinessObject>> GetPaginatedItems([FromQuery] AdminDashboardFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
