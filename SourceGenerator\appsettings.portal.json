{"Profiles": [{"Name": "PremierAutoTag", "FrameworkNamespaces": "PremierAutoTag.Framework.Core", "DbContextNamespaces": "PremierAutoTag.Server.Data", "Projects": [{"Id": 1, "Name": "Service Contracts", "Path": "PremierAutoTag.ServiceContracts\\Features", "NameSpace": "PremierAutoTag.ServiceContracts.Features", "ParentId": 0, "ProjectType": 2}, {"Id": 2, "Name": "Serverside Data Services", "Path": "PremierAutoTag.Server.DataServices\\Features", "NameSpace": "PremierAutoTag.Server.DataServices.Features", "ParentId": 1, "ProjectType": 3}, {"Id": 3, "Name": "Razor Library", "Path": "PremierAutoTag.Razor\\Features", "NameSpace": "PremierAutoTag.Razor.Features", "ParentId": 1, "ProjectType": 6}, {"Id": 4, "Name": "Controllers", "Path": "PremierAutoTag.Server.DataServices\\Controllers", "NameSpace": "PremierAutoTag.Server.DataServices.Controllers", "ParentId": 1, "ProjectType": 5}, {"Id": 5, "Name": "ClientDependency", "Path": "WebApp\\PremierAutoTag.Web.Client", "NameSpace": "", "ParentId": 1, "ProjectType": 7}, {"Id": 6, "Name": "ServerSideDependency", "Path": "WebApp\\PremierAutoTag.Web", "NameSpace": "", "ParentId": 1, "ProjectType": 8}]}], "ProjectFiles": {"ServiceContracts": [{"FileName": "Listing\\I##ComponentPrefix##ListingDataService.cs", "FileCategory": 1, "Content": ["using ##frameworkNamespace##;", "namespace ##Name##.ServiceContracts.Features.##moduleNamespace##;", "public interface I##ComponentPrefix##ListingDataService :", "\tIListingDataService<##ComponentPrefix##ListingBusinessObject, ##ComponentPrefix##FilterBusinessObject>", "{", "\t//Add any custom methods here", "}"]}, {"FileName": "Listing\\##ComponentPrefix##ListingBusinessObject.cs", "FileCategory": 1, "Content": ["using ##frameworkNamespace##;", "namespace ##Name##.ServiceContracts.Features.##moduleNamespace##;", "public class ##ComponentPrefix##ListingBusinessObject", "{", "\t//Add Listing BusinessObject Attributes here", "}"]}, {"FileName": "Listing\\##ComponentPrefix##FilterBusinessObject.cs", "FileCategory": 1, "Content": ["using ##frameworkNamespace##;", "namespace ##Name##.ServiceContracts.Features.##moduleNamespace##;", "public class ##ComponentPrefix##FilterBusinessObject :  BaseFilterBusinessObject", "{", "\t//Add Filter BusinessObject Attributes here", "}"]}, {"FileName": "Form\\I##ComponentPrefix##FormDataService.cs", "FileCategory": 2, "Content": ["using ##frameworkNamespace##;", "namespace ##Name##.ServiceContracts.Features.##moduleNamespace##;", "public interface I##ComponentPrefix##FormDataService :", "\tIFormDataService<##ComponentPrefix##FormBusinessObject, ##primaryKeyType##>", "{", "\t//Add any custom methods here", "}"]}, {"FileName": "Form\\##ComponentPrefix##FormBusinessObject.cs", "FileCategory": 2, "Content": ["using ##frameworkNamespace##;", "namespace ##Name##.ServiceContracts.Features.##moduleNamespace##;", "public class ##ComponentPrefix##FormBusinessObject", "{", "\t//Add Form BusinessObject Attributes here", "}"]}], "ServerSideServices": [{"FileName": "Listing\\##ComponentPrefix##ServerSideListingDataService.cs", "FileCategory": 1, "Content": ["using ##frameworkNamespace##;", "using ##dbContextNamespace##.DB;", "using ##Name##.ServiceContracts.Features.##moduleNamespace##;", "namespace ##Name##.Server.DataServices.Features.##moduleNamespace##;", "public class ##ComponentPrefix##ServerSideListingDataService : ServerSideListingDataService<##ComponentPrefix##ListingBusinessObject, ##ComponentPrefix##FilterBusinessObject>, I##ComponentPrefix##ListingDataService", "{", "", "\tprivate readonly ApplicationDbContext _context;", "", "\tpublic ##ComponentPrefix##ServerSideListingDataService (ApplicationDbContext context)", "\t{", "\t\t_context = context;", "\t}", "\tpublic override IQueryable<##ComponentPrefix##ListingBusinessObject> GetQuery(##ComponentPrefix##FilterBusinessObject filterBusinessObject)", "\t{", "\t\t return Array.Empty<##ComponentPrefix##ListingBusinessObject>().AsQueryable();", "\t}", "}"]}, {"FileName": "Form\\##ComponentPrefix##ServerSideFormDataService.cs", "FileCategory": 2, "Content": ["using ##frameworkNamespace##;", "using ##dbContextNamespace##.DB;", "using ##Name##.ServiceContracts.Features.##moduleNamespace##;", "namespace ##Name##.Server.DataServices.Features.##moduleNamespace##;", "public class ##ComponentPrefix##ServerSideFormDataService : I##ComponentPrefix##FormDataService", "{", "", "\tprivate readonly ApplicationDbContext _context;", "", "\tpublic ##ComponentPrefix##ServerSideFormDataService (ApplicationDbContext context)", "\t{", "\t\t_context = context;", "\t}", "\tpublic async Task<##primaryKeyType##> SaveAsync(##ComponentPrefix##FormBusinessObject formBusinessObject)", "\t{", "\t\tthrow new NotImplementedException();", "\t}", "\tpublic async Task<##ComponentPrefix##FormBusinessObject?> GetItemByIdAsync(##primaryKeyType## id)", "\t{", "\t\tthrow new NotImplementedException();", "\t}", "}"]}], "Controllers": [{"FileName": "##ComponentPrefix##sListingController.cs", "FileCategory": 1, "Content": ["using Microsoft.AspNetCore.Mvc;", "using ##frameworkNamespace##;", "using ##Name##.ServiceContracts.Features.##moduleNamespace##;", "namespace ##Name##.Server.DataServices.Controller.##moduleNamespace##;", "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>(\"api/[controller]/[action]\")]", "public class ##ComponentPrefix##sListingController : ControllerB<PERSON>, I##ComponentPrefix##ListingDataService", "{", "", "\tprivate readonly I##ComponentPrefix##ListingDataService dataService;", "", "\tpublic ##ComponentPrefix##sListingController(I##ComponentPrefix##ListingDataService dataService)", "\t{", "\t\tthis.dataService = dataService;", "\t}", "\t[HttpGet]", "\tpublic async Task<PagedDataList<##ComponentPrefix##ListingBusinessObject>> GetPaginatedItems([FromQuery] ##ComponentPrefix##FilterBusinessObject businessObject)", "\t{", "\t\treturn await dataService.GetPaginatedItems(businessObject);", "\t}", "}"]}, {"FileName": "##ComponentPrefix##sFormController.cs", "FileCategory": 1, "Content": ["using Microsoft.AspNetCore.Mvc;", "using ##frameworkNamespace##;", "using ##Name##.ServiceContracts.Features.##moduleNamespace##;", "namespace ##Name##.Server.DataServices.Controller.##moduleNamespace##;", "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>(\"api/[controller]/[action]\")]", "public class ##ComponentPrefix##sFormController : ControllerB<PERSON>, I##ComponentPrefix##FormDataService", "{", "", "\tprivate readonly I##ComponentPrefix##FormDataService dataService;", "", "\tpublic ##ComponentPrefix##sFormController(I##ComponentPrefix##FormDataService dataService)", "\t{", "\t\tthis.dataService = dataService;", "\t}", "\t[HttpPost]", "\tpublic async Task<##primaryKeyType##> SaveAsync([FromBody] ##ComponentPrefix##FormBusinessObject formBusinessObject)", "\t{", "\t\treturn await dataService.SaveAsync(formBusinessObject);", "\t}", "\t[HttpGet]", "\tpublic async Task<##ComponentPrefix##FormBusinessObject?> GetItemByIdAsync(##primaryKeyType## id)", "\t{", "\t\treturn await dataService.GetItemByIdAsync(id);", "\t}", "}"]}], "UILibrary": [{"FileName": "Listing\\##ComponentPrefix##ListingViewModel.cs", "FileCategory": 1, "Content": ["using ##frameworkNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##ListingViewModel", "{", "\t//Add Listing ViewModel Attributes here", "}"]}, {"FileName": "Listing\\##ComponentPrefix##FilterViewModel.cs", "FileCategory": 1, "Content": ["using ##frameworkNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##FilterViewModel :  BaseFilterViewModel", "{", "\t//Add Filter ViewModel Attributes here", "}"]}, {"FileName": "Listing\\##ComponentPrefix##ListingComponent.razor", "FileCategory": 1, "Content": ["@using Microsoft.AspNetCore.Components.Forms", "@using ##frameworkNamespace##;", "@using ##Name##.Razor.Features.##moduleNamespace##;", "@using ##Name##.ServiceContracts.Features.##moduleNamespace##;", "@inherits ListingBase<##ComponentPrefix##ListingViewModel,##ComponentPrefix##ListingBusinessObject,##ComponentPrefix##FilterViewModel,##ComponentPrefix##FilterBusinessObject, I##ComponentPrefix##ListingDataService>", "", "@*<button  class=\"btn btn-primary\" type=\"button\" @onclick='()=> ShowCenterDialog<##ComponentPrefix##FormComponent>(default(##primaryKeyType##), \"Create New ##ComponentPrefix##\")'>Add New</button>*@", "", "@if(Items == null)", "{}", "else", "{", "if(Items.Count() == 0)", "{}", "else", "{", "@foreach(var item in Items)", "{", "\t<!-- Add item layout here -->", "}", "<EditForm Model=\"PaginationStrip\">", "\t @*<PaginationStrip @bind-Value=\"PaginationStrip\" TotalPages=\"TotalPages\" TotalRows=\"TotalRows\"></PaginationStrip> *@", "</EditForm>", "}", "}", ""]}, {"FileName": "Form\\##ComponentPrefix##FormViewModel.cs", "FileCategory": 1, "Content": ["using ##frameworkNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##FormViewModel : ObservableBase", "{", "\t//Add Form Model Attributes here", "}"]}, {"FileName": "Form\\##ComponentPrefix##FormComponent.razor", "FileCategory": 1, "Content": ["@using ##frameworkNamespace##;", "@using ##Name##.Razor.Features.##moduleNamespace##;", "@using ##Name##.ServiceContracts.Features.##moduleNamespace##;", "@using Microsoft.AspNetCore.Components.Forms", "@inherits FormBase<##ComponentPrefix##FormBusinessObject,##ComponentPrefix##FormViewModel, ##primaryKeyType##, I##ComponentPrefix##FormDataService>", "", "@if (SelectedItem!=null)", "{", "<EditForm Enhance method=\"post\" Model=\"SelectedItem\" OnValidSubmit=\"OnFormSubmit\" FormName=\"##moduleNamespace##\">", "\t<DataAnnotationsValidator></DataAnnotationsValidator>", "\t<ValidationSummary></ValidationSummary>", "<!-- Add Form layout here -->", "</EditForm>", "}"]}, {"FileName": "Listing\\##ComponentPrefix##ClientSideListingDataService.cs", "FileCategory": 1, "Content": ["using System.Net.Http.Json;", "using ##frameworkNamespace##;", "using ##frameworkNamespace##.Extensions;", "using ##Name##.ServiceContracts;", "using ##Name##.ServiceContracts.Features.##moduleNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##ClientSideListingDataService : I##ComponentPrefix##ListingDataService", "{", "", "\tprivate readonly BaseHttpClient _httpClient;", "", "\tpublic ##ComponentPrefix##ClientSideListingDataService (BaseHttpClient context)", "\t{", "\t\t_httpClient = context;", "\t}", "\tpublic async Task<PagedDataList<##ComponentPrefix##ListingBusinessObject>> GetPaginatedItems(##ComponentPrefix##FilterBusinessObject filterBusinessObject)", "\t{", "\t\treturn await _httpClient.GetFromJsonAsync<PagedDataList<##ComponentPrefix##ListingBusinessObject>>($\"api/##ComponentPrefix##sListing/GetPaginatedItems\" + filterBusinessObject.ToQueryString());", "\t}", "}"]}, {"FileName": "Form\\##ComponentPrefix##ClientSideFormDataService.cs", "FileCategory": 1, "Content": ["using System.Text;", "using System.Text.Json;", "using System.Net.Http.Json;", "using ##frameworkNamespace##;", "using ##frameworkNamespace##.Extensions;", "using ##Name##.ServiceContracts;", "using ##Name##.ServiceContracts.Features.##moduleNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##ClientSideFormDataService : I##ComponentPrefix##FormDataService", "{", "", "\tprivate readonly BaseHttpClient _httpClient;", "", "\tpublic ##ComponentPrefix##ClientSideFormDataService (BaseHttpClient context)", "\t{", "\t\t_httpClient = context;", "\t}", "\tpublic async Task<##primaryKeyType##> SaveAsync(##ComponentPrefix##FormBusinessObject formBusinessObject)", "\t{", "\t\t return await _httpClient.PostAsJsonAsync<##primaryKeyType##>($\"api/##ComponentPrefix##sForm/Save\", formBusinessObject);", "\t}", "\tpublic async Task<##ComponentPrefix##FormBusinessObject?> GetItemByIdAsync(##primaryKeyType## id)", "\t{", "\t\treturn await _httpClient.GetFromJsonAsync<##ComponentPrefix##FormBusinessObject>($\"api/##ComponentPrefix##sForm/GetItemById?id=\" + id);", "\t}", "}"]}], "ClientDependency": [{"FileName": "ServiceRegistrar.cs", "FileCategory": 1, "Content": []}], "ServerSideDependency": [{"FileName": "ServiceRegistrar.cs", "FileCategory": 1, "Content": []}]}}