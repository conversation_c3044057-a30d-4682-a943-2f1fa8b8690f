﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.ChangePassword;

namespace PremierAutoTag.Server.DataServices.Controllers.Profiles;
[ApiController, Route("api/[controller]/[action]")]
[Authorize]
public class ChangePasswordsFormController : ControllerBase, IChangePasswordFormDataService
{

	private readonly IChangePasswordFormDataService dataService;

	public ChangePasswordsFormController(IChangePasswordFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] ChangePasswordFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[HttpGet]
	public async Task<ChangePasswordFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
