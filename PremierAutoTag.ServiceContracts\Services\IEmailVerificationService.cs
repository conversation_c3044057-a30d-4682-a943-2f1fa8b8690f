using PremierAutoTag.ServiceContracts.Models;

namespace PremierAutoTag.ServiceContracts.Services
{
    public interface IEmailVerificationService
    {
        /// <summary>
        /// Sends email verification for individual user registration
        /// </summary>
        Task<bool> SendIndividualVerificationEmailAsync(string userId, string email, string verificationUrl);

        /// <summary>
        /// Sends email verification for dealer user registration
        /// </summary>
        Task<bool> SendDealerVerificationEmailAsync(string userId, string email, string verificationUrl, string dealerName);

        /// <summary>
        /// Generates a secure verification token for email confirmation
        /// </summary>
        Task<string> GenerateEmailVerificationTokenAsync(string userId);

        /// <summary>
        /// Validates an email verification token
        /// </summary>
        Task<EmailVerificationResult> ValidateEmailVerificationTokenAsync(string userId, string token);

        /// <summary>
        /// Marks user email as verified
        /// </summary>
        Task<bool> MarkEmailAsVerifiedAsync(string userId);

        /// <summary>
        /// Checks if user's email is verified
        /// </summary>
        Task<bool> IsEmailVerifiedAsync(string userId);
    }
}
