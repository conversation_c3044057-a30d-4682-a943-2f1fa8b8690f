﻿using System.Net.Http.Json;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Extensions;
using PremierAutoTag.ServiceContracts;
using PremierAutoTag.ServiceContracts.Features.UserDashboard;
namespace PremierAutoTag.Razor.Features.UserDashboard;
public class UserDashboardClientSideListingDataService : IUserDashboardListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public UserDashboardClientSideListingDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<PagedDataList<UserDashboardListingBusinessObject>> GetPaginatedItems(UserDashboardFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<UserDashboardListingBusinessObject>>($"api/UserDashboardsListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}
