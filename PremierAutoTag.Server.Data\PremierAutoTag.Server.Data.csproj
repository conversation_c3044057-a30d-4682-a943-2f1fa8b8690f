﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>

		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" /> 
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" /> 
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference> 
		<PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.6.0" /> 
		<PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="9.0.6" />

	</ItemGroup>

	<ItemGroup>
	  <Folder Include="DB\Configuration\" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\PremierAutoTag.Framework.Core\PremierAutoTag.Framework.Core.csproj" />
	</ItemGroup>

</Project>
