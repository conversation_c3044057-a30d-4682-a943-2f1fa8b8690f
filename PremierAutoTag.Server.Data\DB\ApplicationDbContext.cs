﻿
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Server.Data.Entities;

namespace PremierAutoTag.Server.Data.DB
{
    public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IAuthenticatedUser authenticatedUserId) : IdentityDbContext<ApplicationUser>(options)
    {
        private readonly IAuthenticatedUser? _authenticatedUserId = authenticatedUserId;

        public DbSet<UserProfile> Profiles { get; set; }
        public DbSet<SubscriptionPlan> SubscriptionPlans { get; set; }
        public DbSet<StripePayment> StripePayments { get; set; }
        public DbSet<StripeWebhookEvent> StripeWebhookEvents { get; set; }
        public DbSet<UserSubscription> UserSubscriptions { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.ConfigureWarnings(x => x.Ignore(RelationalEventId.PendingModelChangesWarning));
            base.OnConfiguring(optionsBuilder);
        }

        #region Contants

        public int GetNextValueOfSequence(string sequenceName)
        {
            SqlParameter result = new SqlParameter("@result", System.Data.SqlDbType.Int)
            {
                Direction = System.Data.ParameterDirection.Output
            };
            Database.ExecuteSqlRaw("SELECT @result = (NEXT VALUE FOR dbo." + sequenceName + ")", result);
            return (int)result.Value;
        }




        #endregion
        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
        {
            foreach (var entry in ChangeTracker.Entries<BaseEntity>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                            entry.Entity.CreatedBy = _authenticatedUserId.UserId;
                        break;
                    case EntityState.Modified:
                        entry.Entity.ModifiedAt = DateTime.UtcNow;
                        if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                            entry.Entity.ModifiedBy = _authenticatedUserId.UserId;
                        break;
                    case EntityState.Deleted:
                        entry.Entity.DeletedAt = DateTime.UtcNow;
                        entry.Entity.IsDelete = true;
                        if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                            entry.Entity.DeletedBy = _authenticatedUserId.UserId;
                        break;
                }
            }
            return base.SaveChangesAsync(cancellationToken);
        }


        public override int SaveChanges()
        {
            foreach (var entry in ChangeTracker.Entries<BaseEntity>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                            entry.Entity.CreatedBy = _authenticatedUserId.UserId;
                        break;
                    case EntityState.Modified:
                        entry.Entity.ModifiedAt = DateTime.UtcNow;
                        if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                            entry.Entity.ModifiedBy = _authenticatedUserId.UserId;
                        break;
                    case EntityState.Deleted:
                        entry.Entity.DeletedAt = DateTime.UtcNow;
                        entry.Entity.IsDelete = true;
                        if (!string.IsNullOrEmpty(_authenticatedUserId?.UserId))
                            entry.Entity.DeletedBy = _authenticatedUserId.UserId;
                        break;
                }
            }
            return base.SaveChanges();
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            // Configure UserProfile as one-to-one with ApplicationUser
            builder.Entity<UserProfile>()
                .HasKey(p => p.Id); // Primary key

            // Configure one-to-one relationship between ApplicationUser and UserProfile
            builder.Entity<ApplicationUser>()
                .HasOne<UserProfile>()
                .WithOne(p => p.ApplicationUser)
                .HasForeignKey<UserProfile>(p => p.Id)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure UserProfile -> SubscriptionPlan relationship
            builder.Entity<UserProfile>()
                .HasOne(p => p.SubscriptionPlan)
                .WithMany(s => s.UserProfiles)
                .HasForeignKey(p => p.SubscriptionPlanId)
                .OnDelete(DeleteBehavior.SetNull);

            // Configure SubscriptionPlan
            builder.Entity<SubscriptionPlan>()
                .HasKey(s => s.Id);

            // Configure StripePayment
            builder.Entity<StripePayment>()
                .HasKey(sp => sp.Id);

            builder.Entity<StripePayment>()
                .HasOne(sp => sp.SubscriptionPlan)
                .WithMany()
                .HasForeignKey(sp => sp.SubscriptionPlanId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StripePayment>()
                .HasOne(sp => sp.User)
                .WithMany()
                .HasForeignKey(sp => sp.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure StripeWebhookEvent
            builder.Entity<StripeWebhookEvent>()
                .HasKey(swe => swe.Id);

            builder.Entity<StripeWebhookEvent>()
                .HasOne(swe => swe.StripePayment)
                .WithMany(sp => sp.WebhookEvents)
                .HasForeignKey(swe => swe.StripePaymentId)
                .OnDelete(DeleteBehavior.SetNull);

            // Configure UserSubscription
            builder.Entity<UserSubscription>()
                .HasKey(us => us.Id);

            builder.Entity<UserSubscription>()
                .HasOne(us => us.User)
                .WithMany()
                .HasForeignKey(us => us.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<UserSubscription>()
                .HasOne(us => us.SubscriptionPlan)
                .WithMany()
                .HasForeignKey(us => us.SubscriptionPlanId)
                .OnDelete(DeleteBehavior.Restrict);

            foreach (var property in builder.Model.GetEntityTypes()
            .SelectMany(t => t.GetProperties())
            .Where(p => p.ClrType == typeof(decimal) || p.ClrType == typeof(decimal?)))
            {
                // EF Core 5
                property.SetPrecision(16);
                property.SetScale(2);
            }

            base.OnModelCreating(builder);
        }
    }

}
