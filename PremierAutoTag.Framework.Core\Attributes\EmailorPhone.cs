﻿using PremierAutoTag.Framework.Core.Extensions;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace PremierAutoTag.Framework.Core.Attributes
{
    public class EmailOrPhoneAttribute : ValidationAttribute
    {
        private readonly string _validate;
        private readonly string _countryCode;

        public EmailOrPhoneAttribute(string validate = "both", string countryCode = "all")
        {
            _validate = validate;
            _countryCode = countryCode;
        }

        public override bool IsValid(object? value)
        {
            var emailOrPhone = value as string;

            if (_validate.ToLower() == "email")
            {
                if (this.IsValidEmailAddress(emailOrPhone!))
                {
                    // Is valid email address
                    return true;
                }
            }

            else if (_validate.ToLower() == "phone")
            {
                if (this.IsValidPhoneNumber(emailOrPhone!))
                {
                    // Assume phone number
                    return true;
                }
            }
            else if (_validate.ToLower() == "both")
            {
                if (this.IsValidEmailAddress(emailOrPhone!))
                {
                    // Is valid email address
                    return true;
                }
                else if (this.IsValidPhoneNumber(emailOrPhone!))
                {
                    // Assume phone number
                    return true;
                }
            }

            return false;
        }

        private bool IsValidEmailAddress(string emailToValidate)
        {
            if (!string.IsNullOrEmpty(emailToValidate))
            {
                // Get instance of MVC email validation attribute
                var regex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
                if (regex.IsMatch(emailToValidate))
                {
                    var emailAttribute = new EmailAddressAttribute();

                    return emailAttribute.IsValid(emailToValidate);
                }
                else
                {
                    return false;
                }
            }

            return true;
        }

        private bool IsValidPhoneNumber(string phoneNumberToValidate)
        {
            if (phoneNumberToValidate == null)
            {
                return false;
            }

            Regex? regex = null;
            if (_countryCode == "")
            {
                regex = new Regex(@"^(?:(\+923\d{9})|(03\d{9}))$");
            }
            else
            {
                if (_countryCode == "all")
                {
                    regex = new Regex(@"^7\d{9}$");
                    if (regex.IsMatch(phoneNumberToValidate))
                    {
                        return true;
                    }

                    regex = new Regex(@"^3\d{9}$");

                    if (regex.IsMatch(phoneNumberToValidate))
                    {
                        return true;
                    }

                    return false;
                }
                else
                {
                    if (_countryCode == "PK")
                    {
                        regex = new Regex(@"^3\d{9}$");
                    }
                    else if (_countryCode == "UK")
                    {
                        regex = new Regex(@"^7\d{9}$");
                    }
                }
            }

            return regex != null && regex.IsMatch(phoneNumberToValidate);
        }
    }

    public class NullableEmailOrPhoneAttribute : ValidationAttribute
    {

        public override bool IsValid(object? value)
        {

            var emailOrPhone = value as string;
            if (string.IsNullOrEmpty(emailOrPhone!))
            {
                return true;
            }

            // Is this a valid email address?
            if (this.IsValidEmailAddress(emailOrPhone!))
            {
                // Is valid email address
                return true;
            }
            else if (this.IsValidPhoneNumber(emailOrPhone!))
            {
                // Assume phone number
                return true;
            }

            // Not valid email address or phone
            return false;
        }

        private bool IsValidEmailAddress(string emailToValidate)
        {
            // Get instance of MVC email validation attribute
            var emailAttribute = new EmailAddressAttribute();

            return emailAttribute.IsValid(emailToValidate);
        }

        private bool IsValidPhoneNumber(string phoneNumberToValidate)
        {
            // Regualr expression from https://stackoverflow.com/a/8909045/894792 for phone numbers
            var regex = new Regex(@"^\+(?:[0-9] ?){6,14}[0-9]$");
            return regex.IsMatch(phoneNumberToValidate);
        }
    }

    public enum ValidationContactType
    {
        [Description("Email or Phone No.")]
        Any,

        [Description("Phone No.")]
        Phone,

        [Description("Email")]
        Email,
    }
    public class ContactValidatorAttribute : ValidationAttribute
    {
        private readonly ValidationContactType contactType;
        private readonly bool required;

        public ContactValidatorAttribute(ValidationContactType contactType = ValidationContactType.Any, bool required = true)
        {
            this.contactType = contactType;
            this.required = required;
        }
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            var contactTypeString = contactType.GetDescription();
            var fieldName = validationContext?.MemberName;
            if (fieldName == null)
                throw new ArgumentNullException(nameof(fieldName));

            if (value == null && required)
            {
                return new ValidationResult($"{contactTypeString} is required.", [fieldName]);
            }

            if(value == null || value.ToString() == string.Empty)
            {
                return null;
            }

            var contact = value.ToString();

            if (contact == null || contact.Length < 10)
            {
                return new ValidationResult($"Minimum 10 length {contactTypeString} is required", [fieldName]);
            }
            else if (contact.Length > 50)
            {
                return new ValidationResult($"Maximum 50 length {contactTypeString} is allowed", [fieldName]);
            }


            // Regular expression for validating email
            string emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";

            // Regular expression for validating phone numbers starting with a country code (+ followed by digits)
            string phonePattern = @"^(\+?\d{1,3}|0)\d{9,12}$";

            if (contactType == ValidationContactType.Phone && Regex.IsMatch(contact, phonePattern))
            {
                return ValidationResult.Success;
            }
            else if (contactType == ValidationContactType.Email && (Regex.IsMatch(contact, emailPattern)))
            {
                return ValidationResult.Success;
            }
            else if (contactType == ValidationContactType.Any && (Regex.IsMatch(contact, emailPattern) || Regex.IsMatch(contact, phonePattern)))
            {
                return ValidationResult.Success;
            }

            return new ValidationResult($"Invalid {contactTypeString}", [fieldName]);
        }
    }

    public class PasswordValidatorAttribute : ValidationAttribute
    {
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            var fieldName = validationContext?.MemberName;
            if (fieldName == null)
                throw new ArgumentNullException(nameof(fieldName));

            var contact = value?.ToString();

            if (contact == null || contact.Length < 6)
            {
                return new ValidationResult($"Minimum 6 length password is required", [fieldName]);
            }
            else if (contact.Length > 24)
            {
                return new ValidationResult($"Maximum 24 length password is allowed", [fieldName]);
            }

            return ValidationResult.Success;
        }
    }

}
