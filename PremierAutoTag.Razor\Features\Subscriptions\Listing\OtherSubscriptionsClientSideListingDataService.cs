﻿using System.Net.Http.Json;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.Framework.Core.Extensions;
using PremierAutoTag.ServiceContracts;
using PremierAutoTag.ServiceContracts.Features.OtherSubscriptions;
namespace PremierAutoTag.Razor.Features.OtherSubscriptions;
public class OtherSubscriptionsClientSideListingDataService : IOtherSubscriptionsListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public OtherSubscriptionsClientSideListingDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<PagedDataList<OtherSubscriptionsListingBusinessObject>> GetPaginatedItems(OtherSubscriptionsFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<OtherSubscriptionsListingBusinessObject>>($"api/OtherSubscriptionssListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}
