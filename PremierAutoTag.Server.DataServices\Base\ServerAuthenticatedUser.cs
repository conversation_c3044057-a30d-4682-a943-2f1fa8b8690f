﻿using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using PremierAutoTag.Framework.Core;
using System.Security.Claims;

namespace PremierAutoTag.Server.DataServices
{
    public class ServerAuthenticatedUser : IAuthenticatedUser
    {
        private readonly IHttpContextAccessor _httpContextAccessor = null!;
        public ServerAuthenticatedUser(IServiceScopeFactory scopeFactory)
        {
            try
            {
                _httpContextAccessor = scopeFactory.CreateScope().ServiceProvider.GetRequiredService<IHttpContextAccessor>();
                UserId = _httpContextAccessor.HttpContext?.User?.GetUserId() ?? "";
                ProfileName = _httpContextAccessor.HttpContext?.User?.FindFirst(u => u.Type.Contains("kt_profilename"))?.Value ?? "";
                ImageUrl = _httpContextAccessor.HttpContext?.User?.FindFirst(u => u.Type.Contains("kt_profileimage"))?.Value ?? "";
                Phone = _httpContextAccessor.HttpContext?.User?.FindFirst(u => u.Type.Contains("kt_phone"))?.Value;
                Email = _httpContextAccessor.HttpContext?.User?.FindFirst(u => u.Type.Contains("kt_email"))?.Value;
                var username = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Name)?.Value;
                if (username != null)
                {
                    Username = username.Contains("@") ? username : Phone;
                }

            }
            catch
            {
                // Set default values if initialization fails
                UserId = "";
                ProfileName = "";
                ImageUrl = "";
            }

        }
        public string UserId { get; set; }
        public string ProfileName { get; set; }
        public string ImageUrl { get; set; }
        public double TimeZoneOffset { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Username { get; set; }
    }

    public class ServerAuthenticatedUserId : IAuthenticatedUserId
    {
        public string UserId => httpContextAccessor.HttpContext?.User?.GetUserId() ?? "";

        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public ServerAuthenticatedUserId(IServiceScopeFactory scopeFactory)
        {
            httpContextAccessor = scopeFactory.CreateScope().ServiceProvider.GetRequiredService<IHttpContextAccessor>();
            _authenticationStateProvider = scopeFactory.CreateScope().ServiceProvider.GetRequiredService<AuthenticationStateProvider>();
        }

        public async Task<string> GetCurrentUserIdAsync()
        {
            // Get the current authentication state
            var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            // Extract the user ID claim (e.g., NameIdentifier)
            string? userId = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId == null)
            {

                throw new Exception("User is not authenticated");
            }

            return userId;
        }

        public static implicit operator string(ServerAuthenticatedUserId authenticatedUserId)
        {
            return authenticatedUserId.UserId;
        }

    }
}
