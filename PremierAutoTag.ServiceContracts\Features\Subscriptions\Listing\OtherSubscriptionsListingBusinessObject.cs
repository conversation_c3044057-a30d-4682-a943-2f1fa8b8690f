﻿using PremierAutoTag.Framework.Core;
namespace PremierAutoTag.ServiceContracts.Features.OtherSubscriptions;
public class OtherSubscriptionsListingBusinessObject
{
	// Subscription Plan Information
	public long Id { get; set; }
	public string Name { get; set; } = "";
	public string? Description { get; set; }
	public decimal AnnualPrice { get; set; }
	public int MaxVehicles { get; set; }
	public int MaxUsers { get; set; }
	public int StandAloneQuotesDiscountPercent { get; set; }
	public int RegistrationTitleServicesDiscountPercent { get; set; }
	public bool HasDirectSubmission { get; set; }
	public bool HasDedicatedClientManager { get; set; }
	public string? Features { get; set; }
	public bool IsPopular { get; set; }
	public int DisplayOrder { get; set; }
	public bool RequiresPayment { get; set; }

	// Computed Properties for Display
	public string PriceDisplay => $"${AnnualPrice:F0}/year";
	public bool IsFree => AnnualPrice == 0;
}
