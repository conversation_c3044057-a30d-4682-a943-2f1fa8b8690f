﻿@page "/dealership"
@layout MainLayout
@attribute [Authorize(Roles = "Dealer")]
@using Microsoft.AspNetCore.Authorization
@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Razor.Features.DealershipProfile;
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.ServiceContracts.Features.DealershipProfile;
@using Microsoft.AspNetCore.Components.Forms
@inherits FormBase<DealershipProfileFormBusinessObject,DealershipProfileFormViewModel, string, IDealershipProfileFormDataService>

@if (SelectedItem!=null)
{
<EditForm Enhance method="post" Model="SelectedItem" OnValidSubmit="OnFormSubmit" FormName="DealershipProfile">
	<DataAnnotationsValidator></DataAnnotationsValidator>
	<ValidationSummary></ValidationSummary>
        <div class="tab-content hidden" data-content="2">
            <h2 class="font-semibold text-lg md:text-xl text-gray-m-800">
                Dealership Information
            </h2>
            <form class=" w-full flex flex-col gap-3 md:gap-4 my-4">

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">
                        Dealership Name <span class="text-red-500">*</span>
                    </label>
                    <input value="" type="text"
                           class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                           placeholder="" />
                </div>

                <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Dealership ID
                            <span class="text-red-500">*</span>
                        </label>
                        <input disabled value="" type="text"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 disabled:bg-seaShell disabled:text-gray-400 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="" />
                    </div>
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Tax ID <span class="text-red-500">*</span>
                        </label>
                        <input disabled value="" type="text"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 disabled:bg-seaShell disabled:text-gray-400 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="" />
                    </div>
                </div>

                <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Registered State
                        </label>
                        <div class=" relative flex md:!w-full flex-col mb-3 md:mb-0 gap-1 text-slate-700 ">
                            <svg xmlns=" http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                 fill="currentColor"
                                 class="absolute w-6 h-6 pointer-events-none right-2 top-2">
                                <path fillRule="evenodd"
                                      d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                                      clipRule="evenodd" />
                            </svg>
                            <select id="os" name="os"
                                    class="w-full appearance-none rounded-lg md:rounded-lg pe-12 text-[#212529] border border-gray-200 focus:border-gray-200 focus:ring-0 bg-white  px-4 py-2    focus-visible:outline-offset-2 focus-visible:outline-0 disabled:cursor-not-allowed disabled:opacity-75 ">
                                <option value="">Choose</option>
                                <option>Pakistan</option>
                            </select>
                        </div>
                    </div>

                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Subsidiary of another of another company <span class="text-red-500">*</span>
                        </label>
                        <div class="flex items-center gap-3 mt-3">

                            <div>
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <input type="radio" name="subsidiary" value="yes"
                                           class="peer hidden" />
                                    <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                        <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                    </span>
                                    <span class="text-gray-700 text-sm md:text-base font-medium">Yes</span>
                                </label>

                            </div>
                            <div>
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <input type="radio" name="subsidiary" value="no"
                                           class="peer hidden" />
                                    <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                        <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                    </span>
                                    <span class="text-gray-700 text-sm md:text-base font-medium">No</span>
                                </label>

                            </div>

                        </div>
                    </div>
                </div>

                <div>
                    <label class="text-brightGray font-medium text-sm mb-1 block">
                        Address <span class="text-red-500">*</span>
                    </label>
                    <input value="" type="text"
                           class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                           placeholder="" />
                </div>

                <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Dealership Phone #
                            <span class="text-red-500">*</span>
                        </label>
                        <input value="" type="text"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="" />
                    </div>
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Authorized Company
                            <span class="text-red-500">*</span>
                        </label>
                        <input value="" type="text"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="" />
                    </div>
                </div>

                <div class="flex flex-col md:flex-row gap-3 items-center md:gap-4">
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Administrative
                            email address <span class="text-red-500">*</span>
                        </label>
                        <input value="" type="text"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="" />
                    </div>
                    <div class="w-full">
                        <label class="text-brightGray font-medium text-sm mb-1 block">
                            Administrative
                            phone <span class="text-red-500">*</span>
                        </label>
                        <input value="" type="text"
                               class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                               placeholder="" />
                    </div>
                </div>

                <div class="border-b border-b-gray-200 pb-5">
                    <label for="" class="font-semibold text-sm md:text-lg text-gray-m-800">
                        Dealership
                        lots
                    </label>
                    <div class="flex items-center gap-3 mt-3">

                        <div>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="lots" value="Sole" class="peer hidden" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">Sole</span>
                            </label>

                        </div>
                        <div>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="lots" value="1 - 3" class="peer hidden" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">
                                    1 -
                                    3
                                </span>
                            </label>

                        </div>

                        <div>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="lots" value="3 +" class="peer hidden" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">3 +</span>
                            </label>

                        </div>

                    </div>

                </div>

                <div class="border-b border-b-gray-200 pb-5">
                    <label for="" class="font-semibold text-sm md:text-lg text-gray-m-800">
                        Number of
                        vehicles on
                        lot
                    </label>
                    <div class="flex items-center gap-3 mt-3">

                        <div>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="vehicles" value="1 - 25"
                                       class="peer hidden" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">
                                    1 -
                                    25
                                </span>
                            </label>

                        </div>
                        <div>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="vehicles" value="26 - 100"
                                       class="peer hidden" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">
                                    26 -
                                    100
                                </span>
                            </label>

                        </div>

                        <div>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="vehicles" value="100 +" class="peer hidden" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">
                                    100
                                    +
                                </span>
                            </label>

                        </div>

                    </div>

                </div>

                <div class="border-b border-b-gray-200 pb-5">
                    <label for="" class="font-semibold text-sm md:text-lg text-gray-m-800">
                        Average
                        vehicles sold per
                        month
                    </label>
                    <div class="flex items-center gap-3 mt-3">

                        <div>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="vehiclessold" value="1 - 10"
                                       class="peer hidden" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">
                                    1 -
                                    10
                                </span>
                            </label>

                        </div>
                        <div>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="vehiclessold" value="11 - 25"
                                       class="peer hidden" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">
                                    11 -
                                    25
                                </span>
                            </label>

                        </div>

                        <div>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="vehiclessold" value="25 +"
                                       class="peer hidden" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">25 +</span>
                            </label>

                        </div>

                    </div>

                </div>


                <div class="flex flex-row gap-3 justify-end items-center">
                    <button class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg">
                        Cancel
                    </button>
                    <button class="px-4 py-2 rounded-lg bg-primary hover:bg-primaryDark text-white">
                        Save
                    </button>
                </div>

            </form>
        </div>
</EditForm>
}
