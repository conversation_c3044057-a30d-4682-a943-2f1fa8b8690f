﻿@page "/dealership-info"
@layout MainLayout
@attribute [Authorize(Roles = "Dealer")]

@using PremierAutoTag.Framework.Core;
@using PremierAutoTag.Framework.Core.UIServices;
@using PremierAutoTag.Framework.Core.Enums;
@using PremierAutoTag.Razor.Features.DealershipProfile;
@using PremierAutoTag.Razor.Layout
@using PremierAutoTag.ServiceContracts.Features.DealershipProfile;
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Logging
@inherits FormBase<DealershipProfileFormBusinessObject,DealershipProfileFormViewModel, string, IDealershipProfileFormDataService>

@if (!string.IsNullOrEmpty(ValidationError))
{
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <p class="text-red-800 text-sm md:text-base">@ValidationError</p>
    </div>
}

@if (SelectedItem != null)
{
    <EditForm Enhance method="post" Model="SelectedItem" OnValidSubmit="OnFormSubmit" FormName="DealershipProfile">
        <DataAnnotationsValidator></DataAnnotationsValidator>
        <div class="w-full">
            <div class="tab-content block w-full" data-content="1">
                <h2 class="font-semibold text-lg md:text-xl text-gray-m-800">
                    Dealership Information
                </h2>
                <hr class="my-4 text-gray-200" />

                <div class="grid grid-cols-12 gap-6 items-center">
                    <!-- Dealership Name -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Dealership Name <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.DealerName"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter dealership name" />
                        <ValidationMessage For="() => SelectedItem.DealerName" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Dealer ID -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Dealer ID <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.DealerId"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter dealer ID" />
                        <ValidationMessage For="() => SelectedItem.DealerId" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Tax ID -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Tax ID <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.TaxId"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter tax ID" />
                        <ValidationMessage For="() => SelectedItem.TaxId" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- State -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">State <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <div class="relative">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                 class="absolute w-6 h-6 pointer-events-none right-2 top-2">
                                <path fillRule="evenodd"
                                      d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                                      clipRule="evenodd" />
                            </svg>
                            <InputSelect @bind-Value="SelectedItem.StateId"
                                         class="w-full appearance-none rounded-lg pe-12 text-gray-900 border border-gray-300 focus:border-gray-300 focus:ring-1 focus:ring-gray-300 bg-white px-3 py-2 md:py-2.5 text-sm md:text-base outline-none">
                                <option value="0">Choose State</option>
                                <option value="2">Connecticut</option>
                            </InputSelect>
                        </div>
                        <ValidationMessage For="() => SelectedItem.StateId" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Address -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Address <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.Address"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter address" />
                        <ValidationMessage For="() => SelectedItem.Address" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Phone -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Phone</span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.Phone"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter phone number" />
                        <ValidationMessage For="() => SelectedItem.Phone" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Authorized Company -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Authorized Company <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.AuthorizedCompany"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter authorized company" />
                        <ValidationMessage For="() => SelectedItem.AuthorizedCompany" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Administrative Email -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Administrative Email <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.AdministrativeEmail"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter administrative email" />
                        <ValidationMessage For="() => SelectedItem.AdministrativeEmail" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Administrative Phone -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Administrative Phone</span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputText @bind-Value="SelectedItem.AdministrativePhone"
                                   class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                                   placeholder="Enter administrative phone" />
                        <ValidationMessage For="() => SelectedItem.AdministrativePhone" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Subsidiary -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Subsidiary of another company</span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <div class="flex items-center gap-3 mt-2">
                            <InputRadioGroup @bind-Value="SelectedItem.Subsidary" class="flex flex-row gap-4">
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <InputRadio class="peer hidden" Value="true" />
                                    <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                        <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                    </span>
                                    <span class="text-gray-700 text-sm md:text-base font-medium">Yes</span>
                                </label>
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <InputRadio class="peer hidden" Value="false" />
                                    <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                        <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                    </span>
                                    <span class="text-gray-700 text-sm md:text-base font-medium">No</span>
                                </label>
                            </InputRadioGroup>
                        </div>
                    </div>
                </div>

                <hr class="my-6 text-gray-200" />

                <!-- Dealership Lots Section -->
                <div class="grid grid-cols-12 gap-6 items-start">
                    <div class="col-span-12">
                        <h3 class="font-semibold text-base md:text-lg text-gray-800 mb-4">Dealership Lots</h3>
                    </div>
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Number of lots <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputRadioGroup @bind-Value="SelectedItem.DealershipIOTTypes" class="flex flex-col gap-2">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="DEALERSHIP_IOT_TYPES.Sole" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">Sole</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="DEALERSHIP_IOT_TYPES.OnetoThree" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">1 - 3</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="DEALERSHIP_IOT_TYPES.Threeplus" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">3+</span>
                            </label>
                        </InputRadioGroup>
                        <ValidationMessage For="() => SelectedItem.DealershipIOTTypes" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Number of vehicles on lot -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Number of vehicles on lot <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputRadioGroup @bind-Value="SelectedItem.VehicalOnIOTTypes" class="flex flex-col gap-2">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_ON_IOT_TYPES.OnetoTwentySix" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">1 - 26</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_ON_IOT_TYPES.TwentySixtoHundred" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">26 - 100</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_ON_IOT_TYPES.HundredPlus" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">100+</span>
                            </label>
                        </InputRadioGroup>
                        <ValidationMessage For="() => SelectedItem.VehicalOnIOTTypes" class="text-red-500 text-sm mt-1" />
                    </div>

                    <!-- Average vehicles sold per month -->
                    <div class="col-span-4 md:col-span-3">
                        <span class="text-sm font-medium text-gray-700">Average vehicles sold per month <span class="text-red-500">*</span></span>
                    </div>
                    <div class="md:col-span-7 col-span-8">
                        <InputRadioGroup @bind-Value="SelectedItem.VehicalSoldIOT" class="flex flex-col gap-2">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_SOLD_IOT.OnetoTen" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">1 - 10</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_SOLD_IOT.EleventoTwentyFive" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">11 - 25</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <InputRadio class="peer hidden" Value="VEHICAL_SOLD_IOT.TwentyFivePlus" />
                                <span class="md:w-5 w-4 h-4 md:h-5 rounded-full border border-gray-300 flex items-center justify-center peer-checked:bg-primary peer-checked:border-primary">
                                    <span class="w-2 h-2 bg-white md:mb-[0.7px] md:ms-[1px] rounded-full"></span>
                                </span>
                                <span class="text-gray-700 text-sm md:text-base font-medium">26+</span>
                            </label>
                        </InputRadioGroup>
                        <ValidationMessage For="() => SelectedItem.VehicalSoldIOT" class="text-red-500 text-sm mt-1" />
                    </div>
                </div>

                <hr class="my-4 text-gray-200" />

                <div class="flex justify-end gap-3">
                    <button type="button"
                            disabled="@IsBusy"
                            class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            disabled="@IsBusy"
                            class="px-4 py-2 rounded-lg bg-primary hover:bg-primaryDark disabled:bg-gray-400 text-white transition-colors">
                        @if (IsBusy)
                        {
                            <span class="flex items-center gap-2">
                                <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Saving...
                            </span>
                        }
                        else
                        {
                            <span>Save Dealership Information</span>
                        }
                    </button>
                </div>
            </div>
        </div>
    </EditForm>
}
else
{
    <div class="flex items-center justify-center py-8">
        <div class="text-center">
            <svg class="animate-spin h-8 w-8 text-primary mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p class="text-gray-600">Loading...</p>
        </div>
    </div>
}

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Set the user ID from authenticated user - framework will handle the rest automatically
            if (AuthenticatedUser != null && !string.IsNullOrEmpty(AuthenticatedUser.UserId))
            {
                Id = AuthenticatedUser.UserId;
            }

            // Let framework handle automatic data loading via GetItemByIdAsync() and ConvertBusinessModelToViewModel()
            await base.OnInitializedAsync();
        }
        catch (Exception ex)
        {
            ValidationError = $"Error initializing dealership profile: {ex.Message}";
            Logger.LogError(ex, "Error initializing DealershipProfile component for user {UserId}", AuthenticatedUser?.UserId);
        }
    }

    // Framework automatically handles conversion using Clone<T>() extension method
    // No need to override ConvertViewModelToBusinessModel and ConvertBusinessModelToViewModel
    // unless custom logic is required

    public override async Task OnAfterSaveAsync(string key)
    {
        // Show success message using framework's AlertService
        AlertService?.Show("Dealership information updated successfully!", MessageTypes.Success);

        await base.OnAfterSaveAsync(key);
    }
}


