﻿using Microsoft.AspNetCore.Mvc;
using PremierAutoTag.Framework.Core;
using PremierAutoTag.ServiceContracts.Features.MyProfile;
namespace PremierAutoTag.Server.DataServices.Controller.MyProfile;
[ApiController, Route("api/[controller]/[action]")]
public class MyProfilesFormController : ControllerBase, IMyProfileFormDataService
{

	private readonly IMyProfileFormDataService dataService;

	public MyProfilesFormController(IMyProfileFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] MyProfileFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[HttpGet]
	public async Task<MyProfileFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
