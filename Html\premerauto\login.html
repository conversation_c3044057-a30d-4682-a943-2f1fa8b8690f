<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link href="./css/output.css" rel="stylesheet">
</head>

<body>
    <header class="bg-white w-full fixed top-0 border-b border-b-gray-m-200 z-50">
        <nav class="flex items-center justify-between md:px-12 lg:px-16  p-4 mx-auto ">

            <div class="flex ">
                <a class="-m-1.5 p-1.5" href="/html/premerauto/index.html"><span class="sr-only">Your Company</span>
                    <img alt="" loading="lazy" decoding="async" class="h-9 lg:h-14" src="images/logo.png">
                </a>
            </div>

            <ul class="items-center  justify-center flex-grow hidden gap-2 xl:gap-6 lg:flex ">
                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="">Home</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="aboutus.html">About us</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="locations.html">Locations/Hours</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="FAQ.html">FAQ</a>
                </li>

                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="tips.html">DMV Tips</a>
                </li>
                <li class="">
                    <a class="px-1 py-2 text-sm font-medium text-gray-900 rounded-full xl:text-base nav-link active xl:px-3 hover:underline underline-offset-4"
                        href="contact.html">Contact</a>
                </li>
            </ul>

            <div class="flex items-center gap-3">
                <div class="flex items-center gap-3">
                    <a href="login.html"
                        class="bg-white  py-1.5 md:py-2.5 px-3 md:px-4 hover:bg-gray-50 text-gray-900 text-sm md:text-sm font-medium hover:border hover:border-gray-300 border border-transparent   rounded-lg">Log
                        In</a>
                    <a href="register.html" class="rounded-lg text-white bg-primary hover:bg-primaryDark px-5 py-2.5">
                        Register
                    </a>
                </div>
                <div class="flex lg:hidden"><button id="mobileMenuBtn" type="button"
                        class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"><span
                            class="sr-only">Open main menu</span><svg class="w-6 h-6" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
                        </svg></button></div>
            </div>
        </nav>
        <div class="hidden lg:hidden" id="mobileMenu" role="dialog" aria-modal="true">
            <div class="fixed inset-0 z-10 hidden bg-white opacity-50"></div>
            <div
                class="fixed inset-y-0 right-0 z-20 w-full p-4 overflow-y-auto bg-white md:px-6 md:py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
                <div class="flex items-center justify-between pt-1">
                    <a href="#" class="">
                        <span class="sr-only">Your
                            Company</span>
                        <img alt="" loading="lazy" width="194" height="50" decoding="async" class="w-auto h-9"
                            style="color:transparent" src="images/logo.png">
                    </a>
                    <button type="button" id="mobileMenuBtn2" class=" -m-2.5 rounded-md p-2.5 text-gray-700"><span
                            class="sr-only">Close
                            menu</span><svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                        </svg></button>
                </div>
                <div class="flow-root mt-6">
                    <div class="-my-6 divide-y divide-gray-500/10">
                        <div class="py-6 space-y-1">
                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="/">Home</a>
                            <a href="aboutus.html"
                                class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100">About
                                Us</a>
                            <a href="locations.html"
                                class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100">Locations/Hours</a>
                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="FAQ.html">FAQ</a>
                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="tips.html">DMV Tips</a>


                            <a class="block px-3 py-2 -mx-3 text-base font-semibold leading-7 text-gray-900 rounded-lg hover:bg-gray-100"
                                href="contactUs.html">Contact</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <session class="">
        <div class="md:pt-40 pt-24 pb-8 flex justify-center p-2 md:px-0">
            <form
                class="bg-white max-w-2xl w-full flex flex-col gap-3 md:gap-4 rounded-3xl p-6 md:p-10 border border-gray-200">

                <div>
                    <h1 class="text-black font-bold text-center mb-2 text-xl md:text-2xl">Login to your account</h1>
                    <h2 class="text-gray-600 text-sm md:text-base text-center">Welcome back! Please enter your details.
                    </h2>
                </div>

                <input value="" type="email"
                    class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                    placeholder="Your Email" />

                <input value="" type="password"
                    class="w-full py-2 md:py-2.5 border focus:ring-1 focus:ring-gray-300 outline-none border-gray-300 rounded-lg text-sm md:text-base text-gray-900 px-3"
                    placeholder="Password" />

                <div class="flex items-center justify-between ">
                    <div class="flex items-center">


                        <label for="remember" class="mt-1 custom-checkbox">
                            <input type="checkbox" id="remember" @bind-Value="Input.RememberMe">
                            </InputCheckbox> <span class="checkmark"></span>

                        </label>

                        <div class="ml-2 mb-0.5 text-sm">
                            <label for="remember" class="text-gray-500">Remember for 30 days</label>
                        </div>
                    </div>

                    <a href="forgot.html"
                        class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4 ">Forgot
                        password</a>
                </div>

                <button
                    class="w-full py-2 md:py-2.5 rounded-lg bg-primary hover:bg-primaryDark text-white text-sm md:text-base font-semibold"
                    type="submit">
                    Login
                </button>

                <div class="flex justify-center gap-1">
                    <span class="text-sm text-gray-600">Don’t have an account?</span>
                    <a href="register.html"
                        class="text-primary hover:text-primaryDark text-sm font-semibold hover:underline underline-offset-4 ">Sign
                        up</a>
                </div>

            </form>
        </div>
    </session>




</body>
<script>
    const Button = document.getElementById('mobileMenuBtn');
    const menu = document.getElementById('mobileMenu');
    const Button2 = document.getElementById('mobileMenuBtn2');

    Button.addEventListener('click', () => {
        menu.classList.toggle('hidden');
    });

    Button2.addEventListener('click', () => {
        menu.classList.toggle('hidden');
    });


    window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 80) {
            header.classList.add('!bg-white', 'shadow-xl', "fixed");
            header.classList.remove('absolute');
        } else {
            header.classList.remove('!bg-white', 'shadow-xl', 'fixed');
            header.classList.add('absolute');
        }
    });

</script>

</html>