using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PremierAutoTag.ServiceContracts.Services;
using System.Security.Claims;

namespace PremierAutoTag.Server.DataServices.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class StripeController : ControllerBase
    {
        private readonly IStripePaymentService _stripePaymentService;
        private readonly ILogger<StripeController> _logger;

        public StripeController(
            IStripePaymentService stripePaymentService,
            ILogger<StripeController> logger)
        {
            _stripePaymentService = stripePaymentService;
            _logger = logger;
        }

        [HttpPost("create-checkout-session")]
        [Authorize]
        public async Task<IActionResult> CreateCheckoutSession([FromBody] CreateCheckoutSessionDto request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;

                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(userEmail))
                {
                    return BadRequest("User information not found");
                }

                var checkoutRequest = new CreateCheckoutSessionRequest
                {
                    SubscriptionPlanId = request.SubscriptionPlanId,
                    UserId = userId,
                    UserEmail = userEmail,
                    SuccessUrl = request.SuccessUrl,
                    CancelUrl = request.CancelUrl,
                    Metadata = request.Metadata
                };

                var checkoutUrl = await _stripePaymentService.CreateCheckoutSessionAsync(checkoutRequest);

                return Ok(new { checkoutUrl });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating checkout session for user {UserId}", User.Identity?.Name);
                return StatusCode(500, "An error occurred while creating the checkout session");
            }
        }

        [HttpGet("payment-success")]
        public async Task<IActionResult> PaymentSuccess([FromQuery] string session_id)
        {
            try
            {
                if (string.IsNullOrEmpty(session_id))
                {
                    return BadRequest("Session ID is required");
                }

                var success = await _stripePaymentService.ProcessPaymentSuccessAsync(session_id);

                if (success)
                {
                    return Redirect("/dashboard?payment=success");
                }
                else
                {
                    return Redirect("/dashboard?payment=error");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment success for session {SessionId}", session_id);
                return Redirect("/dashboard?payment=error");
            }
        }

        [HttpGet("payment-cancel")]
        public IActionResult PaymentCancel()
        {
            return Redirect("/dashboard?payment=cancelled");
        }

        [HttpPost("webhook")]
        public async Task<IActionResult> StripeWebhook()
        {
            try
            {
                var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                var signature = Request.Headers["Stripe-Signature"].FirstOrDefault();

                if (string.IsNullOrEmpty(signature))
                {
                    return BadRequest("Missing Stripe signature");
                }

                var success = await _stripePaymentService.ProcessWebhookEventAsync(json, signature);

                return success ? Ok() : BadRequest();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Stripe webhook");
                return BadRequest();
            }
        }
    }

    public class CreateCheckoutSessionDto
    {
        public long SubscriptionPlanId { get; set; }
        public string SuccessUrl { get; set; } = "";
        public string CancelUrl { get; set; } = "";
        public Dictionary<string, string>? Metadata { get; set; }
    }
}
