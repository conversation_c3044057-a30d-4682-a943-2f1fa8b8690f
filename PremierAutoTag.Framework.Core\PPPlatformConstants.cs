﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;

namespace Kartoa.Framework.Core
{
    public class PPPlatformConstants
    {
        public readonly IHttpContextAccessor _contextAccessor;
        public readonly IConfiguration _configuration;
        public PPPlatformConstants(IConfiguration configuration, IHttpContextAccessor contextAccessor)
        {
            CMSURL = string.Empty;
            BlogAPIURL = string.Empty;
            CountryName = "Pakistan";
            IsMauiMobileApp = false;
            IsStatePersistanceAllowed = false;
            CountryId = 1;
            _configuration = configuration;
            WebsiteURL = _configuration.GetSection("RootUrls:WebsiteURL").Value!;
            _contextAccessor = contextAccessor;
            NativeCulture = _contextAccessor.HttpContext.Request.Cookies["culture"];
        }
        public bool IsStatePersistanceAllowed { get; set; }
        public bool IsMauiMobileApp { get; set; }
        public string NativeCulture { get; set; }
        public int CountryId { get; set; }
        public string CountryName { get; set; }
        public string CMSURL { get; set; }
        public string WebsiteURL { get; set; }
        public string BlogAPIURL { get; set; }
        public APP_TYPE APP_TYPE { get; set; }

    }
}
