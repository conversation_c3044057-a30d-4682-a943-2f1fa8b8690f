﻿using System.ComponentModel;
using System.Globalization;
using System.Reflection;
using System.Runtime;
using System.Text;
using System.Text.RegularExpressions;

namespace PremierAutoTag.Framework.Core.Extensions
{
    public static class StringExtensions
    {

        public static string GetDescription(this Enum? value)
        {
            if(value == null)
                return string.Empty;

            Type type = value.GetType();
            string? name = Enum.GetName(type, value);
            if (name != null)
            {
                FieldInfo? field = type.GetField(name);
                if (field != null)
                {
                    DescriptionAttribute? attr =
                           Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) as DescriptionAttribute;
                    if (attr != null)
                    {
                        return attr.Description;
                    }
                }
            }
            return value.ToString() ?? "";
        }

        public static string GetSizeInMemory(this long bytesize)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = Convert.ToDouble(bytesize);
            int order = 0;
            while (len >= 1024D && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return string.Format(CultureInfo.CurrentCulture, "{0:0.##} {1}", len, sizes[order]);
        }


        public static string GetInitials(this string value)
        {
            string initials = string.Empty;
            if (!string.IsNullOrEmpty(value))
            {
                initials = string.Concat(value
                                 .Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries)
                                 .Where(x => x.Length >= 1 && char.IsLetter(x[0]))
                                 .Select(x => char.ToUpper(x[0])));
                return initials.Length > 2 ? initials.Substring(0, 2) : initials;
            }
            return initials;


        }

        public static string Substitute(this string @this, string? from = null, string? until = null, StringComparison comparison = StringComparison.InvariantCulture)
        {
            var fromLength = (from ?? string.Empty).Length;
            var startIndex = !string.IsNullOrEmpty(from)
                ? @this.IndexOf(from, comparison) + fromLength
                : 0;

            if (startIndex < fromLength) { throw new ArgumentException("from: Failed to find an instance of the first anchor"); }

            var endIndex = !string.IsNullOrEmpty(until)
            ? @this.IndexOf(until, startIndex, comparison)
            : @this.Length;

            if (endIndex < 0) { throw new ArgumentException("until: Failed to find an instance of the last anchor"); }

            var subString = @this.Substring(startIndex, endIndex - startIndex);
            return subString;
        }

        public static string ExtractText(this string input, string pattern)
        {
            string tagPattern = @"</[^>]+>";

            MatchCollection tagMatch = Regex.Matches(input, tagPattern);

            if (tagMatch.Count == 0)
            {
                return input;
            }

            // Define the regular expression pattern to match text inside <span> tags.
            // example pattern <span class='line1'>(.*?)</span>
            // Use Regex.Matches to find all matches in the input string.

            MatchCollection matches = Regex.Matches(input, pattern);

            // Initialize an array to store the extracted text.
            string[] extractedText = new string[matches.Count];

            // Iterate through the matches and extract the text.
            for (int i = 0; i < matches.Count; i++)
            {
                // Extract the text content between the <span> tags.
                extractedText[i] = matches[i].Groups[1].Value;
            }

            if (extractedText.Length > 1)
            {
                return string.Join("<span>", extractedText);
            }
            return extractedText[0];
        }

        public static string RemoveSpecialCharacters(this string str)
        {
            StringBuilder sb = new StringBuilder();
            foreach (char c in str)
            {
                if ((c >= '0' && c <= '9') || (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || c == '.' || c == '_')
                {
                    sb.Append(c);
                }
            }
            return sb.ToString();
        }

        public static string Trim(this string input, string delimiter)
        {
            string result = input;
            string[] parts = input.Split(new[] { $"{delimiter}" }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length > 0)
            {
                result = parts[0].Trim();
            }
            return result;
        }

        public static string StripHTML(this string input)
        {
            return Regex.Replace(input, "<.*?>", String.Empty);
        }

        public static string Capitalize(this string input)
        {
            TextInfo textInfo = CultureInfo.CurrentCulture.TextInfo;
            return textInfo.ToTitleCase(input.ToLower());
        }


    }
}
