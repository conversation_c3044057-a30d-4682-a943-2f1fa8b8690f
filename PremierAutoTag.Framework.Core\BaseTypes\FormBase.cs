﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text.Json;

namespace PremierAutoTag.Framework.Core
{
    public abstract class FormBase<TFormModel, TFormViewModel, TKey, TService> : FrameworkBaseComponent, INotifyPropertyChanged
        where TFormModel : class, new()
        where TFormViewModel : class, INotifyPropertyChanged, new()
        where TService : IFormDataService<TFormModel, TKey>

    {
        [SupplyParameterFromQuery(Name = "id")]
        [Parameter]
        public virtual TKey? Id { get; set; }

        [Parameter]
        public object? Args { get; set; }

        [Parameter]
        public string Position { get; set; } = "";

        [Parameter]
        public bool KeepAlive { get; set; } = false;

        [Parameter]
        public new string OperationId { get; set; } = "";

        [Inject]
        protected ILogger<TService> Logger { get; set; } = null!;

        public string? ValidationError { get; set; }

        private TFormViewModel? _selectedItem;

        [SupplyParameterFromForm]
        public TFormViewModel? SelectedItem
        {
            get
            {
                return _selectedItem;
            }
            set
            {
                _selectedItem = value;
                NotifyPropertyChanged();
            }
        }

        public int OnDialogClosed { get; set; }

        public bool IsBusy { get; set; }

        public FormBase()
        {
            PropertyChanged += FormBase_PropertyChanged;
        }

        private void FormBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            try
            {
                if (e.PropertyName == nameof(Args) && Args != null)
                {
                    OnArgsSet(Args);
                }

                if (e.PropertyName == nameof(SelectedItem) && SelectedItem != null)
                {
                    OnSelectedItemCreated(SelectedItem);
                }
            }
            catch (Exception ex)
            {
                ValidationError = ex.Message;
            }
        }

        protected virtual void OnSelectedItemCreated(TFormViewModel model)
        {

        }

        protected virtual void OnArgsSet(object args)
        {

        }

        /// <summary>
        /// Called after service returns viewmodel from database
        /// </summary>
        /// <typeparam name="TFormViewMoel"></typeparam>
        /// <param name="formModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        protected virtual Task<TFormViewModel?> ConvertBusinessModelToViewModel(TFormModel formModel)
        {
            if (formModel == null)
                throw new Exception("formModel is null while cloning");

            return Task.FromResult<TFormViewModel?>(formModel.Clone<TFormViewModel>());
        }

        //protected override void OnInitialized()
        //{
        //    // this will initialize viewmodel for SSR form-binding only in case form is not submitted.
        //    if (!IsPostRequestOnServerSide())
        //        SelectedItem = new TFormViewModel();
        //}

        private bool IsPostRequestOnServerSide()
        {
            if (!OperatingSystem.IsBrowser())
            {
                var httpContextAccessor = ScopeFactory?.CreateScope().ServiceProvider.GetService<IHttpContextAccessor>();
                return httpContextAccessor?.HttpContext.Request.Method == "POST";
            }
            return false;
        }
        protected override async Task OnInitializedAsync()
        {
            try
            {
                if (SelectedItem == null || !IsPostRequestOnServerSide())
                {
                    if (Id == null || Id.ToString() == "0" || string.IsNullOrEmpty(Id.ToString()) || Id.ToString() == "00000000-0000-0000-0000-000000000000")
                    {
                        SelectedItem = await CreateSelectedItem();
                    }
                    else
                    {
                        using var scope = ScopeFactory!.CreateScope();
                        var crudService = scope.ServiceProvider.GetRequiredService<TService>();
                        var formModel = await crudService.GetItemByIdAsync(Id);
                        if (formModel == null)
                            throw new InvalidDataException("Selected Item is null after service.GetItemByIdAsync");

                        SelectedItem = await ConvertBusinessModelToViewModel(formModel);

                        if (SelectedItem == null)
                            throw new InvalidDataException("Selected Item is null after cloning SelectedItem");
                    }
                    SelectedItem.PropertyChanged += SelectedItem_PropertyChanged;
                }
                using var scp = ScopeFactory!.CreateScope();
                await LoadSelectLists(scp);
                PubSub.Hub.Default.Publish(new Tuple<string>("CloseRefreshScreenIndicator"));

            }
            catch (UnauthorizedAccessException)
            {
                NavigationManager?.NavigateTo($"/account/login");
            }
            catch (Exception ex)
            {
                ValidationError = ex.Message;
                Logger.LogInformation(ex?.Message);
            }
            IsBusy = false;
            await base.OnInitializedAsync();
        }

        public virtual void SelectedItem_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {

        }


        protected virtual Task LoadSelectLists(IServiceScope scope)
        {
            return Task.CompletedTask;
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            IsInteractiveMode = true;
            if (firstRender)
            {
                try
                {

                    if (JsRuntime is not null)
                    {
                        var timezoneOffset = await JsRuntime.InvokeAsync<string>("getCookie", "timezoneoffset");
                        if (!string.IsNullOrEmpty(timezoneOffset) && int.TryParse(timezoneOffset, out int offset))
                        {
                            TimeZoneOffset = offset;
                        }

                    }

                    if (JsRuntime != null)
                        await InitialzeJsScrips(JsRuntime);
                    await InvokeAsync(() => StateHasChanged());
                }
                catch (Exception ex)
                {
                    ValidationError = ex.Message + ex.InnerException?.Message;
                }
            }
        }

        protected virtual async Task InitialzeJsScrips(IJSRuntime jsRuntime)
        {
            await Task.CompletedTask;
        }


        /// <summary>
        /// Created SelectedItem with default values.
        /// Override this method if want to create and enforce specific rules on ComposeViewModel,
        /// </summary> 
        protected virtual Task<TFormViewModel> CreateSelectedItem()
        {
            return Task.FromResult(new TFormViewModel());
        }


        public virtual Task BeforeSaveAsync()
        {

            return Task.CompletedTask;
        }

        public virtual Task OnAfterSaveAsync(TKey key)
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Called just before saving data, convert dates to UTC
        /// </summary>
        /// <param name="formViewModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        protected virtual TFormModel ConvertViewModelToBusinessModel(TFormViewModel formViewModel)
        {
            if (formViewModel == null)
                throw new Exception();

            return formViewModel.Clone<TFormModel>();
        }

        protected async Task OnFormSubmit()
        {
            IsBusy = true;
            try
            {
                // Remove or adjust delay if not necessary.
                await Task.Delay(5);
                ValidationError = null;

                if (ScopeFactory == null)
                    throw new ArgumentNullException(nameof(ScopeFactory));

                await BeforeSaveAsync();

                using var scope = ScopeFactory.CreateScope();
                var crudService = scope.ServiceProvider.GetRequiredService<TService>();

                if (SelectedItem == null)
                    throw new InvalidOperationException("SelectedItem ViewModel is null before converting to business model");

                var businessModel = ConvertViewModelToBusinessModel(SelectedItem)
                    ?? throw new InvalidDataException("Business model is null before calling SaveAsync");

                TKey id = await crudService.SaveAsync(businessModel);

                if (id == null)
                    throw new InvalidDataException("Id is null after calling SaveAsync");

                if (!KeepAlive)
                {
                    PubSub.Hub.Default.Publish(new Tuple<string, string, dynamic>(OperationId, "Dialog Closed", id));
                    await CloseDialog();
                }

                await OnAfterSaveAsync(id);
            }
            catch (UnauthorizedAccessException)
            {
                NavigationManager?.NavigateTo($"/account/login");
            }
            catch (NavigationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                ValidationError = GetValidationError(ex);
            }
            finally
            {
                IsBusy = false;
            }
        }


        private string GetValidationError(Exception ex)
        {
            string ValidationError = string.Empty;
            if (ex.Message.StartsWith("{"))
            {
                var json = JsonSerializer.Deserialize<ErrorResult<string>>(ex.Message);
#if DEBUG
                string msg = string.Empty;
                msg = json?.Title ?? "";

                if (json?.Errors != null && json.Errors.Count > 0)
                {
                    int counter = 1;
                    msg += ", Validations : \n";
                    foreach (var error in json.Errors)
                    {
                        var key = error.Key.Replace($"[{counter}].Value", "");
                        msg += $"{key} => {error.Value?[0]?.ToString()}\n";
                        counter++;
                    }
                }

                ValidationError = msg;
#else
                    ValidationError = json.Title;
#endif

            }

            else
            {
                ValidationError = ex.Message;
            }

            return ValidationError;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        public void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

    }
    public static class Ext
    {
        public static T Clone<T>(this object model)
        {
            var json = JsonSerializer.Serialize(model);
            var result = JsonSerializer.Deserialize<T>(json);
            if (result == null)
                throw new Exception("Object clonning failed");
            return result;
        }
    }
}
